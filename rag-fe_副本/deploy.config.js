// 部署配置文件
module.exports = {
    // 主应用配置
    mainApp: {
        name: 'rag-fe',
        buildCommand: 'npm run build',
        outputDir: 'dist',
        publicPath: '/',
    },
    // 微应用配置
    microApps: [
        {
            name: 'react-doc-table',
            path: './react-doc-table',
            buildCommand: 'npm run build:prod',
            outputDir: 'dist',
            // 生产环境下的访问路径
            publicPath: '/micro-apps/react-doc-table/',
        },
    ],
    // 部署环境配置
    environments: {
        development: {
            mainAppUrl: 'http://localhost:8090',
            microApps: {
                'react-doc-table': 'http://localhost:3001',
            },
        },
        production: {
            mainAppUrl: 'https://your-domain.com',
            microApps: {
                'react-doc-table': 'https://your-domain.com/micro-apps/react-doc-table',
            },
        },
    },
};
