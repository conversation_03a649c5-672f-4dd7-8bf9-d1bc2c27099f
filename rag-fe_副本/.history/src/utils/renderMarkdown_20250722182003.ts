import { marked } from 'marked';
import DOMPurify from 'dompurify';
import hljs from 'highlight.js';
import 'highlight.js/styles/github.css'; // 选择一种样式

// 流式处理相关的类型定义
export interface DocLink {
  id: string;
  fileName: string;
  docId: string;
}

export interface StreamProcessorState {
  rawBuffer: string;
  thinkBuffer: string;
  docUrls: string[];
  docLinks: DocLink[];
}

export interface StreamProcessorResult {
  type: 'docurl' | 'think' | 'content';
  content: string;
  docLink?: DocLink;
  shouldUpdateDisplay: boolean;
}

// 配置marked
marked.setOptions({
  breaks: true,
  gfm: true,
  highlight(code: string, lang: string) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(code, { language: lang }).value;
      } catch (e) {
        console.error('代码高亮错误:', e);
      }
    }
    return hljs.highlightAuto(code).value;
  }
});

// HTML转义辅助函数
/**
 * 将 HTML 字符串中的特殊字符转义为 HTML 实体
 *
 * @param html 要转义的 HTML 字符串
 * @returns 转义后的字符串
 */
function escapeHtml(html: string): string {
  return html
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
}

// 安全渲染Markdown
export async function renderMarkdown(content: string): Promise<string> {
  const processedContent = content
    .replace(/\\n/g, '\n')
    .replace(/\\t/g, '\t')
    .replace(/\\"/g, '"')
    .replace(/\\\\/g, '\\');

  const rawHtml = await marked.parse(processedContent);
  return DOMPurify.sanitize(rawHtml);
}

// 安全渲染JSON
export function renderJson(content: string): string {
  let finalContent = content;

  // 去掉可能的Markdown代码块标记
  if (finalContent.startsWith('```')) {
    const endMarkIndex = finalContent.lastIndexOf('```');
    if (endMarkIndex > 3) {
      // 提取```和```之间的内容
      let codeContent = finalContent.substring(3, endMarkIndex);

      // 处理前缀 - 检查并移除json前缀
      if (codeContent.startsWith('json\n')) {
        codeContent = codeContent.substring('json\n'.length);
      } else if (codeContent.startsWith('json')) {
        codeContent = codeContent.substring('json'.length);
      }

      finalContent = codeContent;
    }
  }

  try {
    const parsableJSON = JSON.parse(`"${finalContent}"`);
    const jsonObj = JSON.parse(parsableJSON);
    finalContent = JSON.stringify(jsonObj, null, 2);
    finalContent = finalContent.replace(/\\n/g, '\n');
  } catch (e) {
    console.log('无法解析为JSON，将显示原始数据', e);
    finalContent = finalContent
      .replace(/\\"/g, '"')
      .replace(/\\n/g, '\n')
      .replace(/\\\\/g, '\\');
  }

  return `<pre>${finalContent}</pre>`;
}

// 流式数据处理函数
export function processStreamContent(content: string, state: StreamProcessorState): StreamProcessorResult {
  // 处理特殊的docurl前缀
  if (content.startsWith('docurl:')) {
    const docInfo = content.substring(7); // 移除"docurl:"前缀
    const parts = docInfo.split('-');
    if (parts.length >= 3) {
      const id = parts[0];
      const docId = parts[1];
      const fileName = parts.slice(2).join('-').replace(/\\n/g, '').trim();

      const docLink: DocLink = { id, fileName, docId };
      state.docLinks.push(docLink);
      state.docUrls.push(content);

      return {
        type: 'docurl',
        content: content,
        docLink,
        shouldUpdateDisplay: true
      };
    }

    state.docUrls.push(content);
    return {
      type: 'docurl',
      content: content,
      shouldUpdateDisplay: false
    };
  }

  // 处理思考过程
  if (content.startsWith('think:')) {
    const thinkContent = content.substring(6); // 移除"think:"前缀
    state.thinkBuffer += thinkContent;

    return {
      type: 'think',
      content: thinkContent,
      shouldUpdateDisplay: true
    };
  }

  // 处理普通内容
  state.rawBuffer += content;
  return {
    type: 'content',
    content: content,
    shouldUpdateDisplay: true
  };
}

// 创建参考文档HTML
export function createDocumentsHTML(docLinks: DocLink[]): string {
  if (docLinks.length === 0) return '';

  const docsContainer = document.createElement('div');
  docsContainer.className = 'reference-docs-container';
  docsContainer.id = 'docs-container';

  const header = document.createElement('div');
  header.className = 'reference-docs-header';

  const titleSpan = document.createElement('span');
  titleSpan.className = 'reference-docs-title';
  titleSpan.textContent = '参考文档 ';

  const arrowSpan = document.createElement('span');
  arrowSpan.className = 'custom-arrow arrow-down'; // 默认展开

  titleSpan.appendChild(arrowSpan);
  header.appendChild(titleSpan);

  const content = document.createElement('div');
  content.className = 'reference-docs-content';
  content.style.display = 'flex'; // 默认展开
  content.style.flexDirection = 'column';

  // 创建文档链接列表
  docLinks.forEach(docLink => {
    const linkItem = document.createElement('div');
    linkItem.className = 'doc-link-item';

    const link = document.createElement('a');
    const linkUrl = `${window.location.origin}/#/knowledge/docs?id=${docLink.id}&docId=${docLink.docId}`;
    link.href = linkUrl;
    link.target = '_blank';
    link.textContent = docLink.fileName;
    link.style.textDecoration = 'none';
    link.style.color = '#409eff';

    linkItem.appendChild(link);
    content.appendChild(linkItem);
  });

  docsContainer.appendChild(header);
  docsContainer.appendChild(content);

  return docsContainer.outerHTML;
}

// 创建思考过程HTML
export async function createThinkProcessHTML(thinkBuffer: string): Promise<string> {
  if (!thinkBuffer.trim()) return '';

  const thinkContainer = document.createElement('div');
  thinkContainer.className = 'think-process-container';
  thinkContainer.id = 'think-container';

  const header = document.createElement('div');
  header.className = 'think-process-header';

  const titleSpan = document.createElement('span');
  titleSpan.className = 'think-process-title';
  titleSpan.textContent = '思考和行动过程 ';

  const arrowSpan = document.createElement('span');
  arrowSpan.className = 'custom-arrow arrow-down'; // 默认展开

  titleSpan.appendChild(arrowSpan);
  header.appendChild(titleSpan);

  const content = document.createElement('div');
  content.className = 'think-process-content';
  content.style.display = 'flex'; // 默认展开

  // 渲染思考过程的 Markdown 内容
  const renderedThinkContent = await renderMarkdown(thinkBuffer.replace(/\\"/g, '"').replace(/\\n/g, '\n'));
  content.innerHTML = renderedThinkContent; // bca-disable-line

  thinkContainer.appendChild(header);
  thinkContainer.appendChild(content);

  return thinkContainer.outerHTML;
}

// 综合渲染函数 - 将参考文档、思考过程和正常内容组合
export async function renderCompleteContent(state: StreamProcessorState): Promise<string> {
  // 渲染Markdown内容
  const renderedContent = await renderMarkdown(state.rawBuffer.replace(/\\"/g, '"').replace(/\\n/g, '\n'));

  let finalContent = '';

  // 如果有参考文档，将其嵌入到消息内容的最顶部
  if (state.docLinks.length > 0) {
    finalContent += createDocumentsHTML(state.docLinks);
  }

  // 如果有思考过程，将其嵌入到参考文档之后
  if (state.thinkBuffer.trim()) {
    finalContent += await createThinkProcessHTML(state.thinkBuffer);
  }

  // 将最终内容设置为参考文档 + 思考过程 + 正常内容
  return finalContent + renderedContent;
}

// 设置文档折叠/展开事件监听器
export function setupDocumentListeners(): void {
  // 为所有参考文档标题和思考过程标题添加点击事件
  document.querySelectorAll('.reference-docs-header, .think-process-header').forEach(header => {
    // 使用自定义数据属性而不是非标准属性
    if (!header.getAttribute('data-has-click')) {  // 防止重复添加
      header.setAttribute('data-has-click', 'true');
      header.addEventListener('click', function (this: HTMLElement) {
        const content = this.nextElementSibling as HTMLElement;
        const arrow = this.querySelector('.custom-arrow');
        if (content.style.display === 'none') {
          content.style.display = 'flex';
          if (arrow) arrow.classList.remove('arrow-right');
          if (arrow) arrow.classList.add('arrow-down');
        } else {
          content.style.display = 'none';
          if (arrow) arrow.classList.remove('arrow-down');
          if (arrow) arrow.classList.add('arrow-right');
        }
      });
    }
  });
}

// 创建初始状态
export function createStreamProcessorState(): StreamProcessorState {
  return {
    rawBuffer: '',
    thinkBuffer: '',
    docUrls: [],
    docLinks: []
  };
}

// 重置状态
export function resetStreamProcessorState(state: StreamProcessorState): void {
  state.rawBuffer = '';
  state.thinkBuffer = '';
  state.docUrls = [];
  state.docLinks = [];
}