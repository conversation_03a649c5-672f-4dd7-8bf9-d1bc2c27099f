<template>
	<div class="content-home-page">
		<ChatContainer :conversations="conversations" :currentQuestion="currentQuestion" :currentAnswer="currentAnswer">
			<template #empty>
				<el-row style="min-height: 200px">
					<el-col>
						<indexAction />
					</el-col>
				</el-row>
			</template>
		</ChatContainer>

		<ChatInput v-model="prompt" :isLoading="isLoading" :options="options" @submit="startChat" />
	</div>
</template>

<script lang="ts">
import { defineComponent, ref, watch, nextTick } from 'vue';
import ChatContainer from '@/views/index/ChatContainer.vue';
import ChatInput from '@/views/index/ChatInput.vue';
import IndexAction from '@/views/index/IndexAction.vue';
import type { queryAgentParam, queryAgentResponseData } from '@/types/AgentTypes';
import Agent<PERSON><PERSON> from '@/api/AgentApi';
import { renderMarkdown, renderJson } from '@/utils/renderMarkdown';
import { ElMessage } from 'element-plus';

export default defineComponent({
	name: 'ChatView',
	components: { ChatContainer, ChatInput, IndexAction },
	data() {
		return {
			prompt: '',
			isLoading: false,
			currentQuestion: '',
			currentAnswer: '',
			conversations: [] as Array<{ question: string; answer: string; docLinks?: Array<{ id: string; fileName: string }> }>,
			options: [] as Array<{ value: string; label: string }>,
			selectedValue: '',
			docUrls: [] as string[], // 存储docurl列表
			docLinks: [] as Array<{ id: string; fileName: string; docId: string }>,
			rawBuffer: '', // 添加rawBuffer到data中
			thinkBuffer: '', // 添加思考过程缓存
		};
	},
	async created() {
		this.getAgentData();
	},
	methods: {
		getAgentData() {
			const data: queryAgentParam = {
				type: 0,
				keyword: '',
				username: '',
			};
			AgentApi.getAgent(data).then((res) => {
				if (res.code === 200) {
					this.options = res.data.map((agent: queryAgentResponseData) => ({
						value: agent.id,
						label: agent.name,
					}));
				}
			});
		},
		async startChat({ prompt, agentId }: { prompt: string; agentId: string }) {
			this.isLoading = true;
			this.currentQuestion = prompt;
			this.currentAnswer = '';
			this.prompt = '';
			this.docUrls = []; // 清空docurl列表
			this.docLinks = []; // 清空文档链接列表
			this.rawBuffer = ''; // 清空rawBuffer
			this.thinkBuffer = ''; // 清空思考过程缓存

			try {
				const response = await fetch('/rag/api/agent/stream?stream=true', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						query: this.currentQuestion,
						agentId: agentId,
					}),
				});

				if (!response.ok) throw new Error('Network response was not ok');

				const reader = response.body?.getReader();
				const decoder = new TextDecoder('utf-8');

				if (reader) {
					let tempBuffer = '';
					while (true) {
						const { value, done } = await reader.read();
						if (done) break;

						const chunk = decoder.decode(value, { stream: true });
						tempBuffer += chunk;
						const lines = tempBuffer.split('\n');
						tempBuffer = lines.pop() || '';

						for (const line of lines) {
							if (line.startsWith(':')) {
								
							}
							if (line.startsWith('data:"')) {
								const content = line.substring(6, line.length - 1);

								// 处理特殊的docurl前缀
								if (content.startsWith('docurl:')) {
									const docInfo = content.substring(7); // 移除"docurl:"前缀
									const parts = docInfo.split('-');
									if (parts.length >= 3) {
										const id = parts[0];
										const docId = parts[1];
										const fileName = parts.slice(2).join('-').replace(/\\n/g, '').trim();
										// 保存文档链接信息
										this.docLinks.push({ id, fileName, docId });

										// 实时更新显示，将参考文档放在当前内容顶部
										await this.updateDocumentsDisplay();
									}

									this.docUrls.push(content); // 仍然保存原始docurl以备后用
									continue; // 跳过添加到显示内容
								}

								// 处理思考过程
								if (content.startsWith('think:')) {
									const thinkContent = content.substring(6); // 移除"think:"前缀
									this.thinkBuffer += thinkContent;
									
									// 实时更新显示
									await this.updateDocumentsDisplay();
									continue; // 跳过添加到显示内容
								}

								// 正常内容直接添加
								this.rawBuffer += content;

								// 实时更新内容
								await this.updateDocumentsDisplay();
							}
						}
					}

					// 处理最后一行数据
					if (tempBuffer.startsWith('data:"')) {
						const content = tempBuffer.substring(6, tempBuffer.length - 1);

						if (content.startsWith('docurl:')) {
							const docInfo = content.substring(7); // 移除"docurl:"前缀
							const parts = docInfo.split('-');
							if (parts.length >= 3) {
								const id = parts[0];
								const docId = parts[1];
								const fileName = parts.slice(2).join('-').replace(/\\n/g, '').trim();
								// 保存文档链接信息
								this.docLinks.push({ id, fileName, docId });

								// 实时更新显示，将参考文档放在当前内容顶部
								await this.updateDocumentsDisplay();
							}
							this.docUrls.push(content);
						} else if (content.startsWith('think:')) {
							const thinkContent = content.substring(6); // 移除"think:"前缀
							this.thinkBuffer += thinkContent;
							await this.updateDocumentsDisplay();
						} else {
							this.rawBuffer += content;
							await this.updateDocumentsDisplay();
						}
					}

					// 完成处理
					await this.updateDocumentsDisplay(true);

					this.conversations.push({
						question: this.currentQuestion,
						answer: this.currentAnswer,
						docLinks: [] // 不再需要单独存储docLinks
					});

					// 清空当前问题和答案
					this.currentQuestion = '';
					this.currentAnswer = '';
					this.docLinks = []; // 清空文档链接，避免在新的对话中显示
					this.rawBuffer = '';
				}
			} catch (error) {
				console.error('Error222:', error);
				ElMessage.error('请求失败，请重试');
				this.currentAnswer = '请求失败，请重试';
			} finally {
				this.isLoading = false;
			}
		},
		// 添加处理参考文档折叠的方法
		setupDocumentListeners() {
			// 为所有参考文档标题和思考过程标题添加点击事件
			document.querySelectorAll('.reference-docs-header, .think-process-header').forEach(header => {
				// 使用自定义数据属性而不是非标准属性
				if (!header.getAttribute('data-has-click')) {  // 防止重复添加
					header.setAttribute('data-has-click', 'true');
					header.addEventListener('click', function (this: HTMLElement) {
						const content = this.nextElementSibling as HTMLElement;
						const arrow = this.querySelector('.custom-arrow');
						if (content.style.display === 'none') {
							content.style.display = 'flex';
							if (arrow) arrow.classList.remove('arrow-right');
							if (arrow) arrow.classList.add('arrow-down');
						} else {
							content.style.display = 'none';
							if (arrow) arrow.classList.remove('arrow-down');
							if (arrow) arrow.classList.add('arrow-right');
						}
					});
				}
			});
		},
		async updateDocumentsDisplay(isComplete = false) {
			// 渲染Markdown内容
			let renderedContent = await renderMarkdown(this.rawBuffer.replace(/\\"/g, '"').replace(/\\n/g, '\n'));

			let finalContent = '';

			// 如果有参考文档，将其嵌入到消息内容的最顶部
			if (this.docLinks.length > 0) {
				// 创建参考文档HTML
				const docsContent = document.createElement('div');
				docsContent.className = 'reference-docs-container';
				docsContent.id = 'docs-container';

				const header = document.createElement('div');
				header.className = 'reference-docs-header';

				const titleSpan = document.createElement('span');
				titleSpan.className = 'reference-docs-title';
				titleSpan.textContent = '参考文档 ';

				const arrowSpan = document.createElement('span');
				arrowSpan.className = 'custom-arrow arrow-down';

				titleSpan.appendChild(arrowSpan);
				header.appendChild(titleSpan);

				const content = document.createElement('div');
				content.className = 'reference-docs-content';

				this.docLinks.forEach((doc, index) => {
					const docItem = document.createElement('div');
					docItem.className = 'doc-link-item';

					const indexSpan = document.createElement('span');
					indexSpan.textContent = `文档 ${index + 1}： `;

					const linkElement = document.createElement('a');
					linkElement.href = `${window.location.origin}/#/knowledge/docs?id=${doc.id}&docId=${doc.docId}`;
					linkElement.target = '_blank';
					linkElement.textContent = doc.fileName;

					docItem.appendChild(indexSpan);
					docItem.appendChild(linkElement);
					content.appendChild(docItem);
				});

				// 添加到容器
				docsContent.appendChild(header);
				docsContent.appendChild(content);

				finalContent += docsContent.outerHTML;
			}

			// 如果有思考过程，将其嵌入到参考文档之后
			if (this.thinkBuffer.trim()) {
				// 创建思考过程HTML
				const thinkContent = document.createElement('div');
				thinkContent.className = 'think-process-container';
				thinkContent.id = 'think-container';

				const header = document.createElement('div');
				header.className = 'think-process-header';

				const titleSpan = document.createElement('span');
				titleSpan.className = 'think-process-title';
				titleSpan.textContent = '思考和行动过程 ';

				const arrowSpan = document.createElement('span');
				arrowSpan.className = 'custom-arrow arrow-down'; // 默认展开

				titleSpan.appendChild(arrowSpan);
				header.appendChild(titleSpan);

				const content = document.createElement('div');
				content.className = 'think-process-content';
				content.style.display = 'flex'; // 默认展开
				
				// 渲染思考过程的 Markdown 内容
				const renderedThinkContent = await renderMarkdown(this.thinkBuffer.replace(/\\"/g, '"').replace(/\\n/g, '\n'));
				content.innerHTML = renderedThinkContent; // bca-disable-line

				// 添加到容器
				thinkContent.appendChild(header);
				thinkContent.appendChild(content);

				finalContent += thinkContent.outerHTML;
			}

			// 将最终内容设置为参考文档 + 思考过程 + 正常内容
			this.currentAnswer = finalContent + renderedContent;

			// 如果是最终完成，不需要再次设置事件监听
			if (!isComplete) {
				await nextTick();
				this.setupDocumentListeners(); // 设置文档折叠/展开的事件处理
			}
		}
	},
	mounted() {
		// 监听渲染完成事件
		this.$nextTick(() => {
			// 初始化时添加事件监听器
			this.setupDocumentListeners();

			// 使用MutationObserver监听DOM变化，当新的消息添加时设置监听器
			const observer = new MutationObserver(() => {
				this.setupDocumentListeners();
			});

			// 监视聊天容器的变化
			const chatContainer = document.querySelector('.total-css');
			if (chatContainer) {
				observer.observe(chatContainer, {
					childList: true,
					subtree: true
				});
			}
		});
	}
});
</script>

<style scoped>
.content-home-page {
	padding: 10px 5%;
	border-radius: 6px;
	box-sizing: border-box;
	background-color: #f3f6f9;
	background-image: url(https://agi-dev-platform-web.cdn.bcebos.com/ai_apaas/dist/img/bg_3363fe4c.png);
	background-repeat: no-repeat;
	background-size: 100% 100%;
	background-position: center center;
	position: relative;
	padding-bottom: 100px;
	height: calc(100vh - 60px);
	box-sizing: border-box;
}

/* 由于使用v-html渲染，需要使用深度选择器 */
:deep(.reference-docs-container) {
	margin-bottom: 16px;
	border-bottom: 1px solid rgba(0, 0, 0, 0.06);
	padding-bottom: 8px;
}

:deep(.reference-docs-header) {
	display: flex;
	align-items: center;
	cursor: pointer;
	user-select: none;
	margin-bottom: 8px;
	padding: 5px 0;
}

:deep(.reference-docs-title) {
	font-weight: bold;
	color: #666;
	font-size: 14px;
	display: flex;
	align-items: center;
}

:deep(.custom-arrow) {
	font-size: 12px;
	color: #909399;
	margin-left: 5px;
	display: inline-block;
	transition: transform 0.3s ease;
	position: relative;
}

:deep(.arrow-down) {
	transform: rotate(0deg);
}

:deep(.arrow-right) {
	transform: rotate(-90deg);
}

:deep(.custom-arrow)::before {
	content: '';
	display: inline-block;
	width: 0;
	height: 0;
	border-left: 5px solid transparent;
	border-right: 5px solid transparent;
	border-top: 6px solid #909399;
}

:deep(.reference-docs-content) {
	display: flex;
	flex-direction: column;
	gap: 6px;
	background-color: #f5f5f5;
	padding: 10px;
	border-radius: 4px;
}

:deep(.doc-link-item) {
	margin: 2px 0;
	font-size: 13px;
}

:deep(.doc-link-item a) {
	color: #409eff;
	text-decoration: none;
}

:deep(.doc-link-item a:hover) {
	text-decoration: underline;
	color: #1989fa;
}

:deep(.think-process-content) {
	background-color: #f5f5f5;
	padding: 12px;
	border-radius: 4px;
	/* border-left: 4px solid #409eff; */
	/* font-size: 13px; */
	/* line-height: 1.4; */
	/* white-space: pre-wrap; */
	color: #555;
	/* display: flex; */
	flex-direction: column;
}

:deep(.think-process-header) {
	display: flex;
	align-items: center;
	cursor: pointer;
	user-select: none;
	margin-bottom: 8px;
	padding: 5px 0;
}

:deep(.think-process-title) {
	font-weight: bold;
	color: #666;
	font-size: 14px;
	display: flex;
	align-items: center;
}
</style>
