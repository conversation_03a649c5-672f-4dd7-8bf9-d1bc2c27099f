<template>
	<el-row class="upper-row" style="height: 10%">
		<el-col :span="24">
			<el-row style="background-color: white; margin-top: 5px">
				<el-col
					:span="3"
					style="display: flex; align-items: center; justify-content: flex-start; padding: 10px"
				>
					<span style="font-size: 16px">我的Agent应用</span>
				</el-col>
				<el-col :span="17"></el-col>
				<el-col :span="3" style="display: flex; justify-content: flex-end; padding: 10px">
					<el-button
						v-if="isLoadingFaBu"
						type="primary"
						style="width: 200px; height: 35px; padding: 5px"
						@click="saveAgent"
					>
						发布
					</el-button>
					<!-- 当 isLoading 为 true 时显示加载中的图标 -->
					<el-button
						v-if="isLoadingUpdate"
						type="primary"
						style="width: 200px; height: 35px; padding: 5px"
						@click="updateAgent"
					>
						更新
					</el-button>
				</el-col>
				<el-col :span="1"></el-col>
			</el-row>
		</el-col>
	</el-row>
	<el-row class="lower-row" v-loading="isLoading">
		<el-col :span="16" style="border-right: 0.5px solid #cccccc; display: flex; flex-direction: column">
			<el-row>
				<el-col>
					<div
						style="
							height: 50px;
							background-color: white;
							display: flex;
							align-items: center;
							padding-left: 10px;
						"
					>
						<span style="font-size: 14px">应用配置</span>
					</div>
				</el-col>
			</el-row>
			<el-row style="height: 100%">
				<el-col :span="12" style="border-right: 0.5px solid #cccccc">
					<div class="info">
						<el-row>
							<el-col>
								<div class="firstTitle">
									<el-icon><UserFilled /></el-icon>
									<span> 应用设定 </span>
								</div>
							</el-col>
						</el-row>

						<el-row>
							<el-col>
								<div style="padding: 10px" class="littleTitle">
									<el-icon><ArrowDownBold /></el-icon>
									<span style="font-size: 14px">基本信息</span>
								</div>
							</el-col>
						</el-row>

						<el-row>
							<el-col>
								<div style="padding: 10px">
									<el-row>
										<el-col :span="8">
											<el-upload
												class="avatar-uploader"
												:action="baseURL"
												:show-file-list="false"
												:on-success="handleAvatarSuccess"
												:before-upload="beforeAvatarUpload"
											>
												<img v-if="imageUrl" :src="imageUrl" class="avatar" />
												<el-icon
													v-else
													class="avatar-uploader-icon"
													style="border: 1px solid #909399"
													><Plus
												/></el-icon>
											</el-upload>
										</el-col>

										<el-col :span="16">
											<div>
												<el-input
													v-model="agentName"
													style="width: 240px"
													placeholder="我的应用名"
												/>
												<el-input
													v-model="description"
													:rows="2"
													style="width: 240px; margin-top: 10px"
													type="textarea"
													placeholder="应用描述"
												/>
											</div>
										</el-col>
									</el-row>
									<el-row>
										<el-col>
											<el-tag type="success" style="margin-top: 10px"
												>图片的长宽比为360*140为最优，其他的会被拉伸哦</el-tag
											>
										</el-col>
									</el-row>
									<el-row style="margin-top: 10px">
										<el-col :span="10">
											<div style="text-align: right; padding-right: 12px">
												工作组:
											</div>
										</el-col>
										<el-col :span="14">
											<el-input v-model="groupId" placeholder="输入工作组"></el-input>
										</el-col>
									</el-row>
								</div>
							</el-col>
						</el-row>

						<el-divider style="margin-top: 10px; margin-bottom: 0px"></el-divider>

						<el-row>
							<el-col>
								<div>
									<el-row>
										<el-col :span="8">
											<div class="littleTitle">
												<el-icon><ArrowDownBold /></el-icon>
												<span>模型选择</span>
											</div>
										</el-col>
										<el-col
											:span="8"
											style="display: flex; justify-content: flex-end; margin-top: 10px"
										>
											<el-button
												type="primary"
												text="text"
												:icon="Edit"
												@click="dialogRulePromptVisible = true"
												>用户指令</el-button
											>
										</el-col>
										<el-col
											:span="8"
											style="display: flex; justify-content: flex-end; margin-top: 10px"
										>
											<el-button
												type="primary"
												text="text"
												:icon="Edit"
												@click="dialogPromptVisible = true"
												>应用回复</el-button
											>
										</el-col>
									</el-row>
									<el-row justify="end">
										<el-col :span="6">
											<div
												style="
													height: 50px;
													background-color: white;
													display: flex;
													align-items: center;
													padding-left: 10px;
												"
											>
												<span style="font-size: 14px; color: rgb(144, 147, 153)">生成模型</span>
											</div>
										</el-col>
										<el-col :span="16" :offset="2">
											<el-select
												v-model="modelId"
												placeholder="选择模型"
												size="large"
												style="width: 240px"
											>
												<el-option
													v-for="item in modelOptions"
													:key="item.id"
													:label="`${item.platform}-${item.name}`"
													:value="item.id"
												/>
											</el-select>
										</el-col>
									</el-row>
									<el-row>
										<el-col>
											<el-dialog v-model="dialogRulePromptVisible" title="用户指令设置">
												<v-md-editor v-model="rulePrompt" height="400px"></v-md-editor>
											</el-dialog>
											<el-dialog v-model="dialogPromptVisible" title="应用回复设置">
												<v-md-editor v-model="responsePrompt" height="400px"></v-md-editor>
											</el-dialog>
										</el-col>
									</el-row>
								</div>
							</el-col>
						</el-row>
					</div>
				</el-col>

				<el-col :span="12">
					<div class="info">
						<el-row>
							<el-col>
								<div class="firstTitle">
									<el-icon><Tools /></el-icon>
									<span> 能力拓展 </span>
								</div>
							</el-col>
						</el-row>

						<el-row>
							<el-col>
								<el-row>
									<el-col :span="8">
										<div class="littleTitle" style="padding: 10px">
											<el-icon><ArrowDownBold /></el-icon>
											<span>知识</span>
										</div>
									</el-col>
									<el-col
										:span="16"
										style="display: flex; justify-content: flex-end; margin-top: 10px"
									>
										<el-button
											type="primary"
											text="text"
											:icon="Edit"
											@click="dialogTableVisible = true"
											>关联</el-button
										>
									</el-col>
								</el-row>

								<el-row>
									<el-col>
										<div class="slider-demo-block">
											<div>
												<span class="demonstration">召回数</span>
												<el-input-number
													v-model="recallCount"
													:min="1"
													:max="10"
													size="small"
													style="margin-left: 20px"
												/>
											</div>
											<el-tag type="primary">
												召回切片的字符总数不应超过所选模型上下文长度
											</el-tag>
										</div>
										<div class="slider-demo-block">
											<div>
												<span class="demonstration">相似度</span>
												<el-input-number
													v-model="similarity"
													:min="0.1"
													:max="1"
													:step="0.1"
													size="small"
													style="margin-left: 20px"
												/>
											</div>
											<el-tag type="primary"> 需要调整此匹配分阈值才能过滤得到最相关答案 </el-tag>
										</div>
										<el-table :data="selectedRows" style="width: 100%" empty-text="暂无关联知识库">
											<el-table-column width="40px">
												<el-icon><CopyDocument /></el-icon>
											</el-table-column>
											<el-table-column property="name" />
										</el-table>
									</el-col>
								</el-row>
							</el-col>
						</el-row>
					</div>
				</el-col>
			</el-row>
		</el-col>

		<el-dialog 
			v-model="dialogTableVisible" 
			title="知识库关联"
			:before-close="handleDialogClose"
		>
			<template v-if="groupId && groupId !== 0">
				<AgentTable @updateValue="handleSelectedData" :groupId="groupId"></AgentTable>
			</template>
			<template v-else>
				<el-alert
					title="请先选择工作组"
					type="warning"
					:closable="false"
					show-icon
				>
					<template #default>
						必须先选择有效的工作组，才能关联知识库
					</template>
				</el-alert>
			</template>
		</el-dialog>

		<el-col :span="8" style="display: flex; flex-direction: column">
			<el-row justify="start">
				<el-col :span="8">
					<span style="height: 50px; display: flex; align-items: center; padding-left: 10px; font-size: 14px">
						预览和调试
					</span>
				</el-col>
			</el-row>
			<!-- 底部：LLMChat -->
			<el-row style="flex-grow: 1; display: flex; align-items: flex-end">
				<el-col style="width: 100%">
					<LLMChat :agentId="agentId"></LLMChat>
				</el-col>
			</el-row>
		</el-col>
	</el-row>
</template>
<script lang="ts">
import { Edit } from '@element-plus/icons-vue';
import AgentTable from '@/views/agent/table/agentTable.vue';
import LLMChat from '@/views/agent/chat/Chat.vue';
import type { saveAgentParam, updateAgentParam } from '@/types/AgentTypes';
import agentApi from '@/api/AgentApi';
import modelApi from '@/api/ModelApi';

export default {
	components: { LLMChat, AgentTable },
	async created() {
		this.getModels();
		const id = this.$route.query.id;
		this.agentId = typeof id === 'string' ? parseInt(id, 10) || 0 : 0;
		console.log(this.agentId);

		// 确保 groupId 初始化为 0 而不是 null
		this.groupId = 0;
		if (this.agentId !== 0) {
			try {
				const res = await agentApi.getAgentById(this.agentId);
				if (res.code === 200) {
					this.isLoadingFaBu = false;
					this.isLoadingUpdate = true;
					this.similarity = res.data.similarity;
					this.recallCount = res.data.recallCount;
					this.agentName = res.data.name;
					this.description = res.data.description;
					this.imageUrl = res.data.url;
					this.modelId = res.data.modelId;
					this.knowledgeBaseIds = res.data.knowledgeBaseIds;
					this.selectedRows = res.data.knowledgeBaseList;
					this.rulePrompt = res.data.rulePrompt;
					this.responsePrompt = res.data.responsePrompt;
					this.groupId = res.data.groupId || 0;
				}
			} catch (error) {
				console.error('获取 Agent 详情失败:', error);
			}
		}

		this.baseURL = window.location.origin + '/rag/api/agent/image/upload';
	},
	methods: {
		getModels() {
			modelApi.getChatModels().then((resp) => {
				if (resp.code === 200) {
					this.modelOptions = resp.data;
				}
			});
		},
		handleAvatarSuccess(response) {
			this.imageUrl = response.data;
		},
		beforeAvatarUpload(rawFile) {
			console.log(rawFile.type);
			if (rawFile.type !== 'image/jpeg' && rawFile.type !== 'image/png') {
				ElMessage.error('Avatar picture must be JPG or png format!');
				return false;
			}
			return true;
		},
		handleSelectedData(updateValue) {
			// 接收子组件的选中数据
			this.selectedRows = updateValue;
			console.log('father is ', this.selectedRows);
			this.knowledgeBaseIds = this.selectedRows.map((item) => item.id).join(';');
		},
		checkData() {
			if (!this.agentName) {
				this.$message.error('Agent名称为空，请填写！');
				return false;
			}
			if (!this.description) {
				this.$message.error('描述为空，请填写！');
				return false;
			}
			if (!this.groupId) {
				this.$message.error('工作组为空，请选择工作组！');
				return false;
			}
			if (!this.recallCount) {
				this.$message.error('召回次数为空，请填写！');
				return false;
			}
			if (!this.similarity) {
				this.$message.error('相似度为空，请填写！');
				return false;
			}
			if (!this.modelId) {
				this.$message.error('模型ID为空，请填写！');
				return false;
			}
			return true;
		},
		updateAgent() {
			if (!this.checkData()) {
				return; // 如果数据检查失败，则退出函数
			}
			this.isLoading = true;
			const params: updateAgentParam = {
				id: this.agentId,
				name: this.agentName,
				description: this.description,
				groupId: typeof this.groupId === 'string' ? parseInt(this.groupId, 10) || 0 : this.groupId || 0,
				recallCount: this.recallCount || 1,
				similarity: this.similarity || 0.1,
				modelId: this.modelId,
				url: this.imageUrl || '',
				responsePrompt: this.responsePrompt,
				rulePrompt: this.rulePrompt,
				knowledgeBaseIds: this.knowledgeBaseIds,
			};
			agentApi.updateAgent(params).then((response) => {
				if (response.code === 200) {
					this.$message('success');
					this.isLoading = false;
				} else {
					this.isLoading = false;
				}
			});
		},
		saveAgent() {
			if (!this.checkData()) {
				return; // 如果数据检查失败，则退出函数
			}
			this.isLoadingFaBu = false;
			this.isLoading = true;
			const params: saveAgentParam = {
				name: this.agentName,
				description: this.description,
				groupId: this.groupId,
				recallCount: this.recallCount || 1,
				similarity: this.similarity || 0.1,
				modelId: this.modelId,
				url: this.imageUrl || '',
				responsePrompt: this.responsePrompt,
				rulePrompt: this.rulePrompt,
				knowledgeBaseIds: this.knowledgeBaseIds,
			};
			agentApi.saveAgent(params).then((response) => {
				if (response.code === 200) {
					this.$message('success');
					this.isLoading = false;
					this.isLoadingUpdate = true;
				} else {
					this.isLoading = false;
					this.isLoadingUpdate = false;
				}
			});
		},
	},
	data() {
		return {
			baseURL: '',
			rulePrompt: '',
			responsePrompt: '',
			agentId: 0,
			isLoadingUpdate: false,
			isLoadingFaBu: true,
			isLoading: false,
			imageUrl: '',
			knowledgeBaseIds: '',
			groupId: 0,
			description: '',
			agentName: '',
			selectedRows: [],
			modelId: '',
			dialogRulePromptVisible: false,
			dialogPromptVisible: false,
			dialogTableVisible: false,
			recallCount: 1,
			similarity: 0.1,
			Edit,
			modelOptions: [],
		};
	},
};
</script>

<style scoped>
.info {
	height: 100%;
	background-color: white;
	border-top: 0.5px solid #cccccc;
}

.slider-demo-block {
	flex-direction: column; /* 让子元素垂直排列 */
	gap: 10px; /* 设置间距 */
	display: flex;
	align-items: start;
	padding: 10px;
}
.slider-demo-block .el-slider {
}
.slider-demo-block .demonstration {
	font-size: 14px;
	color: var(--el-text-color-secondary);
	line-height: 44px;
	flex: 1;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	margin-bottom: 0;
}
.slider-demo-block .demonstration + .el-slider {
	flex: 0 0 70%;
}

.avatar-uploader .el-upload {
	border: 1px dashed var(--el-border-color);
	border-radius: 6px;
	cursor: pointer;
	position: relative;
	overflow: hidden;
	transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
	border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
	font-size: 28px;
	color: #8c939d;
	width: 100px;
	height: 100px;
	text-align: center;
}

.avatar-uploader .avatar {
	width: 100px;
	height: 100px;
	display: block;
}

.firstTitle {
	display: flex;
	align-items: center;
	justify-content: flex-start;
	height: 24px;
	padding: 16px 10px 4px;
	color: #000;
	font-size: 14px;
	font-weight: 600;
	line-height: 24px;
	gap: 8px;
}

.littleTitle {
	background-color: white;
	display: flex;
	align-items: center;
	height: 24px;
	justify-content: flex-start;
	padding: 16px 10px 4px;
	color: #000;
	font-size: 14px;
	font-weight: 600;
	line-height: 24px;
	gap: 8px;
}
.littleTitle span {
	position: relative;
	display: flex;
	align-items: center;
	color: #303540;
	font-size: 14px;
	font-weight: 500;
}

.upper-row {
	display: flex;
	flex-direction: column;
}

.lower-row {
	min-height: 580px;
	display: flex;
	align-items: stretch;
}
</style>
