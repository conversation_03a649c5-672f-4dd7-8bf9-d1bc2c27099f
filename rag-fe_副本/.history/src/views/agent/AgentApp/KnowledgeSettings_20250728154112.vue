<!-- eslint-disable -->
<template>
	<div class="knowledge-settings">
		<el-card class="settings-card">
			<template #header>
				<div class="section-header">
					<span class="group-title">知识</span>
					<el-button type="primary" text :icon="Edit" class="association-btn" @click="openAssociationDialog">
						关联知识库
					</el-button>
				</div>
			</template>

			<div class="settings-content">
				<el-row class="setting-item">
					<div class="setting-control">
						<span class="setting-label">召回数</span>
						<el-input-number :model-value="recallCount" @update:model-value="updateRecallCount" :min="1"
							:max="10" size="small" controls-position="right" />
					</div>
					<el-tag type="info" class="hint-tag">召回切片的字符总数不应超过所选模型上下文长度</el-tag>
				</el-row>

				<el-row class="setting-item">
					<div class="setting-control">
						<span class="setting-label">相似度</span>
						<el-input-number :model-value="similarity" @update:model-value="updateSimilarity" :min="0.1"
							:max="1" :step="0.1" size="small" controls-position="right" />
					</div>
					<el-tag type="info" class="hint-tag">调整匹配分阈值以过滤得到最相关答案</el-tag>
				</el-row>

				<el-row>
					<el-col>
						<el-table :data="selectedKnowledgeBases" style="width: 100%" empty-text="暂无关联知识库"
							class="knowledge-table">
							<el-table-column width="40px">
								<template #default>
									<el-icon><copy-document /></el-icon>
								</template>
							</el-table-column>
							<el-table-column prop="name" label="知识库名称" />
						</el-table>
					</el-col>
				</el-row>
			</div>
		</el-card>
	</div>
</template>

<script>
import { CopyDocument, Edit } from '@element-plus/icons-vue';
import { watch } from 'vue';

export default {
	name: 'KnowledgeSettings',
	components: { CopyDocument },
	props: {
		recallCount: {
			type: Number,
			default: 1,
		},
		similarity: {
			type: Number,
			default: 0.1,
		},
		selectedKnowledgeBases: {
			type: Array,
			default: () => [],
		},
	},
	emits: ['update:recallCount', 'update:similarity', 'openAssociationDialog'],
	setup(props, { emit }) {
		const updateRecallCount = (value) => {
			emit('update:recallCount', value);
		};

		const updateSimilarity = (value) => {
			emit('update:similarity', value);
		};

		const openAssociationDialog = () => {
			emit('openAssociationDialog');
		};

		// 添加调试信息
		console.log('KnowledgeSettings 初始化时 props.selectedKnowledgeBases:', props.selectedKnowledgeBases);

		// 监听 selectedKnowledgeBases 的变化
		watch(() => props.selectedKnowledgeBases, (newValue) => {
			console.log('KnowledgeSettings selectedKnowledgeBases 变化:', newValue);
		}, { immediate: true, deep: true });

		return {
			Edit,
			updateRecallCount,
			updateSimilarity,
			openAssociationDialog,
		};
	},
};
</script>

<style scoped lang="less">
.knowledge-settings {
	border-radius: 8px;

	.settings-card {
		padding: 10px;
		border-radius: 8px;
	}
}

.group-title {
	font-size: 14px;
	color: var(--el-text-color-secondary);
	margin: 5px;
	font-weight: 500;
}

.section-header {
	display: flex;
	align-items: center;

	.association-btn {
		margin-left: auto;
		font-size: 13px;
	}
}

.settings-content {
	display: flex;
	flex-direction: column;
	gap: 20px;
}

.setting-item {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.setting-control {
	display: flex;
	align-items: center;
	gap: 12px;
}

.setting-label {
	font-size: 14px;
	color: var(--el-text-color-regular);
	min-width: 60px;
}

.hint-tag {
	align-self: flex-start;
	font-size: 12px;
	color: var(--el-text-color-secondary);
	background-color: var(--el-fill-color-light);
	border: none;
}

.knowledge-table {
	margin-top: 10px;
	border: 1px solid var(--el-border-color-light);
	border-radius: 4px;

	:deep(.el-table__cell) {
		padding: 8px 0;
	}
}
</style>
