<!-- eslint-disable -->

<template>
	<div class="agent-app">
		<agent-header :is-loading-fa-bu="isLoadingFaBu" :is-loading-update="isLoadingUpdate" @save="saveAgent"
			@update="updateAgent" />

		<!-- 知识库关联对话框 -->
		<el-dialog v-model="dialogTableVisible" title="知识库关联" width="800px">
			<el-tabs v-model="activeKnowledgeTab" @tab-change="handleKnowledgeTabChange">
				<el-tab-pane
					v-for="tab in knowledgeTabList"
					:key="tab.name"
					:label="`${tab.name} (${tab.count})`"
					:name="tab.name"
				>
					<agent-table
						@updateValue="handleSelectedData"
						:groupId="agentData.groupId"
						:embeddingModelId="agentData.embeddingModelId"
						:tableName="tab.name"
						:key="tab.name"
					/>
				</el-tab-pane>
			</el-tabs>
		</el-dialog>

		<!-- MCP服务器关联对话框 -->
		<el-dialog v-model="mcpServerDialogVisible" title="关联MCP服务器" width="600px" destroy-on-close>
			<el-form>
				<el-form-item>
					<el-input v-model="mcpSearchKeyword" placeholder="搜索 MCP Server" clearable>
						<template #prefix>
							<el-icon>
								<Search />
							</el-icon>
						</template>
					</el-input>
				</el-form-item>
			</el-form>
			<!-- 已选服务器执行顺序展示 -->
			<div v-if="selectedServersInOrder.length > 0" class="selected-servers-display">
				<div class="selected-servers-header">
					<span>已选执行顺序</span>
				</div>
				<div class="selected-servers-list">
					<div v-for="item in sortedSelectedServers" :key="item.id" class="selected-server-tag">
						<span class="server-order-badge">{{ item.order }}</span>
						<span class="selected-server-name">{{ getMCPServerName(item.id) }}</span>
					</div>
				</div>
			</div>
			<div class="server-list-container">
				<div class="server-list-header">
					<span>可选 MCP Server （选择顺序决定执行顺序）</span>
					<span class="server-count">{{ filteredMcpServers.length }}个</span>
				</div>

				<el-scrollbar height="300px">
					<div class="server-item" v-for="server in filteredMcpServers" :key="server.id">
						<div class="server-checkbox">
							<div class="custom-order-checkbox" :class="{ 'is-selected': isServerSelected(server.id) }"
								@click="toggleServer(server.id, !isServerSelected(server.id))">
								<span v-if="isServerSelected(server.id)" class="checkbox-number">
									{{ getServerOrder(server.id) }}
								</span>
							</div>
							<div class="server-info">
								<span class="server-name">{{ server.id }} - {{ server.name }} - {{ server.description }}
								</span>
							</div>
						</div>
					</div>
				</el-scrollbar>
			</div>

			<template #footer>
				<div class="dialog-footer">
					<el-button @click="mcpServerDialogVisible = false">取消</el-button>
					<el-button type="primary" @click="confirmMCPServerSelection"
						:disabled="selectedServersInOrder.length === 0">
						确定
					</el-button>
				</div>
			</template>
		</el-dialog>

		<div class="card-container">
			<el-card class="wide-card">
				<template #header>
					<!-- <span class="section-header">应用配置</span> -->
					<span class="section-header">
						<el-icon>
							<UserFilled />
						</el-icon>
						应用设定
					</span>
				</template>
				<!-- eslint-disable-next-line  -->
				<agent-config :baseURL="baseURL" :agent-data="agentData" :model-options="modelOptions"
					:embedding-model-options="embeddingModelOptions" :dialog-visible="dialogTableVisible"
					@update:agentData="updateAgentData" @openAssociationDialog="handleOpenDialog"
					@openMCPServerDialog="handleOpenMCPServerDialog" @createSuccess="handleCreateSuccess"
					ref="agentConfigRef" />
			</el-card>
			<el-card class="narrow-card">
				<template #header>
					<span class="section-header">
						<el-icon>
							<ViewIcon />
						</el-icon>
						预览和调试
					</span>
				</template>
				<agent-preview :agent-id="agentData.id" />
			</el-card>
		</div>
	</div>
</template>

<script>
import AgentHeader from './AgentHeader.vue';
import AgentConfig from './AgentConfig.vue';
import AgentPreview from './AgentPreview.vue';
import AgentTable from './AgentTable.vue';
import { Edit, UserFilled, View as ViewIcon, Search } from '@element-plus/icons-vue';
import agentApi from '@/api/AgentApi.ts';
import modelApi from '@/api/ModelApi.ts';
import mcpServersApi from '@/api/McpServersApi.ts';

export default {
	components: {
		AgentHeader,
		AgentConfig,
		AgentPreview,
		AgentTable,
		UserFilled,
		ViewIcon,
		Search
	},
	data() {
		return {
			agentData: {
				id: 0,
				imageUrl: '', // 图片url
				name: '', // agent 名称
				description: '', // 描述
				groupId: 0, // 工作组ID
				responsePrompt: '',
				rulePrompt: '',
				rolePrompt: 'qa',
				recallCount: 1,
				similarity: 0.1,
				modelId: '',
				embeddingModelId: '',
				selectedRows: [],
				knowledgeBaseIds: '',
				mcpServerIds: '',
				selectedMcpServers: [],
				flowNodes: [], // MCP服务器节点
			},
			dialogTableVisible: false,
			mcpServerDialogVisible: false,
			isLoading: false,
			isLoadingFaBu: true,
			isLoadingUpdate: false,
			baseURL: window.location.origin + '/rag/api/agent/image/upload',
			modelOptions: [],
			embeddingModelOptions: [],
			Edit,
			// MCP服务器相关
			mcpServers: [],
			mcpSearchKeyword: '',
			selectedServersInOrder: [], // 按选中顺序存储服务器信息 [{id, order, inputLimit, outputLimit}]
			nextOrder: 1, // 下一个序号
			// 知识库Tab相关
			activeKnowledgeTab: '', // 当前激活的知识库tab
			knowledgeTabList: [], // 知识库tab列表
			allKnowledgeData: [
    {
        "createAt": "2025-03-07 11:37:58",
        "description": "知识库描述1111",
        "embeddingModelId": 3,
        "embeddingRule": "{\"delimiter\":\"\\n!?。！？\",\"chunkTokenNum\":600}",
        "groupId": 18,
        "id": 13,
        "name": "知识库测试",
        "owner": "lirui42;guanlin",
        "parseId": 1,
        "status": 1,
        "tableName": "chunks",
        "updateAt": "2025-06-11 20:14:28"
    },
    {
        "createAt": "2025-06-12 17:54:20",
        "description": "21312",
        "embeddingModelId": 3,
        "embeddingRule": "{\"delimiter\":[\"。\"],\"chunkTokenNum\":600}",
        "groupId": 18,
        "id": 57,
        "name": "213123",
        "owner": "xushixuan01",
        "parseId": 1,
        "status": 1,
        "tableName": "chunks",
        "updateAt": "2025-06-12 17:54:20"
    },
    {
        "createAt": "2025-06-17 10:23:30",
        "description": "333",
        "embeddingModelId": 3,
        "embeddingRule": "{\"delimiter\":[\"。\",\"！\"],\"chunkTokenNum\":600}",
        "groupId": 18,
        "id": 58,
        "name": "123",
        "owner": "xushixuan01",
        "parseId": 1,
        "status": 1,
        "tableName": "chunks",
        "updateAt": "2025-06-17 10:23:30"
    },
    {
        "createAt": "2025-06-17 10:25:48",
        "description": "312312",
        "embeddingModelId": 3,
        "embeddingRule": "{\"delimiter\":[\"。\"],\"groupId\":18,\"chunkTokenNum\":600}",
        "groupId": 18,
        "id": 59,
        "name": "123",
        "owner": "xushixuan01",
        "parseId": 1,
        "status": 1,
        "tableName": "chunks",
        "updateAt": "2025-06-17 10:25:48"
    },
    {
        "createAt": "2025-06-17 17:06:58",
        "description": "13",
        "embeddingModelId": 3,
        "embeddingRule": "{\"delimiter\":[\"。\",\"！\"],\"chunkTokenNum\":600}",
        "groupId": 18,
        "id": 62,
        "name": "test",
        "owner": "xushixuan01",
        "parseId": 1,
        "status": 1,
        "tableName": "chunks",
        "updateAt": "2025-06-17 17:06:58"
    },
    {
        "createAt": "2025-06-17 17:42:25",
        "description": "123",
        "embeddingModelId": 3,
        "embeddingRule": "{\"delimiter\":[\"。\"],\"chunkTokenNum\":600}",
        "groupId": 18,
        "id": 63,
        "name": "test",
        "owner": "xushixuan01",
        "parseId": 1,
        "status": 1,
        "tableName": "chunks",
        "updateAt": "2025-06-17 17:42:25"
    },
    {
        "createAt": "2025-06-17 17:44:04",
        "description": "@！3123",
        "embeddingModelId": 3,
        "embeddingRule": "{\"delimiter\":[\"。\",\"！\"],\"chunkTokenNum\":600}",
        "groupId": 18,
        "id": 64,
        "name": "？？？",
        "owner": "xushixuan01",
        "parseId": 1,
        "status": 1,
        "tableName": "chunks",
        "updateAt": "2025-06-17 17:44:04"
    },
    {
        "createAt": "2025-07-18 11:42:24",
        "description": "这个是一个很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长的描述",
        "embeddingModelId": 4,
        "embeddingRule": "{\"delimiter\":[\"。\",\"！\",\"；\",\"？\",\".\",\"!\",\";\",\"?\",\"\\n\",\"\\n\\n\"],\"chunkTokenNum\":\"599\"}",
        "groupId": 18,
        "id": 69,
        "name": "这是一个很长很长名字",
        "owner": "xushixuan01",
        "parseId": 2,
        "status": 1,
        "tableName": "chunks",
        "updateAt": "2025-07-18 11:42:24"
    },
    {
        "createAt": "2025-07-22 21:51:56",
        "description": "HAL ",
        "embeddingModelId": 3,
        "embeddingRule": "{\"delimiter\":[\"。\",\"！\",\"？\"],\"chunkTokenNum\":600}",
        "groupId": 18,
        "id": 70,
        "name": "借我试一下",
        "parseId": 1,
        "status": 1,
        "tableName": "chunks",
        "updateAt": "2025-07-22 21:51:56"
    },
    {
        "createAt": "2025-07-22 21:57:12",
        "description": "HAL ",
        "embeddingModelId": 3,
        "embeddingRule": "{\"delimiter\":[\"。\",\"！\"],\"chunkTokenNum\":\"601\"}",
        "groupId": 18,
        "id": 71,
        "name": "借我试一下",
        "owner": "xushixuan01",
        "parseId": 1,
        "status": 1,
        "tableName": "chunks",
        "updateAt": "2025-07-24 14:34:22"
    },
    {
        "createAt": "2025-07-22 21:57:39",
        "description": "HAL ",
        "embeddingModelId": 3,
        "embeddingRule": "{\"delimiter\":[\"。\",\"！\"],\"chunkTokenNum\":600}",
        "groupId": 18,
        "id": 72,
        "name": "312312",
        "owner": "xuhuibi",
        "parseId": 1,
        "status": 1,
        "tableName": "chunks",
        "updateAt": "2025-07-22 21:57:39"
    }
], // 所有知识库数据
		};
	},
	computed: {
		filteredMcpServers() {
			if (!this.mcpSearchKeyword) {
				return this.mcpServers;
			}
			const keyword = this.mcpSearchKeyword.toLowerCase();
			return this.mcpServers.filter(
				(server) => server.name.toLowerCase().includes(keyword)
			);
		},
		sortedSelectedServers() {
			// 按order排序
			return [...this.selectedServersInOrder].sort((a, b) => a.order - b.order);
		}
	},
	watch: {
		mcpServerDialogVisible(newVal) {
			if (newVal) {
				// 当对话框打开时，初始化已选服务器
				this.initSelectedMCPServers();
			}
		},
		'agentData.embeddingModelId'() {
			// 当切词模型改变时，清空已选择的知识库
			this.agentData.selectedRows = [];
			this.agentData.knowledgeBaseIds = '';
		}
	},
	created() {
		this.initData();
	},
	methods: {
		async handleOpenDialog() {
			await this.loadKnowledgeData();
			this.dialogTableVisible = true;
		},
		// 加载知识库数据并生成Tab
		async loadKnowledgeData() {
			if (!this.agentData.groupId || this.agentData.groupId === 0) {
				return;
			}

			try {
				// 这里需要导入getKDB方法
				const { getKDB } = await import('@/api/BookApi');
				const res = await getKDB(this.agentData.groupId);
				this.allKnowledgeData = res.data || [];
				this.generateKnowledgeTabs();
			} catch (error) {
				console.error('获取知识库数据失败:', error);
				this.allKnowledgeData = [];
				this.knowledgeTabList = [];
			}
		},
		// 生成知识库Tab
		generateKnowledgeTabs() {
			if (!this.allKnowledgeData || this.allKnowledgeData.length === 0) {
				this.knowledgeTabList = [];
				this.activeKnowledgeTab = '';
				return;
			}

			// 过滤数据（根据embeddingModelId）
			const filteredData = this.filterKnowledgeData(this.allKnowledgeData);

			// 按tableName分组
			const groupedData = {};
			filteredData.forEach(item => {
				const tableName = item.tableName || 'default';
				if (!groupedData[tableName]) {
					groupedData[tableName] = [];
				}
				groupedData[tableName].push(item);
			});

			// 生成tab列表
			this.knowledgeTabList = Object.keys(groupedData).map(tableName => ({
				name: tableName,
				count: groupedData[tableName].length
			}));

			// 设置默认激活的tab
			if (this.knowledgeTabList.length > 0 && !this.activeKnowledgeTab) {
				this.activeKnowledgeTab = this.knowledgeTabList[0].name;
			}

			// 如果当前激活的tab不存在于新的tab列表中，重置为第一个
			if (!this.knowledgeTabList.find(tab => tab.name === this.activeKnowledgeTab)) {
				this.activeKnowledgeTab = this.knowledgeTabList.length > 0 ? this.knowledgeTabList[0].name : '';
			}
		},
		// 过滤知识库数据
		filterKnowledgeData(data) {
			if (!data || data.length === 0) {
				return [];
			}
			if (!this.agentData.embeddingModelId) {
				return data;
			} else {
				// 根据切词模型ID过滤知识库
				const embeddingModelIdNum = typeof this.agentData.embeddingModelId === 'string'
					? parseInt(this.agentData.embeddingModelId, 10)
					: this.agentData.embeddingModelId;

				return data.filter(item =>
					item.embeddingModelId === embeddingModelIdNum
				);
			}
		},
		// 处理知识库Tab切换
		handleKnowledgeTabChange(tabName) {
			this.activeKnowledgeTab = tabName;
			// 切换tab时清空选择
			this.agentData.selectedRows = [];
			this.agentData.knowledgeBaseIds = '';
		},
		handleOpenMCPServerDialog() {
			this.fetchMCPServers();
			this.mcpServerDialogVisible = true;
		},
		initSelectedMCPServers() {
			this.selectedServersInOrder = [];
			this.nextOrder = 1;

			if (this.agentData.flowNodes && this.agentData.flowNodes.length > 0) {
				const sortedNodes = [...this.agentData.flowNodes].sort((a, b) => a.numbering - b.numbering);
				// 添加已有服务器到选择列表，保持原有序号和描述设置
				sortedNodes.forEach(node => {
					this.selectedServersInOrder.push({
						id: node.mcpServerId,
						order: this.nextOrder++,
						description: node.description || ''
					});
				});
			}
		},
		isServerSelected(serverId) {
			return this.selectedServersInOrder.some(item => item.id === serverId);
		},
		getServerOrder(serverId) {
			const found = this.selectedServersInOrder.find(item => item.id === serverId);
			return found ? found.order : '';
		},
		getServerData(serverId) {
			const found = this.selectedServersInOrder.find(item => item.id === serverId);
			if (!found) {
				return { description: '' };
			}
			// 确保description属性存在
			if (!('description' in found)) {
				found.description = '';
			}

			return found;
		},
		toggleServer(serverId, checked) {
			if (checked) {
				// 添加服务器到选择列表
				const server = this.mcpServers.find(s => s.id === serverId);
				this.selectedServersInOrder.push({
					id: serverId,
					order: this.nextOrder++,
					description: server.description || ''
				});
			} else {
				// 从选择列表中移除服务器
				const index = this.selectedServersInOrder.findIndex(item => item.id === serverId);
				if (index !== -1) {
					this.selectedServersInOrder.splice(index, 1);
					// 更新后续项的顺序号
					this.reorderSelectedServers();
				}
			}
		},
		reorderSelectedServers() {
			// 重新排序，确保序号连续
			this.selectedServersInOrder.sort((a, b) => a.order - b.order);
			// 更新序号
			this.selectedServersInOrder.forEach((item, index) => {
				item.order = index + 1;
			});

			// 更新下一个序号
			this.nextOrder = this.selectedServersInOrder.length + 1;
		},
		confirmMCPServerSelection() {
			const newFlowNodes = this.selectedServersInOrder.map((item, index) => {
				const server = this.mcpServers.find(s => s.id === item.id);

				// 检查是否是已有节点
				const existingNode = this.agentData.flowNodes ?
					this.agentData.flowNodes.find(node => node.mcpServerId === item.id) : null;

				if (existingNode) {
					// 如果是已有节点，保留其ID和其他信息，更新numbering
					return {
						...existingNode,
						numbering: index,
						description: server.description || '',
					};
				} else {
					// 如果是新节点
					return {
						mcpServerId: item.id,
						mcpServerName: server.name,
						numbering: index,
						description: server.description || '',
					};
				}
			});

			this.agentData.flowNodes = newFlowNodes;

			this.mcpServerDialogVisible = false;
		},
		handleMCPServerSelected(selectedServers) {
			// 合并已有和新选择的MCP服务器，避免重复
			const existingServerIds = this.agentData.flowNodes ?
				this.agentData.flowNodes.map(node => node.mcpServerId) : [];
			const newServers = selectedServers.filter(server => !existingServerIds.includes(server.mcpServerId));

			const updatedFlowNodes = [
				...(this.agentData.flowNodes || []),
				...newServers
			];

			this.agentData.flowNodes = updatedFlowNodes;
		},
		initData() {
			this.getModels();
			this.getEmbeddingModels();
			// this.fetchMCPServers(); // 获取MCP服务器列表
			this.baseURL = window.location.origin + '/rag/api/agent/image/upload';

			const id = this.$route.query.id;
			this.agentData.id = typeof id === 'string' ? parseInt(id, 10) || 0 : 0;

			if (this.agentData.id !== 0) {
				this.loadAgentData();
			}
		},
		async loadAgentData() {
			try {
				const res = await agentApi.getAgentById(this.agentData.id);
				if (res.code === 200) {
					this.isLoadingFaBu = false;
					this.isLoadingUpdate = true;
					// 更新agentData
					Object.assign(this.agentData, {
						similarity: res.data.similarity,
						recallCount: res.data.recallCount,
						name: res.data.name,
						description: res.data.description,
						imageUrl: res.data.url,
						modelId: res.data.modelId,
						embeddingModelId: res.data.embeddingModelId || '',
						knowledgeBaseIds: res.data.knowledgeBaseIds,
						selectedRows: res.data.knowledgeBaseList || [],
						rulePrompt: res.data.rulePrompt,
						rolePrompt: res.data.rolePrompt,
						responsePrompt: res.data.responsePrompt,
						groupId: res.data.groupId,
						flowNodes: res.data.flowNodes || [],
					});
				}
			} catch (error) {
				console.error('加载Agent数据失败:', error);
			}
		},
		async getModels() {
			try {
				const resp = await modelApi.getChatModels();
				if (resp.code === 200) {
					this.modelOptions = resp.data;
				}
			} catch (error) {
				console.error('获取模型列表失败:', error);
			}
		},
		async getEmbeddingModels() {
			try {
				const resp = await modelApi.getEmbddingModels();
				if (resp.code === 200) {
					this.embeddingModelOptions = resp.data;
				}
			} catch (error) {
				console.error('获取切词模型列表失败:', error);
			}
		},
		updateAgentData(newData) {
			this.agentData = { ...this.agentData, ...newData };
		},
		handleSelectedData(updateValue) {
			this.agentData.selectedRows = updateValue;
			this.agentData.knowledgeBaseIds = updateValue.map((item) => item.id).join(';');
		},
		checkData() {
			const requiredFields = [
				{ field: 'name', message: 'Agent名称为空，请填写！' },
				{ field: 'description', message: '描述为空，请填写！' },
				{ field: 'groupId', message: '工作组为空，请选择！' },
				{ field: 'recallCount', message: '召回次数为空，请填写！' },
				{ field: 'similarity', message: '相似度为空，请填写！' },
				{ field: 'modelId', message: '模型ID为空，请填写！' },
			];

			for (const { field, message } of requiredFields) {
				if (!this.agentData[field]) {
					this.$message.error(message);
					return false;
				}
			}
			return true;
		},
		async saveAgent() {
			if (!this.checkData()) {
				return;
			}

			if (this.$refs.agentConfigRef) {
				await this.$refs.agentConfigRef.createAgent();
			}
		},
		async updateAgent() {
			if (!this.checkData()) {
				return;
			}

			if (this.$refs.agentConfigRef) {
				await this.$refs.agentConfigRef.updateAgent();
			}
		},
		handleCreateSuccess(agentId) {
			this.isLoadingUpdate = true;
			this.isLoadingFaBu = false;
			this.agentData.id = agentId;
		},
		async fetchMCPServers() {
			try {
				const res = await mcpServersApi.getListByGroup(this.agentData.groupId);
				if (res.code === 200) {

					this.mcpServers = res.data.map(server => ({
						id: server.id,
						name: server.name,
						description: server.description || ''
					}));
				} else {
					console.error('获取MCP服务器列表失败:', res.message);
				}
			} catch (error) {
				console.error('获取MCP服务器列表异常:', error);
			}
		},
		getMCPServerName(serverId) {
			const server = this.mcpServers.find(s => s.id === serverId);
			return server ? server.name : `服务器 ${serverId}`;
		},
	},
};
</script>

<style scoped>
.agent-app {
	height: 100%;
	display: flex;
	flex-direction: column;
}

.card-container {
	display: flex;
	flex: 1;
	/* 关键：占据剩余空间 */
	min-height: 0;
	/* 关键：允许内容收缩 */
	gap: 10px;
	overflow: hidden;
	/* 防止双重滚动条 */
	max-height: calc(100vh - 130px);
}

.card-container .section-header {
	font: 14px 'PingFang SC';
	display: inline-flex;
	/* 使用 inline-flex 保持行内特性 */
	align-items: center;
	/* 垂直居中 */
	gap: 8px;
}

/* 宽卡片 */
.wide-card {
	flex: 1;
	display: flex;
	flex-direction: column;
	overflow: hidden;
}

/* 窄卡片 */
.narrow-card {
	flex: 2;
	display: flex;
	flex-direction: column;
	overflow: hidden;
}

:deep(.el-card__body) {
	padding: 0px;
	overflow: auto;
	flex: 1;
}

/* MCP服务器对话框样式 */
.server-list-container {
	margin-top: 15px;
	border: 1px solid var(--el-border-color-light);
	border-radius: 4px;
}

.server-list-header {
	display: flex;
	justify-content: space-between;
	padding: 10px 15px;
	background-color: var(--el-fill-color-light);
	border-bottom: 1px solid var(--el-border-color-light);
	color: var(--el-text-color-regular);
	font-size: 14px;
}

.server-count {
	color: var(--el-text-color-secondary);
}

.server-item {
	padding: 10px 15px;
	border-bottom: 1px solid var(--el-border-color-lighter);
}

.server-item:last-child {
	border-bottom: none;
}

.server-checkbox {
	display: flex;
	align-items: center;
	gap: 10px;
}

.custom-order-checkbox {
	width: 20px;
	height: 20px;
	border: 1px solid var(--el-border-color-darker);
	border-radius: 4px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.2s;
}

.custom-order-checkbox.is-selected {
	border-color: var(--el-color-primary);
	background-color: var(--el-color-primary);
}

.checkbox-number {
	color: white;
	font-size: 12px;
	font-weight: bold;
	line-height: 1;
}

.server-info {
	display: flex;
	align-items: center;
}

.server-name {
	font-size: 14px;
	color: var(--el-text-color-primary);
	line-height: 1.5;
}

.server-limits {
	margin-top: 10px;
	padding-left: 30px;
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.limit-item {
	display: flex;
	align-items: center;
	gap: 10px;
}

.limit-label {
	min-width: 70px;
	font-size: 13px;
	color: var(--el-text-color-regular);
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
}

/* 已选服务器执行顺序展示 */
.selected-servers-display {
	margin: 15px 0;
	border: 1px solid var(--el-border-color-light);
	border-radius: 4px;
	overflow: hidden;
}

.selected-servers-header {
	padding: 8px 15px;
	background-color: var(--el-fill-color-light);
	border-bottom: 1px solid var(--el-border-color-light);
	color: var(--el-text-color-regular);
	font-size: 14px;
	font-weight: 500;
}

.selected-servers-list {
	padding: 10px 15px;
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
}

.selected-server-tag {
	display: flex;
	align-items: center;
	padding: 5px 8px;
	border-radius: 4px;
	background-color: var(--el-fill-color);
	border: 1px solid var(--el-border-color);
}

.server-order-badge {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	background-color: var(--el-color-primary);
	color: white;
	border-radius: 50%;
	width: 18px;
	height: 18px;
	font-size: 12px;
	margin-right: 8px;
	font-weight: bold;
}

.selected-server-name {
	font-size: 13px;
	color: var(--el-text-color-primary);
}
</style>
