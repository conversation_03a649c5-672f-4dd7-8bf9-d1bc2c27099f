<template>
	<el-table @selection-change="handleSelectionChange" :data="tableData">
		<el-table-column type="selection" width="55" />
		<el-table-column property="name" label="名字" width="300" />
		<el-table-column property="description" label="描述" width="400" />
	</el-table>
</template>

<script lang="ts">
// @ts-ignore
import { getKDB } from '@/api/BookApi';

interface TableItem {
	name: string;
	description: string;
	[key: string]: any;
}


export default {
	name: 'AgentTable',
	props: {
		groupId: {
			type: Number,
			default: 0
		}
	},
	data() {
		return {
			selectedRows: [] as TableItem[],
			tableData: [] as TableItem[],
		};
	},
	created() {
		this.getTableData();
	},
	methods: {
		getTableData() {
			// const userName = sessionStorage.getItem('username')!;
			getKDB(this.groupId).then((res) => {
				this.tableData = res.data;
			});
		},
		handleSelectionChange(rows: TableItem[]) {
			this.selectedRows = rows;
			this.$emit('updateValue', this.selectedRows);
			console.log(this.selectedRows);
		},
	},
};
</script>
