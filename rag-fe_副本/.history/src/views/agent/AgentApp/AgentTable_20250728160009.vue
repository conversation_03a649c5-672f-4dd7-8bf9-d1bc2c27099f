<template>
	<div>
		<el-tabs v-model="activeTab" @tab-change="handleTabChange">
			<el-tab-pane
				v-for="tableName in tableNames"
				:key="tableName"
				:label="tableName"
				:name="tableName"
			>
				<el-table
					@selection-change="handleSelectionChange"
					:data="getTableDataByTab(tableName)"
					ref="tableRefs"
				>
					<el-table-column type="selection" width="55" />
					<el-table-column property="name" label="名字" width="300" />
					<el-table-column property="description" label="描述" width="400" />
				</el-table>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script lang="ts">
// @ts-ignore
import { getKDB } from '@/api/BookApi';

interface TableItem {
	name: string;
	description: string;
	[key: string]: any;
}

export default {
	name: 'AgentTable',
	props: {
		groupId: {
			type: Number,
			default: 0
		},
		embeddingModelId: {
			type: [Number, String],
			default: ''
		},
		selectedKnowledgeBases: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			selectedRows: [] as TableItem[],
			tableData: [] as TableItem[],
			allTableData: [] as TableItem[], // 存储所有数据，用于过滤
			activeTab: '', // 当前激活的tab
			tableNames: [] as string[], // 所有的tableName
			selectedTabData: {} as Record<string, TableItem[]>, // 每个tab选中的数据
		};
	},
	watch: {
		groupId: {
			immediate: true,
			handler(newVal) {
				if (newVal && newVal !== 0) {
					this.getTableData();
				}
			}
		},
		embeddingModelId: {
			immediate: true,
			handler() {
				if (this.allTableData && this.allTableData.length > 0) {
					this.tableData = this.filterTableData(this.allTableData);
					this.initTabs();
				}
			}
		},
		selectedKnowledgeBases: {
			deep: true,
			handler() {
				// 当传入的选中数据发生变化时，重新初始化选中状态
				if (this.tableNames.length > 0) {
					this.initTabs();
				}
			}
		}
	},
	methods: {
		getTableData() {
			if (!this.groupId || this.groupId === 0) {
				return;
			}

			getKDB(this.groupId).then((res) => {
				this.allTableData = res.data;
				this.tableData = this.filterTableData(res.data);
				this.initTabs();
			}).catch(error => {
				console.error('获取知识库数据失败:', error);
				this.allTableData = [];
				this.tableData = [];
				this.tableNames = [];
				this.activeTab = '';
			});
		},
		initTabs() {
			// 获取所有唯一的tableName
			const tableNamesSet = new Set<string>();
			this.tableData.forEach(item => {
				if (item.tableName) {
					tableNamesSet.add(item.tableName);
				}
			});
			this.tableNames = Array.from(tableNamesSet);

			// 设置默认激活的tab
			if (this.tableNames.length > 0 && !this.activeTab) {
				this.activeTab = this.tableNames[0];
			}

			// 初始化每个tab的选中数据，并设置初始选中状态
			this.tableNames.forEach(tableName => {
				if (!this.selectedTabData[tableName]) {
					this.selectedTabData[tableName] = [];
				}

				// 根据传入的 selectedKnowledgeBases 设置初始选中状态
				if (this.selectedKnowledgeBases && this.selectedKnowledgeBases.length > 0) {
					const tabData = this.getTableDataByTab(tableName);
					const selectedInThisTab = tabData.filter(item =>
						this.selectedKnowledgeBases.some(selected => selected.id === item.id)
					);
					this.selectedTabData[tableName] = selectedInThisTab;
				}
			});

			// 设置当前激活tab的选中数据
			this.selectedRows = this.selectedTabData[this.activeTab] || [];
			this.$emit('updateValue', this.selectedRows);

			// 使用 nextTick 确保表格渲染完成后设置选中状态
			this.$nextTick(() => {
				this.setTableSelection();
			});
		},
		filterTableData(data) {
			if (!data || data.length === 0) {
				return [];
			}
			if (!this.embeddingModelId) {
				return data;
			} else {
				// 根据切词模型ID过滤知识库
				const embeddingModelIdNum = typeof this.embeddingModelId === 'string'
					? parseInt(this.embeddingModelId, 10)
					: this.embeddingModelId;

				return data.filter(item =>
					item.embeddingModelId === embeddingModelIdNum
				);
			}
		},
		getTableDataByTab(tableName: string) {
			return this.tableData.filter(item => item.tableName === tableName);
		},
		setTableSelection() {
			// 设置当前激活tab的表格选中状态
			if (this.$refs.tableRefs && this.selectedTabData[this.activeTab]) {
				const tables = Array.isArray(this.$refs.tableRefs) ? this.$refs.tableRefs : [this.$refs.tableRefs];
				const currentTabIndex = this.tableNames.indexOf(this.activeTab);
				const currentTable = tables[currentTabIndex];

				if (currentTable) {
					// 清除所有选中状态
					currentTable.clearSelection();
					// 设置选中状态
					this.selectedTabData[this.activeTab].forEach(row => {
						currentTable.toggleRowSelection(row, true);
					});
				}
			}
		},
		handleTabChange(tabName: string) {
			this.activeTab = tabName;
			// 切换tab时，清除其他tab的选中状态，只保留当前tab的选中数据
			this.selectedRows = this.selectedTabData[tabName] || [];
			this.$emit('updateValue', this.selectedRows);

			// 设置新tab的表格选中状态
			this.$nextTick(() => {
				this.setTableSelection();
			});
		},
		handleSelectionChange(rows: TableItem[]) {
			// 保存当前tab的选中数据
			this.selectedTabData[this.activeTab] = rows;
			this.selectedRows = rows;
			this.$emit('updateValue', this.selectedRows);
		},
	},
};
</script>
