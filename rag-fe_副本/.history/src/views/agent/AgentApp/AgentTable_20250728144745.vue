<template>
	<el-table
		@selection-change="handleSelectionChange"
		:data="tableData"
		ref="tableRef"
	>
		<el-table-column type="selection" width="55" />
		<el-table-column property="name" label="名字" width="300" />
		<el-table-column property="description" label="描述" width="400" />
	</el-table>
</template>

<script lang="ts">
// @ts-ignore
import { getKDB } from '@/api/BookApi';

interface TableItem {
	name: string;
	description: string;
	[key: string]: any;
}

export default {
	name: 'AgentTable',
	props: {
		groupId: {
			type: Number,
			default: 0
		},
		embeddingModelId: {
			type: [Number, String],
			default: ''
		},
		tableName: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			selectedRows: [] as TableItem[],
			tableData: [] as TableItem[],
			// allTableData: [] as TableItem[], // 存储所有数据，用于过滤
			allTableData:[
    {
        "createAt": "2025-03-07 11:37:58",
        "description": "知识库描述1111",
        "embeddingModelId": 3,
        "embeddingRule": "{\"delimiter\":\"\\n!?。！？\",\"chunkTokenNum\":600}",
        "groupId": 18,
        "id": 13,
        "name": "知识库测试",
        "owner": "lirui42;guanlin",
        "parseId": 1,
        "status": 1,
        "tableName": "chunks",
        "updateAt": "2025-06-11 20:14:28"
    },
    {
        "createAt": "2025-06-12 17:54:20",
        "description": "21312",
        "embeddingModelId": 3,
        "embeddingRule": "{\"delimiter\":[\"。\"],\"chunkTokenNum\":600}",
        "groupId": 18,
        "id": 57,
        "name": "213123",
        "owner": "xushixuan01",
        "parseId": 1,
        "status": 1,
        "tableName": "chunks",
        "updateAt": "2025-06-12 17:54:20"
    },
    {
        "createAt": "2025-06-17 10:23:30",
        "description": "333",
        "embeddingModelId": 3,
        "embeddingRule": "{\"delimiter\":[\"。\",\"！\"],\"chunkTokenNum\":600}",
        "groupId": 18,
        "id": 58,
        "name": "123",
        "owner": "xushixuan01",
        "parseId": 1,
        "status": 1,
        "tableName": "chunks",
        "updateAt": "2025-06-17 10:23:30"
    },
    {
        "createAt": "2025-06-17 10:25:48",
        "description": "312312",
        "embeddingModelId": 3,
        "embeddingRule": "{\"delimiter\":[\"。\"],\"groupId\":18,\"chunkTokenNum\":600}",
        "groupId": 18,
        "id": 59,
        "name": "123",
        "owner": "xushixuan01",
        "parseId": 1,
        "status": 1,
        "tableName": "chunks",
        "updateAt": "2025-06-17 10:25:48"
    },
    {
        "createAt": "2025-06-17 17:06:58",
        "description": "13",
        "embeddingModelId": 3,
        "embeddingRule": "{\"delimiter\":[\"。\",\"！\"],\"chunkTokenNum\":600}",
        "groupId": 18,
        "id": 62,
        "name": "test",
        "owner": "xushixuan01",
        "parseId": 1,
        "status": 1,
        "tableName": "chunks",
        "updateAt": "2025-06-17 17:06:58"
    },
    {
        "createAt": "2025-06-17 17:42:25",
        "description": "123",
        "embeddingModelId": 3,
        "embeddingRule": "{\"delimiter\":[\"。\"],\"chunkTokenNum\":600}",
        "groupId": 18,
        "id": 63,
        "name": "test",
        "owner": "xushixuan01",
        "parseId": 1,
        "status": 1,
        "tableName": "chunks",
        "updateAt": "2025-06-17 17:42:25"
    },
    {
        "createAt": "2025-06-17 17:44:04",
        "description": "@！3123",
        "embeddingModelId": 3,
        "embeddingRule": "{\"delimiter\":[\"。\",\"！\"],\"chunkTokenNum\":600}",
        "groupId": 18,
        "id": 64,
        "name": "？？？",
        "owner": "xushixuan01",
        "parseId": 1,
        "status": 1,
        "tableName": "chunks",
        "updateAt": "2025-06-17 17:44:04"
    },
    {
        "createAt": "2025-07-18 11:42:24",
        "description": "这个是一个很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长的描述",
        "embeddingModelId": 4,
        "embeddingRule": "{\"delimiter\":[\"。\",\"！\",\"；\",\"？\",\".\",\"!\",\";\",\"?\",\"\\n\",\"\\n\\n\"],\"chunkTokenNum\":\"599\"}",
        "groupId": 18,
        "id": 69,
        "name": "这是一个很长很长名字",
        "owner": "xushixuan01",
        "parseId": 2,
        "status": 1,
        "tableName": "chunks",
        "updateAt": "2025-07-18 11:42:24"
    },
    {
        "createAt": "2025-07-22 21:51:56",
        "description": "HAL ",
        "embeddingModelId": 3,
        "embeddingRule": "{\"delimiter\":[\"。\",\"！\",\"？\"],\"chunkTokenNum\":600}",
        "groupId": 18,
        "id": 70,
        "name": "借我试一下",
        "parseId": 1,
        "status": 1,
        "tableName": "chunks",
        "updateAt": "2025-07-22 21:51:56"
    },
    {
        "createAt": "2025-07-22 21:57:12",
        "description": "HAL ",
        "embeddingModelId": 3,
        "embeddingRule": "{\"delimiter\":[\"。\",\"！\"],\"chunkTokenNum\":\"601\"}",
        "groupId": 18,
        "id": 71,
        "name": "借我试一下",
        "owner": "xushixuan01",
        "parseId": 1,
        "status": 1,
        "tableName": "chunks",
        "updateAt": "2025-07-24 14:34:22"
    },
    {
        "createAt": "2025-07-22 21:57:39",
        "description": "HAL ",
        "embeddingModelId": 3,
        "embeddingRule": "{\"delimiter\":[\"。\",\"！\"],\"chunkTokenNum\":600}",
        "groupId": 18,
        "id": 72,
        "name": "312312",
        "owner": "xuhuibi",
        "parseId": 1,
        "status": 1,
        "tableName": "chunks",
        "updateAt": "2025-07-22 21:57:39"
    }
]
		};
	},

	watch: {
		groupId: {
			immediate: true,
			handler(newVal) {
				if (newVal && newVal !== 0) {
					this.getTableData();
				}
			}
		},
		embeddingModelId: {
			immediate: true,
			handler() {
				if (this.allTableData && this.allTableData.length > 0) {
					this.tableData = this.filterTableData(this.allTableData);
				}
			}
		},
		tableName: {
			immediate: true,
			handler() {
				if (this.allTableData && this.allTableData.length > 0) {
					this.tableData = this.filterTableData(this.allTableData);
				}
			}
		}
	},
	methods: {
		getTableData() {
			if (!this.groupId || this.groupId === 0) {
				return;
			}

			getKDB(this.groupId).then((res) => {
				this.allTableData = res.data;
				this.tableData = this.filterTableData(res.data);
			}).catch(error => {
				console.error('获取知识库数据失败:', error);
				this.allTableData = [];
				this.tableData = [];
			});
		},

		filterTableData(data) {
			if (!data || data.length === 0) {
				return [];
			}

			let filteredData = data;

			// 根据切词模型ID过滤知识库
			if (this.embeddingModelId) {
				const embeddingModelIdNum = typeof this.embeddingModelId === 'string'
					? parseInt(this.embeddingModelId, 10)
					: this.embeddingModelId;

				filteredData = filteredData.filter(item =>
					item.embeddingModelId === embeddingModelIdNum
				);
			}

			// 根据tableName过滤
			if (this.tableName) {
				filteredData = filteredData.filter(item =>
					item.tableName === this.tableName
				);
			}

			return filteredData;
		},

		handleSelectionChange(rows: TableItem[]) {
			this.selectedRows = rows;
			this.$emit('updateValue', this.selectedRows);
			console.log('当前选择的知识库:', this.selectedRows);
			console.log('当前tableName:', this.tableName);
		},
	},
};
</script>


