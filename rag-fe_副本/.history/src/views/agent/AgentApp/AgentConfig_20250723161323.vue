<!-- eslint-disable -->
<template>
	<div class="agent-config">
		<div class="config-container">
			<!-- 应用设定部分 -->
			<div class="config-section">
				<!-- <div class="section-header">
					<el-icon><UserFilled /></el-icon>
					<span>应用设定</span>
				</div> -->

				<div class="section-content">
					<div class="setting-group">
						<BasicSettings
							:baseURL="baseURL"
							:imageUrl="agentData.imageUrl"
							:agentName="agentData.name"
							:description="agentData.description"
							:groupId="agentData.groupId"
							@update:imageUrl="updateField('imageUrl', $event)"
							@update:agentName="updateField('name', $event)"
							@update:description="updateField('description', $event)"
							@update:groupId="updateField('groupId', $event)"
						/>
					</div>

					<div class="setting-group">
						<ModelSettings
							:modelOptions="modelOptions"
							:embeddingModelOptions="embeddingModelOptions"
							:modelId="agentData.modelId"
							:embeddingModelId="agentData.embeddingModelId"
							:rolePrompt="agentData.rolePrompt"
							:rulePrompt="agentData.rulePrompt"
							:responsePrompt="agentData.responsePrompt"
							@update:modelId="(val) => updateField('modelId', val)"
							@update:embeddingModelId="(val) => updateField('embeddingModelId', val)"
							@update:modelSettings="updateModelSettings"
							@update:functionCallSupport="updateFunctionCallSupport"
						/>
					</div>

					<div class="setting-group">
						<KnowledgeSettings
							:recallCount="agentData.recallCount"
							:similarity="agentData.similarity"
							:selectedKnowledgeBases="agentData.selectedRows"
							@update:recallCount="updateField('recallCount', $event)"
							@update:similarity="updateField('similarity', $event)"
							@openAssociationDialog="$emit('openAssociationDialog')"
						/>
					</div>

					<div class="setting-group">
						<MCPServerSettings
							:selectedMCPServers="agentData.flowNodes || []"
							:functionCallSupported="functionCallSupported"
							@update:selectedMCPServers="updateField('flowNodes', $event)"
							@openMCPServerDialog="$emit('openMCPServerDialog')"
						/>
					</div>
				</div>
			</div>

			<!-- 能力拓展部分 -->
			<!-- <div class="config-section">
				<div class="section-header">
					<el-icon><Tools /></el-icon>
					<span>能力拓展</span>
				</div>

				<div class="section-content">
					<div class="setting-group">
						<h6 class="group-title">知识</h6>
						<KnowledgeSettings
							:recallCount="agentData.recallCount"
							:similarity="agentData.similarity"
							:selectedKnowledgeBases="agentData.selectedRows"
							@update:recallCount="updateField('recallCount', $event)"
							@update:similarity="updateField('similarity', $event)"
							@openAssociationDialog="$emit('openAssociationDialog')"
						/>
					</div>
				</div>
			</div> -->
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import { UserFilled, Tools } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import agentApi from '@/api/AgentApi.ts';
import BasicSettings from './BasicSettings.vue';
import ModelSettings from './ModelSettings.vue';
import KnowledgeSettings from './KnowledgeSettings.vue';
import MCPServerSettings from './MCPServerSettings.vue';
interface FlowNode {
	mcpServerId: number;
	numbering: number;
	inputLimit?: string;
	outputLimit?: string;
	id?: number;
	mcpServerName?: string;
	type?: number;
}

interface ModelOption {
	id: number;
	name: string;
	[key: string]: any;
}

export default defineComponent({
	name: 'AgentConfig',
	components: {
		BasicSettings,
		ModelSettings,
		KnowledgeSettings,
		MCPServerSettings,
		UserFilled,
		Tools,
	},
	props: {
		agentData: {
			type: Object,
			required: true,
		},
		modelOptions: {
			type: Array,
			default: () => [],
		},
		embeddingModelOptions: {
			type: Array,
			default: () => [],
		},
		baseURL: {
			type: String,
			default: '',
		},
	},
	emits: ['update:agentData', 'openAssociationDialog', 'openMCPServerDialog', 'createSuccess'],
	setup(props, { emit }) {
		const dialogTableVisible = ref(false);
		const loading = ref(false);
		const functionCallSupported = ref(false);

		const updateField = (field: string, value: any) => {
			emit('update:agentData', { ...props.agentData, [field]: value });
		};

		const updateModelSettings = (settings: any) => {
			emit('update:agentData', { ...props.agentData, ...settings });
		};

		const updateFunctionCallSupport = (supported: boolean) => {
			functionCallSupported.value = supported;
		};

		// 验证数据
		const validateData = () => {
			// 必填字段验证
			if (!props.agentData.name?.trim()) {
				ElMessage.error('智能体标题不能为空');
				return false;
			}
			
			if (!props.agentData.groupId) {
				ElMessage.error('工作组不能为空，请选择工作组');
				return false;
			}
			// 验证flowNodes
			// if (props.agentData.flowNodes && props.agentData.flowNodes.length > 0) {
			// 	for (const node of props.agentData.flowNodes) {
			// 		if (!node.mcpServerId) {
			// 			ElMessage.error('MCP服务器ID不能为空');
			// 			return false;
			// 		}
					
			// 		if (node.numbering === undefined || node.numbering === null) {
			// 			ElMessage.error('节点顺序不能为空');
			// 			return false;
			// 		}

			// 		if (!node.mcpServerName) {
			// 			ElMessage.error('MCP服务器名称不能为空');
			// 			return false;
			// 		}
			// 	}
			// }

			return true;
		};

		const prepareRequestData = () => {
			const { 
				name, 
				description = '', 
				imageUrl: url = '',
				knowledgeBaseIds = '',
				recallCount = 1,
				similarity = 0.1,
				modelId,
				groupId,
				rolePrompt = 'qa',
				responsePrompt = '',
				rulePrompt = '',
				flowNodes = []
			} = props.agentData;
			
			// 对 flowNodes 重新进行排序和编号
			const sortedFlowNodes = [...flowNodes].sort((a, b) => a.numbering - b.numbering);
			const reNumberedFlowNodes = sortedFlowNodes.map((node, index) => ({
				...node,
				numbering: index
			}));

			// 获取模型名称
			let modelName = '';
			if (modelId && props.modelOptions) {
				const model = props.modelOptions.find((m: ModelOption) => m.id === modelId);
				if (model) {
					modelName = model.name;
				}
			}
			return {
				name,
				description,
				url,
				knowledgeBaseIds,
				recallCount,
				similarity,
				modelId,
				modelName,
				groupId,
				rolePrompt,
				// 保持原始字段名称一致
				responsePrompt: responsePrompt,
				rulePrompt,
				flowNodes: reNumberedFlowNodes.map((node: FlowNode) => ({
					...(node.id ? { id: node.id } : {}), // 如果存在id则包含，否则忽略
					mcpServerId: node.mcpServerId,
					mcpServerName: node.mcpServerName || '',
					numbering: node.numbering,
					inputLimit: node.inputLimit || '',
					outputLimit: node.outputLimit || '',
				}))
			};
		};
		
		// 创建智能体
		const createAgent = async () => {
			if (!validateData()) return;
			
			loading.value = true;
			try {
				const requestData = prepareRequestData();
				// if ('responsePrompt' in requestData) {
				// 	requestData.responsePrompter = requestData.responsePrompt;
				// 	delete requestData.responsePrompt;
				// }
				// 使用saveAgent方法
				const response = await agentApi.saveAgent(requestData);
				if (response && response.data && response.code === 200) {
					ElMessage.success('创建智能体成功');
					// 返回创建的智能体ID
					emit('createSuccess', parseInt(response.data)); 
				} else {
					const message = response?.message || '未知错误';
					ElMessage.error(`创建失败: ${message}`);
				}
			} catch (error) {
				console.error('创建智能体出错:', error);
				ElMessage.error('创建智能体时发生错误，请稍后重试');
			} finally {
				loading.value = false;
			}
		};
		
		// 更新智能体
		const updateAgent = async () => {
			if (!validateData()) return;
			if (!props.agentData.id) {
				ElMessage.error('缺少智能体ID，无法更新');
				return;
			}
			
			loading.value = true;
			try {
				const baseParams = prepareRequestData();
				// 更新  需要添加id
				const requestData = {
					...baseParams,
					id: props.agentData.id,
				};
				const response = await agentApi.updateAgent(requestData);

				if (response?.code === 200) {
					ElMessage.success(response?.data || '更新智能体成功');
				}
			} finally {
				loading.value = false;
			}
		};

		return {
			dialogTableVisible,
			loading,
			functionCallSupported,
			updateField,
			updateModelSettings,
			updateFunctionCallSupport,
			createAgent,
			updateAgent
		};
	},
});
</script>

<style scoped>
.agent-config {
	height: 100%;
	display: flex;
	flex-direction: column;
	background-color: #fff;
}

.config-container {
	display: flex;
	flex-direction: column; /* 改为垂直排列 */
	height: 100%;
	min-height: 0;
	gap: 20px; /* 添加两个部分之间的间距 */
}

.config-section {
	flex: 1;
	padding: 5px;
	overflow-y: auto;
	display: flex;
	flex-direction: column;
	border-bottom: 1px solid var(--el-border-color-light); /* 添加分割线 */
}

/* 移除左右分割线 */
.left-section {
	border-right: none;
}

.section-header {
	display: flex;
	align-items: center;
	gap: 8px;
	font-size: 14px;
	font-weight: 600;
	color: var(--el-text-color-primary);
	/* margin-bottom: 20px; */
	padding-bottom: 12px;
	border-bottom: 1px solid var(--el-border-color-light);
}

.section-content {
	flex: 1;
	overflow-y: auto;
	padding-right: 8px;
}

.setting-group {
	margin-bottom: 5px;
}

.group-title {
	font-size: 14px;
	color: var(--el-text-color-secondary);
	margin: 5px;
	font-weight: 500;
}

/* 滚动条样式 */
.section-content::-webkit-scrollbar {
	width: 6px;
}

.section-content::-webkit-scrollbar-thumb {
	background-color: var(--el-border-color-dark);
	border-radius: 3px;
}

.section-content::-webkit-scrollbar-track {
	background-color: var(--el-border-color-lighter);
}
</style>
