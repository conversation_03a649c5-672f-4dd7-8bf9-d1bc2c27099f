<template>
	<el-table
		@selection-change="handleSelectionChange"
		:data="tableData"
		ref="tableRef"
	>
		<el-table-column type="selection" width="55" />
		<el-table-column property="name" label="名字" width="300" />
		<el-table-column property="description" label="描述" width="400" />
	</el-table>
</template>

<script lang="ts">
// @ts-ignore
import { getKDB } from '@/api/BookApi';

interface TableItem {
	name: string;
	description: string;
	[key: string]: any;
}

export default {
	name: 'AgentTable',
	props: {
		groupId: {
			type: Number,
			default: 0
		},
		embeddingModelId: {
			type: [Number, String],
			default: ''
		},
		tableName: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			selectedRows: [] as TableItem[],
			tableData: [] as TableItem[],
			allTableData: [] as TableItem[], // 存储所有数据，用于过滤
			activeTab: '', // 当前激活的tab
			tabList: [] as Array<{name: string, label: string, count: number}>, // tab列表
			tabDataMap: {} as Record<string, TableItem[]>, // 按tableName分组的数据
		};
	},
	computed: {
		// 当前tab的数据
		currentTabData() {
			return this.tabDataMap[this.activeTab] || [];
		}
	},
	watch: {
		groupId: {
			immediate: true,
			handler(newVal) {
				if (newVal && newVal !== 0) {
					this.getTableData();
				}
			}
		},
		embeddingModelId: {
			immediate: true,
			handler() {
				if (this.allTableData && this.allTableData.length > 0) {
					this.processTabData(this.allTableData);
				}
			}
		}
	},
	methods: {
		getTableData() {
			if (!this.groupId || this.groupId === 0) {
				return;
			}

			getKDB(this.groupId).then((res) => {
				this.allTableData = res.data;
				this.processTabData(res.data);
			}).catch(error => {
				console.error('获取知识库数据失败:', error);
				this.allTableData = [];
				this.resetTabData();
			});
		},
		// 处理tab数据
		processTabData(data: TableItem[]) {
			if (!data || data.length === 0) {
				this.resetTabData();
				return;
			}

			// 先过滤数据
			const filteredData = this.filterTableData(data);

			// 按tableName分组
			const groupedData: Record<string, TableItem[]> = {};
			filteredData.forEach((item: TableItem) => {
				const tableName = item.tableName || 'default';
				if (!groupedData[tableName]) {
					groupedData[tableName] = [];
				}
				groupedData[tableName].push(item);
			});

			// 生成tab列表
			this.tabList = Object.keys(groupedData).map(tableName => ({
				name: tableName,
				label: this.getTabLabel(tableName),
				count: groupedData[tableName].length
			}));

			// 设置tabDataMap
			this.tabDataMap = groupedData;

			// 设置默认激活的tab
			if (this.tabList.length > 0 && !this.activeTab) {
				this.activeTab = this.tabList[0].name;
			}

			// 如果当前激活的tab不存在于新的tab列表中，重置为第一个
			if (!this.tabList.find(tab => tab.name === this.activeTab)) {
				this.activeTab = this.tabList.length > 0 ? this.tabList[0].name : '';
			}
		},
		// 重置tab数据
		resetTabData() {
			this.tabList = [];
			this.tabDataMap = {};
			this.activeTab = '';
		},
		// 获取tab标签名
		getTabLabel(tableName: string) {
			// 可以根据需要自定义不同tableName的显示名称
			const labelMap: Record<string, string> = {
				'chunks': '文档片段',
				'documents': '文档',
				'default': '默认'
			};
			return labelMap[tableName] || tableName;
		},
		filterTableData(data) {
			if (!data || data.length === 0) {
				return [];
			}
			if (!this.embeddingModelId) {
				return data;
			} else {
				// 根据切词模型ID过滤知识库
				const embeddingModelIdNum = typeof this.embeddingModelId === 'string'
					? parseInt(this.embeddingModelId, 10)
					: this.embeddingModelId;

				return data.filter(item =>
					item.embeddingModelId === embeddingModelIdNum
				);
			}
		},
		// 处理tab切换
		handleTabChange(tabName: string) {
			this.activeTab = tabName;
			// 切换tab时清空选择
			this.selectedRows = [];
			this.$emit('updateValue', this.selectedRows);
			// 清空表格选择状态
			this.$nextTick(() => {
				if (this.$refs.tableRef) {
					(this.$refs.tableRef as any).clearSelection();
				}
			});
		},
		handleSelectionChange(rows: TableItem[]) {
			this.selectedRows = rows;
			this.$emit('updateValue', this.selectedRows);
			console.log('当前选择的知识库:', this.selectedRows);
			console.log('当前tab:', this.activeTab);
		},
	},
};
</script>

<style scoped>
.knowledge-base-tabs {
	height: 100%;
}

.knowledge-base-tabs :deep(.el-tabs) {
	height: 100%;
	display: flex;
	flex-direction: column;
}

.knowledge-base-tabs :deep(.el-tabs__content) {
	flex: 1;
	overflow: hidden;
}

.knowledge-base-tabs :deep(.el-tab-pane) {
	height: 100%;
	overflow: auto;
}

.knowledge-base-tabs :deep(.el-table) {
	height: 100%;
}

.knowledge-base-tabs :deep(.el-tabs__header) {
	margin-bottom: 10px;
}

.knowledge-base-tabs :deep(.el-tabs__nav-wrap) {
	padding: 0 10px;
}
</style>
