<!-- eslint-disable -->
<template>
	<div class="model-settings">
		<el-card class="settings-card">
			<template #header>
				<div class="section-header">
					<span class="group-title">模型选择</span>
					<el-button type="primary" text :icon="Edit" class="action-button" @click="openPromptDialog">
						用户指令
					</el-button>
				</div>
			</template>

			<el-row class="model-selection">
				<el-col :span="6">
					<span class="selection-label">生成模型1</span>
				</el-col>
				<el-col :span="16" :offset="2">
					<el-select v-model="selectedModel" placeholder="选择模型" size="large" class="model-selector">
						<el-option v-for="item in validatedModelOptions" :key="item.id"
							:label="`${item.platform}-${item.name}`" :value="item.id" />
					</el-select>
				</el-col>
			</el-row>
		</el-card>

		<el-dialog v-model="dialogVisible" title="模型交互设置" width="70%" @closed="handleDialogClose">
			<el-row class="model-selection">
				<el-col :span="4">
					<span class="selection-label">系统角色</span>
				</el-col>
				<el-col :span="20">
					<el-input v-model="localRolePrompt" placeholder="设置模型的角色" @input="updateRolePrompt" />
				</el-col>
			</el-row>

			<el-row class="model-selection">
				<el-col :span="4">
					<span class="selection-label">用户指令</span>
				</el-col>
				<el-col :span="20">
					<markdown-editor v-model="localRulePrompt" height="300px" />
				</el-col>
			</el-row>

			<el-row class="model-selection">
				<el-col :span="4">
					<span class="selection-label">应用回复</span>
				</el-col>
				<el-col :span="20">
					<markdown-editor v-model="localResponsePrompt" height="300px" />
				</el-col>
			</el-row>
			<template #footer>
				<el-button @click="dialogVisible = false">取消</el-button>
				<el-button type="primary" @click="savePrompts">保存</el-button>
			</template>
		</el-dialog>
	</div>
</template>

<script>
import { ref, computed, watch, nextTick } from 'vue';
import { Edit } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import _ from 'lodash';
import MarkdownEditor from './MarkdownEditor.vue';

export default {
	name: 'ModelSettings',
	components: {
		MarkdownEditor,
	},
	props: {
		modelOptions: {
			type: Array,
			default: () => [],
			validator: (value) => value.every((item) => item.id && item.platform && item.name),
		},
		modelId: {
			type: String,
			required: true,
			default: '',
		},
		rolePrompt: {
			type: String,
			default: '',
		},
		rulePrompt: {
			type: String,
			default: '{}',
			validator: (value) => {
				// 允许任何格式
				return true;
			},
		},
		responsePrompt: {
			type: String,
			default: '{}',
			validator: (value) => {
				// 允许任何格式
				return true;
			},
		},
	},
	emits: {
		'update:modelId': (value) => typeof value === 'string',
		'update:rolePrompt': (value) => typeof value === 'string',
		'update:modelSettings': (value) =>
			typeof value.rolePrompt === 'string' &&
			typeof value.rulePrompt === 'string' &&
			typeof value.responsePrompt === 'string',
		'update:functionCallSupport': (value) => typeof value === 'boolean',
	},
	setup(props, { emit }) {
		// State
		const dialogVisible = ref(false);
		const editorKey = ref(0);
		const localRolePrompt = ref(props.rolePrompt);
		const localRulePrompt = ref(props.rulePrompt);
		const localResponsePrompt = ref(props.responsePrompt);
		// Computed
		const selectedModel = computed({
			get: () => props.modelId,
			set: (val) => emit('update:modelId', val),
		});

		const validatedModelOptions = computed(() => {
			return props.modelOptions.filter((item) => item.id);
		});

		// 计算当前选中模型是否支持functionCall
		const currentModelSupportsFunctionCall = computed(() => {
			if (!selectedModel.value) {
				return false;
			}

			const currentModel = props.modelOptions.find(model =>
				model.id.toString() === selectedModel.value.toString());
			return currentModel && currentModel.functionCall === 1;
		});

		// 监听props变化
		watch(
			() => props.rulePrompt,
			(newVal) => {
				localRulePrompt.value = newVal;
			}
		);

		watch(
			() => props.responsePrompt,
			(newVal) => {
				localResponsePrompt.value = newVal;
			}
		);

		watch(
			() => props.rolePrompt,
			(newVal) => {
				localRolePrompt.value = newVal;
			}
		);

		// 监听模型变化，通知父组件functionCall支持状态
		watch(
			() => currentModelSupportsFunctionCall.value,
			(newVal) => {
				emit('update:functionCallSupport', newVal);
			},
			{ immediate: true }
		);

		// 监听选中模型变化
		watch(
			() => selectedModel.value,
			() => {
				// 选中模型变化时，发送functionCall支持状态
				emit('update:functionCallSupport', currentModelSupportsFunctionCall.value);
			}
		);

		const updateRolePrompt = (value) => {
			emit('update:rolePrompt', value);
		};

		const openPromptDialog = async () => {
			editorKey.value += 1;
			await nextTick();
			dialogVisible.value = true;
		};

		const handleDialogClose = () => { };

		const savePrompts = () => {
			if (localRolePrompt.value.trim() === '') {
				ElMessage.error('角色提示不能为空');
				return;
			}

			emit('update:modelSettings', {
				rolePrompt: localRolePrompt.value,
				rulePrompt: localRulePrompt.value,
				responsePrompt: localResponsePrompt.value,
			});

			ElMessage.success('设置保存成功');
			dialogVisible.value = false;
		};

		return {
			Edit,
			selectedModel,
			validatedModelOptions,
			dialogVisible,
			localRolePrompt,
			editorKey,
			updateRolePrompt,
			openPromptDialog,
			handleDialogClose,
			savePrompts,
			localRulePrompt,
			localResponsePrompt,
			currentModelSupportsFunctionCall,
		};
	},
};
</script>

<style lang="less" scoped>
.model-settings {
	border-radius: 8px;
}

.group-title {
	font-size: 14px;
	color: var(--el-text-color-secondary);
	margin: 5px;
	font-weight: 500;
}

.settings-card {
	padding: 10px;
	border-radius: 8px;
}

.section-header {
	display: flex;
	align-items: center;

	.action-button {
		margin-left: auto;
		font-size: 13px;
	}
}

.model-selection {
	margin: 10px 0;
	display: flex;
	align-items: center;
}

.selection-label {
	font-size: 14px;
	color: var(--el-text-color-secondary);
}

.model-selector {
	width: 100%;
}
</style>
