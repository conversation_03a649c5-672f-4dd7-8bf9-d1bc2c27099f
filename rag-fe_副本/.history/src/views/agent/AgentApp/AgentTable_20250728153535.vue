<template>
	<div>
		<el-tabs v-model="activeTab" @tab-change="handleTabChange">
			<el-tab-pane
				v-for="tableName in tableNames"
				:key="tableName"
				:label="tableName"
				:name="tableName"
			>
				<el-table
					@selection-change="handleSelectionChange"
					:data="getTableDataByTab(tableName)"
					:key="tableName"
					ref="tableRefs"
				>
					<el-table-column type="selection" width="55" />
					<el-table-column property="name" label="名字" width="300" />
					<el-table-column property="description" label="描述" width="400" />
				</el-table>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script lang="ts">
// @ts-ignore
import { getKDB } from '@/api/BookApi';

interface TableItem {
	name: string;
	description: string;
	[key: string]: any;
}

export default {
	name: 'AgentTable',
	props: {
		groupId: {
			type: Number,
			default: 0
		},
		embeddingModelId: {
			type: [Number, String],
			default: ''
		},
		initialSelectedRows: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			selectedRows: [] as TableItem[],
			tableData: [] as TableItem[],
			allTableData: [] as TableItem[], // 存储所有数据，用于过滤
			activeTab: '', // 当前激活的tab
			tableNames: [] as string[], // 所有的tableName
			selectedTabData: {} as Record<string, TableItem[]>, // 每个tab选中的数据
		};
	},
	watch: {
		groupId: {
			immediate: true,
			handler(newVal) {
				if (newVal && newVal !== 0) {
					this.getTableData();
				}
			}
		},
		embeddingModelId: {
			immediate: true,
			handler() {
				if (this.allTableData && this.allTableData.length > 0) {
					this.tableData = this.filterTableData(this.allTableData);
					this.initTabs();
				}
			}
		},
		initialSelectedRows: {
			immediate: true,
			deep: true,
			handler() {
				if (this.tableData.length > 0) {
					this.initTabs();
				}
			}
		}
	},
	methods: {
		getTableData() {
			if (!this.groupId || this.groupId === 0) {
				return;
			}

			getKDB(this.groupId).then((res) => {
				this.allTableData = res.data;
				this.tableData = this.filterTableData(res.data);
				this.initTabs();
			}).catch(error => {
				console.error('获取知识库数据失败:', error);
				this.allTableData = [];
				this.tableData = [];
				this.tableNames = [];
				this.activeTab = '';
			});
		},
		initTabs() {
			// 获取所有唯一的tableName
			const tableNamesSet = new Set<string>();
			this.tableData.forEach(item => {
				if (item.tableName) {
					tableNamesSet.add(item.tableName);
				}
			});
			this.tableNames = Array.from(tableNamesSet);

			// 设置默认激活的tab
			if (this.tableNames.length > 0 && !this.activeTab) {
				this.activeTab = this.tableNames[0];
			}

			// 初始化每个tab的选中数据
			this.tableNames.forEach(tableName => {
				if (!this.selectedTabData[tableName]) {
					this.selectedTabData[tableName] = [];
				}
			});

			// 处理初始选中状态
			this.initSelectedState();
		},
		initSelectedState() {
			if (!this.initialSelectedRows || this.initialSelectedRows.length === 0) {
				return;
			}

			// 清空之前的选中状态
			Object.keys(this.selectedTabData).forEach(tableName => {
				this.selectedTabData[tableName] = [];
			});

			// 根据初始选中数据设置选中状态
			this.initialSelectedRows.forEach(selectedItem => {
				const tableName = selectedItem.tableName;
				if (tableName && this.selectedTabData[tableName]) {
					// 在当前数据中查找匹配的项
					const matchedItem = this.tableData.find(item => item.id === selectedItem.id);
					if (matchedItem) {
						this.selectedTabData[tableName].push(matchedItem);
					}
				}
			});

			// 如果有选中的数据，切换到对应的tab
			const firstSelectedItem = this.initialSelectedRows[0];
			if (firstSelectedItem && firstSelectedItem.tableName) {
				this.activeTab = firstSelectedItem.tableName;
				this.selectedRows = this.selectedTabData[firstSelectedItem.tableName] || [];
				this.$emit('updateValue', this.selectedRows);
			}
		},
		filterTableData(data) {
			if (!data || data.length === 0) {
				return [];
			}
			if (!this.embeddingModelId) {
				return data;
			} else {
				// 根据切词模型ID过滤知识库
				const embeddingModelIdNum = typeof this.embeddingModelId === 'string'
					? parseInt(this.embeddingModelId, 10)
					: this.embeddingModelId;

				return data.filter(item =>
					item.embeddingModelId === embeddingModelIdNum
				);
			}
		},
		getTableDataByTab(tableName: string) {
			return this.tableData.filter(item => item.tableName === tableName);
		},
		handleTabChange(tabName: string) {
			this.activeTab = tabName;
			// 切换tab时，清除其他tab的选中状态，只保留当前tab的选中数据
			this.selectedRows = this.selectedTabData[tabName] || [];
			this.$emit('updateValue', this.selectedRows);
			// 同步表格选中状态
			this.$nextTick(() => {
				this.syncTableSelection();
			});
		},
		syncTableSelection() {
			// 获取当前激活tab的表格引用
			const tableRefs = this.$refs.tableRefs;
			if (!tableRefs) return;

			// 如果是数组，找到对应的表格
			let currentTable = null;
			if (Array.isArray(tableRefs)) {
				// 根据当前激活的tab找到对应的表格
				const tabIndex = this.tableNames.indexOf(this.activeTab);
				currentTable = tableRefs[tabIndex];
			} else {
				currentTable = tableRefs;
			}

			if (currentTable && currentTable.clearSelection) {
				// 清除所有选中
				currentTable.clearSelection();
				// 重新选中当前tab的数据
				const selectedData = this.selectedTabData[this.activeTab] || [];
				selectedData.forEach(row => {
					currentTable.toggleRowSelection(row, true);
				});
			}
		},
		handleSelectionChange(rows: TableItem[]) {
			// 保存当前tab的选中数据
			this.selectedTabData[this.activeTab] = rows;
			this.selectedRows = rows;
			this.$emit('updateValue', this.selectedRows);
			console.log('当前选中的数据:', this.selectedRows);
			console.log('当前激活的tab:', this.activeTab);
		},
	},
};
</script>
