<template>
	<el-table
		@selection-change="handleSelectionChange"
		:data="tableData"
		ref="tableRef"
	>
		<el-table-column type="selection" width="55" />
		<el-table-column property="name" label="名字" width="300" />
		<el-table-column property="description" label="描述" width="400" />
	</el-table>
</template>

<script lang="ts">
// @ts-ignore
import { getKDB } from '@/api/BookApi';

interface TableItem {
	name: string;
	description: string;
	[key: string]: any;
}

export default {
	name: 'AgentTable',
	props: {
		groupId: {
			type: Number,
			default: 0
		},
		embeddingModelId: {
			type: [Number, String],
			default: ''
		},
		tableName: {
			type: String,
			default: ''
		},
		selectedRows: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			selectedRows: [] as TableItem[],
			tableData: [] as TableItem[],
			allTableData: [] as TableItem[], // 存储所有数据，用于过滤
			isUserSelecting: false, // 标记用户是否正在选择
		};
	},
	watch: {
		groupId: {
			immediate: true,
			handler(newVal) {
				if (newVal && newVal !== 0) {
					this.getTableData();
				}
			}
		},
		embeddingModelId: {
			immediate: true,
			handler() {
				if (this.allTableData && this.allTableData.length > 0) {
					this.tableData = this.filterTableData(this.allTableData);
				}
			}
		},
		tableName: {
			immediate: true,
			handler() {
				if (this.allTableData && this.allTableData.length > 0) {
					this.tableData = this.filterTableData(this.allTableData);
					this.restoreSelection();
				}
			}
		},
		selectedRows: {
			immediate: true,
			handler() {
				this.restoreSelection();
			}
		}
	},
	methods: {
		getTableData() {
			if (!this.groupId || this.groupId === 0) {
				return;
			}

			getKDB(this.groupId).then((res) => {
				this.allTableData = res.data;
				this.tableData = this.filterTableData(res.data);
				this.restoreSelection();
			}).catch(error => {
				console.error('获取知识库数据失败:', error);
				this.allTableData = [];
				this.tableData = [];
			});
		},
		// 恢复选择状态
		restoreSelection() {
			// 如果用户正在选择，不要恢复选择状态
			if (this.isUserSelecting) {
				return;
			}

			this.$nextTick(() => {
				if (this.$refs.tableRef && this.selectedRows && this.selectedRows.length > 0) {
					// 清空当前选择
					(this.$refs.tableRef as any).clearSelection();

					// 找到当前tab中应该被选中的项
					const currentTabSelectedItems = this.selectedRows.filter(selectedItem =>
						selectedItem.tableName === this.tableName
					);

					// 恢复选择状态
					currentTabSelectedItems.forEach(selectedItem => {
						const rowToSelect = this.tableData.find(row => row.id === selectedItem.id);
						if (rowToSelect) {
							(this.$refs.tableRef as any).toggleRowSelection(rowToSelect, true);
						}
					});
				} else if (this.$refs.tableRef) {
					// 如果当前tab没有选择项，清空选择
					(this.$refs.tableRef as any).clearSelection();
				}
			});
		},
		filterTableData(data) {
			if (!data || data.length === 0) {
				return [];
			}

			let filteredData = data;

			// 根据切词模型ID过滤知识库
			if (this.embeddingModelId) {
				const embeddingModelIdNum = typeof this.embeddingModelId === 'string'
					? parseInt(this.embeddingModelId, 10)
					: this.embeddingModelId;

				filteredData = filteredData.filter(item =>
					item.embeddingModelId === embeddingModelIdNum
				);
			}

			// 根据tableName过滤
			if (this.tableName) {
				filteredData = filteredData.filter(item =>
					item.tableName === this.tableName
				);
			}

			return filteredData;
		},
		handleSelectionChange(rows: TableItem[]) {
			// 标记用户正在选择
			this.isUserSelecting = true;

			this.selectedRows = rows;
			this.$emit('updateValue', this.selectedRows);
			console.log('当前tableName:', this.tableName);
			console.log('选择的行:', rows);

			// 延迟重置标记
			this.$nextTick(() => {
				setTimeout(() => {
					this.isUserSelecting = false;
				}, 100);
			});
		},
	},
};
</script>