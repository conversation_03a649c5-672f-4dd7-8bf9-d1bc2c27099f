<!-- eslint-disable -->
<template>
	<div class="agent-preview">
		<div class="chat-container-wrapper">
			<chat-container
				:conversations="conversations"
				:current-question="currentQuestion"
				:current-answer="currentAnswer"
			>
				<template #empty>
					<el-row justify="center">
						<el-col :span="20">
							<div
								style="
									text-align: center;
									font-size: 10px;
									font-family: 'Courier New', Courier, monospace;
								"
							></div>
						</el-col>
					</el-row>
					<el-row style="min-height: 400px" v-if="!showOutput">
						<el-col>
							<el-row style="margin-top: 100px">
								<el-col
									:span="24"
									style="display: flex; justify-content: center; align-items: center; height: 100%"
								>
									<img src="@/assets/chat.webp" style="width: 72px; height: 72px" />
								</el-col>
							</el-row>
							<el-row>
								<el-col
									:span="24"
									style="display: flex; justify-content: center; align-items: center; height: 100%"
								>
									<span>我的Agent</span>
								</el-col>
							</el-row>
						</el-col>
					</el-row>
				</template>
			</chat-container>
		</div>
		<div class="input-container">
			<agent-input v-model="prompt" :agentId="agentId" :isLoading="isLoading" @submit="startChat" />
		</div>
	</div>
</template>

<script>
import ChatContainer from '@/views/index/ChatContainer.vue';
import AgentInput from '@/views/agent/chat/AgentInput.vue';
import { renderMarkdown } from '@/utils/renderMarkdown.ts';
import { ElMessage } from 'element-plus';
import { nextTick } from 'vue';

export default {
	name: 'AgentPreview',
	components: { ChatContainer, AgentInput },
	props: {
		agentId: {
			type: Number,
			default: 0,
		},
	},
	data() {
		return {
			prompt: '',
			isLoading: false,
			currentQuestion: '',
			currentAnswer: '',
			conversations: [],
			showOutput: false,
			docUrls: [] as string[], // 存储docurl列表
			docLinks: [] as Array<{ id: string; fileName: string; docId: string }>,
			rawBuffer: '', // 添加rawBuffer到data中
			thinkBuffer: '', // 添加思考过程缓存
		};
	},
	methods: {
		async startChat(params) {
			const { prompt, agentId } = params;
			this.isLoading = true;
			this.currentQuestion = prompt;
			this.currentAnswer = '';
			this.prompt = '';
			this.showOutput = true;

			try {
				const response = await fetch('/rag/api/agent/stream?stream=true', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						query: this.currentQuestion,
						agentId: agentId,
					}),
				});

				if (!response.ok) {
					throw new Error('Network response was not ok');
				}

				// console.log(response, response.body?.getReader(), 'response.body');

				const reader = response.body?.getReader();
				const decoder = new TextDecoder('utf-8');
				let buffer = '';

				if (reader) {
					let tempBuffer = '';
					while (true) {
						const { value, done } = await reader.read();
						if (done) {
							break;
						}

						const chunk = decoder.decode(value, { stream: true });
						tempBuffer += chunk;
						const lines = tempBuffer.split('\n');
						tempBuffer = lines.pop() || '';

						for (const line of lines) {
							if (line.startsWith('data:"')) {
								const parts = line.split(':"');
								if (parts.length > 1) {
									const dataValue = parts[1].slice(0, -1);
									buffer += dataValue;
									this.currentAnswer = await renderMarkdown(buffer.replace(/\n+/g, '<br/>'));
									await nextTick(); // 确保DOM更新
								}
							}
						}
					}
				}

				this.conversations.push({
					question: this.currentQuestion,
					answer: this.currentAnswer,
				});

				this.currentQuestion = '';
				this.currentAnswer = '';
			} catch (error) {
				console.error('Error:', error);
				ElMessage.error('请求失败，请重试');
				// this.currentAnswer = '请求失败，请重试';
				this.conversations.push({
					question: this.currentQuestion,
					answer: '请求失败，请重试',
				});
				this.currentQuestion = '';
				this.currentAnswer = '';
			} finally {
				this.isLoading = false;
			}
		},
	},
};
</script>

<style scoped>
.agent-preview {
	height: 100%; /* 使用100%而不是calc */
	/* padding: 10px 5% 10px; */
	border-radius: 6px;
	background-color: #f3f6f9;
	background-image: url(https://agi-dev-platform-web.cdn.bcebos.com/ai_apaas/dist/img/bg_3363fe4c.png);
	background-repeat: no-repeat;
	background-size: 100% 100%;
	background-position: center center;
	position: relative;
	height: calc(100vh - 180px);
	box-sizing: border-box;

	.total-css {
		height: calc(100% - 180px);
		max-height: calc(100% - 10px);
		overflow-y: auto;
		margin-bottom: 20px;
	}
}

/* ChatContainer 容器样式 */
.chat-container-wrapper {
	flex: 1; /* 占据剩余空间 */
	min-height: 0; /* 关键：允许内容收缩 */
	display: flex;
	flex-direction: column;
	overflow: hidden; /* 隐藏内部溢出 */
	/* padding: 10px 5% 10px; */
	position: relative;
	height: calc(100vh - 180px);
	box-sizing: border-box;
}

/* 输入框容器 */
.input-container {
	flex-shrink: 0; /* 防止输入框被压缩 */
	padding-top: 10px;
}
</style>
