<template>
	<div>
		<el-tabs v-model="activeTab" @tab-change="handleTabChange">
			<el-tab-pane
				v-for="tableName in tableNames"
				:key="tableName"
				:label="tableName"
				:name="tableName"
			>
				<el-table
					@selection-change="handleSelectionChange"
					:data="getTableDataByTab(tableName)"
					ref="tableRefs"
					:row-class-name="getRowClassName"
				>
					<el-table-column type="selection" width="55" :selectable="isRowSelectable" />
					<el-table-column property="name" label="名字" width="300">
						<template #default="{ row }">
							<div class="name-cell">
								<span>{{ row.name }}</span>
								<el-tooltip
									v-if="!isEmbeddingModelMatch(row)"
									effect="dark"
									placement="top"
									:content="getTooltipContent(row)"
								>
									<el-icon class="warning-icon">
										<Warning />
									</el-icon>
								</el-tooltip>
							</div>
						</template>
					</el-table-column>
					<el-table-column property="description" label="描述" width="400" />
				</el-table>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script lang="ts">
// @ts-ignore
import { getKDB } from '@/api/BookApi';
// @ts-ignore
import modelApi from '@/api/ModelApi.ts';
import { Warning } from '@element-plus/icons-vue';

interface TableItem {
	name: string;
	description: string;
	embeddingModelId: number | string;
	[key: string]: any;
}

interface EmbeddingModel {
	id: number;
	platform: string;
	name: string;
	functionCall: number;
	lenLimit: number;
	tokenLimit: number;
	type: number;
}

export default {
	name: 'AgentTable',
	components: {
		Warning
	},
	props: {
		groupId: {
			type: Number,
			default: 0
		},
		embeddingModelId: {
			type: [Number, String],
			default: ''
		}
	},
	data() {
		return {
			selectedRows: [] as TableItem[],
			tableData: [] as TableItem[],
			allTableData: [] as TableItem[], // 存储所有数据，用于过滤
			activeTab: '', // 当前激活的tab
			tableNames: [] as string[], // 所有的tableName
			selectedTabData: {} as Record<string, TableItem[]>, // 每个tab选中的数据
			embeddingModels: [] as EmbeddingModel[], // 切词模型列表
		};
	},
	watch: {
		groupId: {
			immediate: true,
			handler(newVal) {
				if (newVal && newVal !== 0) {
					this.getTableData();
				}
			}
		},
		embeddingModelId: {
			immediate: true,
			handler() {
				if (this.allTableData && this.allTableData.length > 0) {
					this.tableData = this.filterTableData(this.allTableData);
					this.initTabs();
				}
			}
		}
	},
	methods: {
		getTableData() {
			if (!this.groupId || this.groupId === 0) {
				return;
			}

			getKDB(this.groupId).then((res) => {
				this.allTableData = res.data;
				this.tableData = this.filterTableData(res.data);
				this.initTabs();
			}).catch(error => {
				console.error('获取知识库数据失败:', error);
				this.allTableData = [];
				this.tableData = [];
				this.tableNames = [];
				this.activeTab = '';
			});
		},
		initTabs() {
			// 获取所有唯一的tableName
			const tableNamesSet = new Set<string>();
			this.tableData.forEach(item => {
				if (item.tableName) {
					tableNamesSet.add(item.tableName);
				}
			});
			this.tableNames = Array.from(tableNamesSet);

			// 设置默认激活的tab
			if (this.tableNames.length > 0 && !this.activeTab) {
				this.activeTab = this.tableNames[0];
			}

			// 初始化每个tab的选中数据
			this.tableNames.forEach(tableName => {
				if (!this.selectedTabData[tableName]) {
					this.selectedTabData[tableName] = [];
				}
			});
		},
		filterTableData(data) {
			// 不再过滤数据，返回所有数据
			if (!data || data.length === 0) {
				return [];
			}
			return data;
		},
		getTableDataByTab(tableName: string) {
			return this.tableData.filter(item => item.tableName === tableName);
		},
		handleTabChange(tabName: string) {
			this.activeTab = tabName;
			// 切换tab时，清除其他tab的选中状态，只保留当前tab的选中数据
			this.selectedRows = this.selectedTabData[tabName] || [];
			this.$emit('updateValue', this.selectedRows);
		},
		handleSelectionChange(rows: TableItem[]) {
			// 保存当前tab的选中数据
			this.selectedTabData[this.activeTab] = rows;
			this.selectedRows = rows;
			this.$emit('updateValue', this.selectedRows);
		},
		// 检查切词模型是否匹配
		isEmbeddingModelMatch(row: TableItem): boolean {
			if (!this.embeddingModelId) {
				return true;
			}
			const embeddingModelIdNum = typeof this.embeddingModelId === 'string'
				? parseInt(this.embeddingModelId, 10)
				: this.embeddingModelId;

			const rowEmbeddingModelId = typeof row.embeddingModelId === 'string'
				? parseInt(row.embeddingModelId, 10)
				: row.embeddingModelId;

			return rowEmbeddingModelId === embeddingModelIdNum;
		},
		// 判断行是否可选择
		isRowSelectable(row: TableItem): boolean {
			return this.isEmbeddingModelMatch(row);
		},
		// 获取行的样式类名
		getRowClassName({ row }: { row: TableItem }): string {
			if (!this.isEmbeddingModelMatch(row)) {
				return 'disabled-row';
			}
			return '';
		},
		// 获取 tooltip 内容
		getTooltipContent(row: TableItem): string {
			const model = this.embeddingModels.find(m => m.id == row.embeddingModelId);
			const modelName = model ? `${model.platform}-${model.name}` : '未知模型';
			const selectedModel = this.embeddingModels.find(m => m.id == this.embeddingModelId);
			const selectedModelName = selectedModel ? `${selectedModel.platform}-${selectedModel.name}` : '未知模型';

			return `此知识库的切词模型是 ${modelName}，与当前选中的 ${selectedModelName} 不匹配，建议选择相同切词模型的知识库，以免影响效果`;
		},
		// 获取切词模型列表
		async getEmbeddingModels() {
			try {
				// @ts-ignore
				const resp = await modelApi.getEmbddingModels();
				// @ts-ignore
				if (resp.code === 200) {
					// @ts-ignore
					this.embeddingModels = resp.data;
				}
			} catch (error) {
				console.error('获取切词模型列表失败:', error);
			}
		},
	},
	created() {
		this.getEmbeddingModels();
	},
};
</script>

<style scoped>
.name-cell {
	display: flex;
	align-items: center;
	gap: 8px;
}

.warning-icon {
	color: #e6a23c;
	font-size: 16px;
}

:deep(.disabled-row .el-checkbox) {
	cursor: not-allowed;
}

:deep(.disabled-row .el-checkbox__input.is-disabled .el-checkbox__inner) {
	background-color: #f5f7fa;
	border-color: #e4e7ed;
	cursor: not-allowed;
}
</style>
