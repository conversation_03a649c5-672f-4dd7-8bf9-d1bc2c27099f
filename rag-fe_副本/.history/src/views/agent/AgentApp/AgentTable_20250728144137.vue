<template>
	<div class="knowledge-base-tabs">
		<el-tabs v-model="activeTab" @tab-change="handleTabChange">
			<el-tab-pane
				v-for="tab in tabList"
				:key="tab.name"
				:label="`${tab.label} (${tab.count})`"
				:name="tab.name"
			>
				<el-table
					@selection-change="handleSelectionChange"
					:data="currentTabData"
					ref="tableRef"
				>
					<el-table-column type="selection" width="55" />
					<el-table-column property="name" label="名字" width="300" />
					<el-table-column property="description" label="描述" width="400" />
				</el-table>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script lang="ts">
// @ts-ignore
import { getKDB } from '@/api/BookApi';

interface TableItem {
	name: string;
	description: string;
	[key: string]: any;
}

export default {
	name: 'AgentTable',
	props: {
		groupId: {
			type: Number,
			default: 0
		},
		embeddingModelId: {
			type: [Number, String],
			default: ''
		}
	},
	data() {
		return {
			selectedRows: [] as TableItem[],
			tableData: [] as TableItem[],
			allTableData: [] as TableItem[], // 存储所有数据，用于过滤
			activeTab: '', // 当前激活的tab
			tabList: [] as Array<{name: string, label: string, count: number}>, // tab列表
			tabDataMap: {} as Record<string, TableItem[]>, // 按tableName分组的数据
		};
	},
	computed: {
		// 当前tab的数据
		currentTabData() {
			return this.tabDataMap[this.activeTab] || [];
		}
	},
	watch: {
		groupId: {
			immediate: true,
			handler(newVal) {
				if (newVal && newVal !== 0) {
					this.getTableData();
				}
			}
		},
		embeddingModelId: {
			immediate: true,
			handler() {
				if (this.allTableData && this.allTableData.length > 0) {
					this.tableData = this.filterTableData(this.allTableData);
				}
			}
		}
	},
	methods: {
		getTableData() {
			if (!this.groupId || this.groupId === 0) {
				return;
			}

			getKDB(this.groupId).then((res) => {
				this.allTableData = res.data;
				this.tableData = this.filterTableData(res.data);
			}).catch(error => {
				console.error('获取知识库数据失败:', error);
				this.allTableData = [];
				this.tableData = [];
			});
		},
		filterTableData(data) {
			if (!data || data.length === 0) {
				return [];
			}
			if (!this.embeddingModelId) {
				return data;
			} else {
				// 根据切词模型ID过滤知识库
				const embeddingModelIdNum = typeof this.embeddingModelId === 'string'
					? parseInt(this.embeddingModelId, 10)
					: this.embeddingModelId;

				return data.filter(item =>
					item.embeddingModelId === embeddingModelIdNum
				);
			}
		},
		handleSelectionChange(rows: TableItem[]) {
			this.selectedRows = rows;
			this.$emit('updateValue', this.selectedRows);
			console.log(this.selectedRows);
		},
	},
};
</script>
