<template>
	<div>
		<!-- 功能开关：如果禁用Tab功能，直接显示原始表格 -->
		<div v-if="!enableTabFeature">
			<el-table @selection-change="handleSelectionChange" :data="tableData">
				<el-table-column type="selection" width="55" />
				<el-table-column property="name" label="名字" width="300" />
				<el-table-column property="description" label="描述" width="400" />
			</el-table>
		</div>
		<!-- Tab功能启用时 -->
		<div v-else>
			<!-- 如果只有一个tableName，直接显示表格，不显示tabs -->
			<div v-if="tableNames.length <= 1">
				<el-table
					@selection-change="handleSelectionChange"
					:data="tableData"
					ref="tableRefs"
				>
					<el-table-column type="selection" width="55" />
					<el-table-column property="name" label="名字" width="300" />
					<el-table-column property="description" label="描述" width="400" />
				</el-table>
			</div>
			<!-- 多个tableName时才显示tabs -->
			<div v-else>
				<el-tabs v-model="activeTab" @tab-change="handleTabChange">
					<el-tab-pane
						v-for="tableName in tableNames"
						:key="tableName"
						:label="tableName || 'default'"
						:name="tableName || 'default'"
					>
						<el-table
							@selection-change="handleSelectionChange"
							:data="getTableDataByTab(tableName)"
							ref="tableRefs"
						>
							<el-table-column type="selection" width="55" />
							<el-table-column property="name" label="名字" width="300" />
							<el-table-column property="description" label="描述" width="400" />
						</el-table>
					</el-tab-pane>
				</el-tabs>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
// @ts-ignore
import { getKDB } from '@/api/BookApi';

interface TableItem {
	name: string;
	description: string;
	[key: string]: any;
}

export default {
	name: 'AgentTable',
	props: {
		groupId: {
			type: Number,
			default: 0
		},
		embeddingModelId: {
			type: [Number, String],
			default: ''
		}
	},
	data() {
		return {
			selectedRows: [] as TableItem[],
			tableData: [] as TableItem[],
			allTableData: [] as TableItem[], // 存储所有数据，用于过滤
			activeTab: '', // 当前激活的tab
			tableNames: [] as string[], // 所有的tableName
			selectedTabData: {} as Record<string, TableItem[]>, // 每个tab选中的数据
			enableTabFeature: true, // 功能开关，可以快速禁用Tab功能
		};
	},
	watch: {
		groupId: {
			immediate: true,
			handler(newVal) {
				if (newVal && newVal !== 0) {
					this.getTableData();
				}
			}
		},
		embeddingModelId: {
			immediate: true,
			handler() {
				if (this.allTableData && this.allTableData.length > 0) {
					this.tableData = this.filterTableData(this.allTableData);
					this.initTabs();
				}
			}
		}
	},
	methods: {
		getTableData() {
			if (!this.groupId || this.groupId === 0) {
				return;
			}

			getKDB(this.groupId).then((res) => {
				this.allTableData = res.data;
				this.tableData = this.filterTableData(res.data);
				this.initTabs();
			}).catch(error => {
				console.error('获取知识库数据失败:', error);
				this.allTableData = [];
				this.tableData = [];
				this.tableNames = [];
				this.activeTab = '';
			});
		},
		initTabs() {
			// 获取所有唯一的tableName
			const tableNamesSet = new Set<string>();
			this.tableData.forEach(item => {
				if (item.tableName) {
					// 过滤和清理tableName，防止特殊字符
					const cleanTableName = String(item.tableName).replace(/[^a-zA-Z0-9_-]/g, '');
					if (cleanTableName) {
						tableNamesSet.add(cleanTableName);
					}
				}
			});
			this.tableNames = Array.from(tableNamesSet);

			// 设置默认激活的tab
			if (this.tableNames.length > 0 && !this.activeTab) {
				this.activeTab = this.tableNames[0];
			}

			// 初始化每个tab的选中数据
			this.tableNames.forEach(tableName => {
				if (!this.selectedTabData[tableName]) {
					this.selectedTabData[tableName] = [];
				}
			});
		},
		filterTableData(data) {
			if (!data || data.length === 0) {
				return [];
			}
			if (!this.embeddingModelId) {
				return data;
			} else {
				// 根据切词模型ID过滤知识库
				const embeddingModelIdNum = typeof this.embeddingModelId === 'string'
					? parseInt(this.embeddingModelId, 10)
					: this.embeddingModelId;

				return data.filter(item =>
					item.embeddingModelId === embeddingModelIdNum
				);
			}
		},
		getTableDataByTab(tableName: string) {
			return this.tableData.filter(item => item.tableName === tableName);
		},
		handleTabChange(tabName: string) {
			this.activeTab = tabName;
			// 切换tab时，清除其他tab的选中状态，只保留当前tab的选中数据
			this.selectedRows = this.selectedTabData[tabName] || [];
			this.$emit('updateValue', this.selectedRows);
		},
		handleSelectionChange(rows: TableItem[]) {
			// 保存当前tab的选中数据
			this.selectedTabData[this.activeTab] = rows;
			this.selectedRows = rows;
			this.$emit('updateValue', this.selectedRows);
		},
	},
};
</script>
