<template>
  <el-table @selection-change="handleSelectionChange" :data="tableData">
    <el-table-column type="selection" width="55" />
    <el-table-column property="name" label="名字" width="300" />
    <el-table-column property="description" label="描述" width="400" />
  </el-table>
</template>

<script lang="ts">
// @ts-ignore
import {getKDB} from "@/api/BookApi";

interface TableItem {
  name: string;
  description: string;
  [key: string]: any;
}

export default {
  name: 'AgentTable',
  props: {
    groupId: {
      type: [Number, String],
      default: 0
    },
    embeddingModelId: {
      type: [Number, String],
      default: ''
    }
  },
  data() {
    return {
      selectedRows: [] as TableItem[],
      tableData: [] as TableItem[]
    }
  },
  watch: {
    groupId: {
      immediate: true,
      handler(newVal) {
        if (newVal && newVal !== 0) {
          this.getTableData();
        }
      }
    }
  },
  methods: {
    getTableData() {
      if (!this.groupId || this.groupId === 0) {
        return;
      }

      const safeGroupId = typeof this.groupId === 'string' ? parseInt(this.groupId, 10) : this.groupId;

      getKDB(safeGroupId).then((res) => {
        this.tableData = res.data;
      }).catch(error => {
        console.error('获取知识库数据失败:', error);
        this.tableData = [];
      });
    },
    handleSelectionChange(rows: TableItem[]) {
      this.selectedRows = rows;
      this.$emit('updateValue', this.selectedRows);
    }
  }
};
</script>
