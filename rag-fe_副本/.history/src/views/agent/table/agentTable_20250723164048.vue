<template>
  <el-table @selection-change="handleSelectionChange" :data="tableData">
    <el-table-column type="selection" width="55" />
    <el-table-column property="name" label="名字" width="300" />
    <el-table-column property="description" label="描述" width="400" />
  </el-table>
</template>

<script lang="ts">
// @ts-ignore
import {getKDB} from "@/api/BookApi";

interface TableItem {
  name: string;
  description: string;
  [key: string]: any;
}

export default {
  name: 'AgentTable',
  props: {
    groupId: {
      type: [Number, String],
      default: 0
    },
    embeddingModelId: {
      type: [Number, String],
      default: ''
    }
  },
  data() {
    return {
      selectedRows: [] as TableItem[],
      tableData: [] as TableItem[],
      allTableData: [] as TableItem[] // 存储所有数据，用于过滤
    }
  },
  watch: {
    groupId: {
      immediate: true,
      handler(newVal) {
        if (newVal && newVal !== 0) {
          this.getTableData();
        }
      }
    },
    embeddingModelId: {
      immediate: true,
      handler() {
        // 只有在有数据的情况下才进行过滤
        if (this.allTableData && this.allTableData.length > 0) {
          this.filterTableData();
        }
      }
    }
  },
  methods: {
    getTableData() {
      console.log('=== getTableData called ===');
      console.log('groupId:', this.groupId);
      console.log('embeddingModelId:', this.embeddingModelId);

      if (!this.groupId || this.groupId === 0) {
        return;
      }

      const safeGroupId = typeof this.groupId === 'string' ? parseInt(this.groupId, 10) : this.groupId;

      getKDB(safeGroupId).then((res) => {
        console.log('Knowledge base data loaded:', res.data.length, 'items');
        // this.allTableData = res.data;
         this.allTableData = this.filterTableData();
      }).catch(error => {
        console.error('获取知识库数据失败:', error);
        this.allTableData = [];
        this.tableData = [];
      });
    },
    filterTableData() {
      console.log('=== filterTableData Debug ===');
      console.log('embeddingModelId:', this.embeddingModelId, 'type:', typeof this.embeddingModelId);
      console.log('allTableData length:', this.allTableData.length);

      if (!this.embeddingModelId) {
        // 如果没有选择切词模型，显示所有知识库
        this.tableData = this.allTableData;
        console.log('No embeddingModelId, showing all data');
      } else {
        // 根据切词模型ID过滤知识库
        const embeddingModelIdNum = typeof this.embeddingModelId === 'string'
          ? parseInt(this.embeddingModelId, 10)
          : this.embeddingModelId;

        console.log('Filtering by embeddingModelId:', embeddingModelIdNum);
        console.log('Sample data:', this.allTableData.slice(0, 2));

        this.tableData = this.allTableData.filter(item => {
          const match = item.embeddingModelId === embeddingModelIdNum;
          if (item.embeddingModelId === 4 || embeddingModelIdNum === 4) {
            console.log(`Item "${item.name}" embeddingModelId: ${item.embeddingModelId}, target: ${embeddingModelIdNum}, match: ${match}`);
          }
          return match;
        });

        console.log('Filtered result length:', this.tableData.length);
      }
      console.log('=== End Debug ===');
    },
    handleSelectionChange(rows: TableItem[]) {
      this.selectedRows = rows;
      this.$emit('updateValue', this.selectedRows);
    }
  }
};
</script>
