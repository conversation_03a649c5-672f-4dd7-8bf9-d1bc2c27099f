<!-- eslint-disable -->
<template>
	<div class="table-container">
		<el-table v-loading="loading" :data="tableData" stripe table-layout="auto" style="width: 100%">
			<el-table-column fixed prop="id" label="ID" width="80" align="center" />
			<el-table-column prop="name" label="知识库名称" width="200" show-overflow-tooltip />
			<el-table-column prop="description" label="知识库描述" show-overflow-tooltip />
			<el-table-column label="工作组" show-overflow-tooltip>
				<template #default="{ row }">
					{{ formatGroupInfo(row.groupId) }}
				</template>
			</el-table-column>
			<!-- <el-table-column prop="owner" label="创建人" width="200" align="center" show-overflow-tooltip /> -->
			<el-table-column prop="createAt" label="创建时间" width="180" align="center" />
			<el-table-column label="操作" width="200" align="center" fixed="right">
				<template #default="{ row }">
					<el-button type="primary" size="small" @click="$emit('viewDocs', row.id)" link>查看文档</el-button>
					<el-button v-if="hasEditPermission(row)" type="primary" size="small" @click="$emit('edit', row)" link>
						修改
					</el-button>
					<el-popconfirm
						v-if="hasEditPermission(row)"
						title="确认要删除该条记录吗?"
						@confirm="$emit('delete', row.id)"
					>
						<template #reference>
							<el-button type="danger" size="small" link>删除</el-button>
						</template>
					</el-popconfirm>
				</template>
			</el-table-column>
		</el-table>

		<el-pagination
			v-if="paging.total > 0"
			:current-page="paging.page"
			:page-size="paging.size"
			:total="paging.total"
			:background="true"
			layout="prev, pager, next, jumper, total"
			@update:current-page="$emit('update:page', $event)"
			@update:page-size="$emit('update:size', $event)"
		/>
	</div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import userApi from '@/api/UserApi';
import groupApi from '@/api/GroupApi';
import { ElMessage } from 'element-plus';
import type { Book, Paging } from './types';

const props = defineProps<{
	tableData: Book[];
	loading: boolean;
	paging: Paging;
	workgroups: any[];
}>();

defineEmits(['viewDocs', 'edit', 'delete', 'update:page', 'update:size']);

const allWorkgroups = ref<any[]>([]);
const workgroupsLoading = ref(false);

// 获取所有工作组列表
const fetchAllWorkgroups = async () => {
	workgroupsLoading.value = true;
	try {
		const response = await groupApi.getAllGroupsList();
		if (response?.data) {
			allWorkgroups.value = response.data;
		}
	} catch (error) {
		console.error('获取所有工作组列表错误:', error);
		ElMessage.error('获取所有工作组列表失败');
	} finally {
		workgroupsLoading.value = false;
	}
};

onMounted(() => {
	fetchAllWorkgroups();
});

// 检查是否是管理员（根据owner）
const isAdmin = (owner: string) => userApi.isAdmin(owner);

// 检查是否是该工作组的成员
const isGroupMember = (groupId: number | null) => {
	if (!groupId) return false;
	return props.workgroups.some((group: any) => group.id === groupId);
};

// 检查是否有编辑/删除权限
const hasEditPermission = (row: Book) => {
	// const isOwner = isAdmin(row.owner);

	// 如果是该工作组的成员，则有权限
	const isMember = isGroupMember(row.groupId);
	
	return isMember;
};

const formatGroupInfo = (groupId: number | null) => {
	if (!groupId) {
		return '-';
	}
	
	// 先在用户加入的工作组中查找
	let group = props.workgroups.find((g: any) => g.id === groupId);
	
	// 如果没找到，在所有工作组中查找
	if (!group && allWorkgroups.value.length > 0) {
		group = allWorkgroups.value.find((g: any) => g.id === groupId);
	}
	
	return group ? `${group.id} - ${group.name} (${group.business})` : `${groupId}`;
};
</script>

<style scoped>
.table-container {
	margin-bottom: 20px;
}

.el-pagination {
	margin-top: 20px;
	justify-content: center;
}
</style>
