import { ConfigEnv, defineConfig, loadEnv, UserConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import { viteCommonjs } from '@originjs/vite-plugin-commonjs'
import path from 'path'
import fs from 'fs'

export default defineConfig(({ command, mode }: ConfigEnv): UserConfig => {
    console.log(command, mode, '===')
    const root = process.cwd()
    const env = loadEnv(mode, root) // 环境变量对象
    console.log('环境变量------', env)
    console.log('文件路径（ process.cwd()）------', root)
    console.log('文件路径（dirname）------', __dirname + '/src')
    return {
        plugins: [
            vue(),
            viteCommonjs(),
            AutoImport({
                resolvers: [ElementPlusResolver()],
            }),
            Components({
                resolvers: [ElementPlusResolver()],
            })
        ],
        resolve: {
            alias: {
                '@': path.resolve(__dirname, 'src')
            }
        },
        server: {
            host: true,
            port: 8090,
            open: true,
            cors: false,
            proxy: {
                '/rag/api': {
                    target: 'http://qerag-test.baidu-int.com',
                    changeOrigin: true, // 允许跨域
                    secure: false, // 允许代理 HTTPS，适用于没有可信证书的情况
                    cookieDomainRewrite: "", // 删除 Set-Cookie 头中的 domain 限制
                    followRedirects: true,
                    rewrite: path => path,
                    configure: (proxy) => {
                        proxy.on('proxyReq', (proxyReq, req, res) => {
                            try {
                                const cookieFilePath = path.resolve(__dirname, '.cookie');
                                if (fs.existsSync(cookieFilePath)) {
                                    const cookieContent = fs.readFileSync(cookieFilePath, 'utf8');
                                    if (cookieContent.trim()) {  // 确保文件内容不为空
                                        proxyReq.setHeader('Cookie', cookieContent);
                                    }
                                }
                            } catch (error) {
                                console.log('读取 cookie 文件时出错:', error, '请根目录下创建 .cookie 文件，并写入 cookie 内容，然后继续执行...');
                                // 不抛出错误，继续执行
                            }
                        });
                    }
                },
            },
        },
        build: {
            target: 'modules',
            outDir: 'dist',
            assetsDir: 'assets',
            sourcemap: false,
            minify: 'esbuild',
            chunkSizeWarningLimit: 1000,
            //防止 vite 将 rgba() 颜色转化为 #RGBA 十六进制符号的形式  (要兼容的场景是安卓微信中的 webview 时,它不支持 CSS 中的 #RGBA 十六进制颜色符号)
            cssTarget: 'chrome61'
        },
        define: {
            // 'process.env': env,
            __APP_ENV__: JSON.stringify(mode) // 自定义全局变量
        }
    }
})

