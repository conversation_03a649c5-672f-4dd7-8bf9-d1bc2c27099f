{"name": "rag-fe", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --mode dev", "build": "rm -rf dist.zip && rm -rf dist* &&   vite build --mode online && zip -r dist.zip dist", "build:online": "rm -rf dist.zip && rm -rf dist* &&   vite build --mode online", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@kangc/v-md-editor": "^2.3.18", "@originjs/vite-plugin-commonjs": "^1.0.3", "@types/dompurify": "^3.2.0", "animejs": "^3.2.2", "axios": "^1.7.9", "cron-validator": "^1.3.1", "dompurify": "^3.2.4", "element-plus": "^2.9.3", "highlight.js": "^11.11.1", "json-editor-vue3": "^1.1.1", "lodash": "^4.17.21", "marked": "^15.0.7", "mavon-editor": "^2.10.4", "pinia": "^3.0.1", "qiankun": "^2.10.16", "vue": "^3.5.13", "vue-cookie": "^1.1.4", "vue-json-editor": "^1.4.3", "vue-router": "^4.5.0", "vue3-cookies": "^1.0.6"}, "devDependencies": {"@originjs/vite-plugin-federation": "^1.4.1", "@tsconfig/node22": "^22.0.0", "@types/animejs": "^3.1.13", "@types/marked": "^6.0.0", "@types/node": "^22.10.7", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "charming": "^3.0.2", "eslint": "^9.18.0", "eslint-plugin-vue": "^10.0.0", "jiti": "^2.4.2", "less": "^4.2.2", "npm-run-all2": "^7.0.2", "prettier": "^3.4.2", "typescript": "~5.7.3", "unplugin-auto-import": "^19.0.0", "unplugin-vue-components": "^28.0.0", "vite": "^6.0.11", "vite-plugin-vue-devtools": "^7.7.0", "vue-tsc": "^2.2.0"}}