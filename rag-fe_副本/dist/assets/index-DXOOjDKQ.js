import{_ as P,ai as oe,q as x,o as c,d as A,ac as H,c as f,G as y,w as s,a as i,X as ne,a4 as J,F as q,t as L,V as le,b as Z,r as S,f as re,aj as se,W as ie,ad as ae,ak as de,af as ue,ag as ce,al as me,m as $,n as Q,x as G,a2 as he,Z as pe,am as X,U as fe,an as T,ah as N,a3 as ge}from"./index-BQ_QpmzA.js";/* empty css                  *//* empty css                   *//* empty css                     *//* empty css                    *//* empty css                    *//* empty css               */import{d as K}from"./dict-ClHI1emZ.js";import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css               *//* empty css                     *//* empty css                */import{r as be}from"./renderMarkdown-CDCJAqU1.js";import{b as _e,m as ve}from"./ModelApi-BfpIl2lZ.js";const we={name:"ReactDocumentTable",props:{book:Object,statuses:Array},emits:["getDocuments","getDocumentContent","openDrawer","statusSwitch","remove","routeChange","refresh"],data(){return{microApp:null}},async mounted(){await this.loadReactApp()},beforeUnmount(){this.microApp&&this.microApp.unmount()},watch:{book:{handler(){this.updateMicroAppProps()},deep:!0},statuses:{handler(){this.updateMicroAppProps()},deep:!0}},methods:{async loadReactApp(){try{const e=`${window.location.origin}/micro-apps/react-doc-table/index.html`;this.microApp=oe({name:"react-doc-table",entry:e,container:"#react-doc-table-container",props:this.getMicroAppProps()}),console.log("React微应用加载成功")}catch(t){console.error("React微应用加载失败:",t)}},getMicroAppProps(){return{book:this.book,statuses:this.statuses,onRouteChange:t=>{console.log("ReactDocumentTable: 接收到路由变化事件",t),this.$emit("routeChange",t)},onAddSuccess:()=>{console.log("ReactDocumentTable: 文档添加成功")}}},async updateMicroAppProps(){var t;if(this.microApp)try{const e=this.getMicroAppProps();console.log("ReactDocumentTable: 更新微应用 props，items 长度:",(t=e.items)==null?void 0:t.length),this.microApp.update?this.microApp.update(e):(console.log("ReactDocumentTable: update 方法不可用，重新挂载微应用"),await this.microApp.unmount(),await this.loadReactApp())}catch(e){console.error("ReactDocumentTable: 更新微应用失败:",e),await this.loadReactApp()}else console.log("ReactDocumentTable: 微应用不存在，重新加载"),await this.loadReactApp()}}};function ye(t,e,o,l,r,m){return c(),x("div",null,e[0]||(e[0]=[A("div",{id:"react-doc-table-container"},[A("div",{id:"react-doc-table-root"})],-1)]))}const ee=P(we,[["render",ye],["__scopeId","data-v-af61802d"]]),ke={dict:K,props:{document:{type:Object,required:!0},models:{type:Array,default:()=>[]},showFields:{type:Array,default:()=>["title","sourceUrl","embeddingModelId","parseId","delimiter","chunkTokenNum","cronOpen","cronExpression"]}},data(){return{InfoFilled:H}}};function Ue(t,e,o,l,r,m){const _=ne,h=J,w=ie,b=le,k=ae,U=S("InfoFilled"),E=Z,R=re,D=se;return c(),x("div",null,[o.showFields.includes("title")?(c(),f(h,{key:0,label:"文档标题:",prop:"title","label-width":"120px"},{default:s(()=>[i(_,{type:"text",modelValue:o.document.title,"onUpdate:modelValue":e[0]||(e[0]=a=>o.document.title=a),placeholder:"请输入文档标题",clearable:"",style:{width:"700px"}},null,8,["modelValue"])]),_:1})):y("",!0),o.showFields.includes("sourceUrl")?(c(),f(h,{key:1,label:"URL 链接:",prop:"sourceUrl","label-width":"120px"},{default:s(()=>[i(_,{type:"text",modelValue:o.document.sourceUrl,"onUpdate:modelValue":e[1]||(e[1]=a=>o.document.sourceUrl=a),placeholder:"文档的在线访问地址 URL",clearable:"",style:{width:"700px"}},null,8,["modelValue"])]),_:1})):y("",!0),o.showFields.includes("embeddingModelId")?(c(),f(h,{key:2,label:"切词模型:",prop:"embeddingModelId","label-width":"120px"},{default:s(()=>[i(b,{modelValue:o.document.embeddingModelId,"onUpdate:modelValue":e[2]||(e[2]=a=>o.document.embeddingModelId=a),placeholder:"请选择切词模型",style:{width:"700px"},clearable:""},{default:s(()=>[(c(!0),x(q,null,L(o.models,a=>(c(),f(w,{key:a.id,label:`${a.platform}-${a.name}`,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):y("",!0),o.showFields.includes("parseId")?(c(),f(h,{key:3,label:"切词类型:",prop:"parseId","label-width":"120px"},{default:s(()=>[i(b,{modelValue:o.document.parseId,"onUpdate:modelValue":e[3]||(e[3]=a=>o.document.parseId=a),placeholder:"请选择切词类型",style:{width:"700px"},clearable:""},{default:s(()=>[(c(!0),x(q,null,L(t.$options.dict.parserOptions,a=>(c(),f(w,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):y("",!0),o.showFields.includes("delimiter")?(c(),f(h,{key:4,label:"切片分隔符:",prop:"embeddingRule.delimiter","label-width":"120px"},{default:s(()=>[i(b,{multiple:"",style:{width:"700px"},modelValue:o.document.embeddingRule.delimiter,"onUpdate:modelValue":e[4]||(e[4]=a=>o.document.embeddingRule.delimiter=a),placeholder:"切词分隔符，可以多个，置空表示不做切片"},{default:s(()=>[(c(!0),x(q,null,L(t.$options.dict.delimiters,a=>(c(),f(k,{key:a.label,label:a.label},{default:s(()=>[(c(!0),x(q,null,L(a.options,C=>(c(),f(w,{key:C.value,label:C.label,value:C.value},null,8,["label","value"]))),128))]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"]),i(R,{content:"按照指定的标识符切分文本,可以有多个分割符",placement:"top",effect:"light"},{default:s(()=>[i(E,{style:{"margin-left":"5px"}},{default:s(()=>[i(U)]),_:1})]),_:1})]),_:1})):y("",!0),o.showFields.includes("chunkTokenNum")?(c(),f(h,{key:5,label:"最大切片长:",prop:"embeddingRule.chunkTokenNum","label-width":"120px"},{default:s(()=>[i(_,{type:"text",modelValue:o.document.embeddingRule.chunkTokenNum,"onUpdate:modelValue":e[5]||(e[5]=a=>o.document.embeddingRule.chunkTokenNum=a),placeholder:"最大切片长度",clearable:"",style:{width:"700px"}},null,8,["modelValue"]),i(R,{content:"切片长度越大，召回的上下文越丰富。长度越小，召回的信息越精简",placement:"top",effect:"light"},{default:s(()=>[i(E,{style:{"margin-left":"5px"}},{default:s(()=>[i(U)]),_:1})]),_:1})]),_:1})):y("",!0),o.showFields.includes("cronOpen")?(c(),f(h,{key:6,label:"定时任务:",prop:"cronOpen","label-width":"120px"},{default:s(()=>[i(D,{modelValue:o.document.cronOpen,"onUpdate:modelValue":e[6]||(e[6]=a=>o.document.cronOpen=a),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1})):y("",!0),o.showFields.includes("cronExpression")?(c(),f(h,{key:7,label:"Cron 表达式:",prop:"cronExpression","label-width":"120px",required:o.document.cronOpen===1},{default:s(()=>[i(_,{type:"text",modelValue:o.document.cronExpression,"onUpdate:modelValue":e[7]||(e[7]=a=>o.document.cronExpression=a),placeholder:"请输入Cron表达式，例如: 0 0 12 * * ?",clearable:"",style:{width:"700px"}},null,8,["modelValue"]),i(R,{content:"Cron表达式用于设置定时任务执行时间，格式为: 秒 分 时 日 月 星期",placement:"top",effect:"light"},{default:s(()=>[i(E,{style:{"margin-left":"5px"}},{default:s(()=>[i(U)]),_:1})]),_:1})]),_:1},8,["required"])):y("",!0)])}const Re=P(ke,[["render",Ue]]);var M={},Y;function xe(){if(Y)return M;Y=1;var t=M&&M.__assign||function(){return t=Object.assign||function(n){for(var d,u=1,p=arguments.length;u<p;u++){d=arguments[u];for(var g in d)Object.prototype.hasOwnProperty.call(d,g)&&(n[g]=d[g])}return n},t.apply(this,arguments)};Object.defineProperty(M,"__esModule",{value:!0}),M.isValidCron=void 0;var e=function(n){return/^\d+$/.test(n)?Number(n):NaN},o=function(n){return n==="*"},l=function(n){return n==="?"},r=function(n,d,u){return n>=d&&n<=u},m=function(n,d,u){var p=n.split("-");switch(p.length){case 1:return o(n)||r(e(n),d,u);case 2:var g=p.map(function(F){return e(F)}),v=g[0],B=g[1];return v<=B&&r(v,d,u)&&r(B,d,u);default:return!1}},_=function(n){return n===void 0||n.search(/[^\d]/)===-1&&e(n)>0},h=function(n,d,u){if(n.search(/[^\d-,\/*]/)!==-1)return!1;var p=n.split(",");return p.every(function(g){var v=g.split("/");if(g.trim().endsWith("/")||v.length>2)return!1;var B=v[0],F=v[1];return m(B,d,u)&&_(F)})},w=function(n){return h(n,0,59)},b=function(n){return h(n,0,59)},k=function(n){return h(n,0,23)},U=function(n,d){return d&&l(n)||h(n,1,31)},E={jan:"1",feb:"2",mar:"3",apr:"4",may:"5",jun:"6",jul:"7",aug:"8",sep:"9",oct:"10",nov:"11",dec:"12"},R=function(n,d){if(n.search(/\/[a-zA-Z]/)!==-1)return!1;if(d){var u=n.toLowerCase().replace(/[a-z]{3}/g,function(p){return E[p]===void 0?p:E[p]});return h(u,1,12)}return h(n,1,12)},D={sun:"0",mon:"1",tue:"2",wed:"3",thu:"4",fri:"5",sat:"6"},a=function(n,d,u,p){if(u&&l(n))return!0;if(!u&&l(n)||n.search(/\/[a-zA-Z]/)!==-1)return!1;if(d){var g=n.toLowerCase().replace(/[a-z]{3}/g,function(v){return D[v]===void 0?v:D[v]});return h(g,0,p?7:6)}return h(n,0,p?7:6)},C=function(n,d,u){return!(u&&l(n)&&l(d))},z=function(n){return n.trim().split(/\s+/)},j={alias:!1,seconds:!1,allowBlankDay:!1,allowSevenAsSunday:!1};return M.isValidCron=function(n,d){d=t(t({},j),d);var u=z(n);if(u.length>(d.seconds?6:5)||u.length<5)return!1;var p=[];if(u.length===6){var g=u.shift();g&&p.push(w(g))}var v=u[0],B=u[1],F=u[2],te=u[3],W=u[4];return p.push(b(v)),p.push(k(B)),p.push(U(F,d.allowBlankDay)),p.push(R(te,d.alias)),p.push(a(W,d.alias,d.allowBlankDay,d.allowSevenAsSunday)),p.push(C(F,W,d.allowBlankDay)),p.every(Boolean)},M}var Ee=xe();const Ie={components:{CommonFormItems:Re},dict:K,props:{show:Boolean,title:String,document:Object,files:Array,models:Array,tabActive:String},emits:["update:show","update:tabActive","uploadDocuments","beforeUpload","beforeRemove","editOrCreate"],data(){return{InfoFilled:H,UploadFilled:de,rules:{title:[{required:!0,message:"请输入文档标题",trigger:"blur"}],sourceUrl:[{required:!0,message:"请输入文档链接",trigger:"blur"}],embeddingModelId:[{required:!0,message:"请选择切词模型",trigger:"change"}],parseId:[{required:!0,message:"请选择切词方式",trigger:"change"}],"embeddingRule.delimiter":[{required:!0,message:"请指定切词分隔符",trigger:"change"}],"embeddingRule.chunkTokenNum":[{required:!0,message:"请输入最大切片长度",trigger:"blur"}],cronExpression:[{validator:(e,o,l)=>{if(this.document.cronOpen!==1){l();return}if(!o){l(new Error("请输入Cron表达式"));return}Ee.isValidCron(o,{seconds:!0,alias:!0,allowBlankDay:!0,allowSevenAsSunday:!0})?l():l(new Error("Cron表达式格式错误，请检查语法"))},trigger:"blur"}]}}},computed:{activeTabRules(){var o;if(!this.tabActive)return{};const t={};return(o={upload:["embeddingModelId","parseId","embeddingRule.delimiter","embeddingRule.chunkTokenNum","cronExpression"],link:["title","sourceUrl","embeddingModelId","parseId","embeddingRule.delimiter","embeddingRule.chunkTokenNum","cronExpression"],virtual:["title","embeddingModelId"]}[this.tabActive])==null||o.forEach(l=>{this.rules[l]&&(t[l]=this.rules[l])}),t}},methods:{validateAndSubmit(){if(!this.$refs.documentForm){console.error("表单引用未初始化");return}const t={...this.$refs.documentForm.rules};try{this.$refs.documentForm.rules=this.activeTabRules,this.$refs.documentForm.validate(e=>{this.$refs.documentForm.rules=t,e?this.$emit("editOrCreate"):this.$message.error("请填写当前标签页的必填内容")})}catch(e){console.error("验证错误:",e),this.$refs.documentForm.rules=t}},handleTabChange(t){console.log("Tab changed to:",t),this.$emit("update:tabActive",t)},handleBeforeUpload(t){if(console.log("Before upload:",t.name),!(t.size/1024/1024<50))return this.$message.error("上传文件大小不能超过50MB!"),!1;const o=["doc","docx","txt","pdf","json","xls","xlsx"],l=t.name.split(".").pop().toLowerCase();return o.includes(l)?(this.$emit("beforeUpload",t),!0):(this.$message.error(`不支持的文件类型，请上传${o.join(",")}格式`),!1)},handleUpload(t){console.log("Uploading:",t.file.name);const e=new FormData;e.append("file",t.file),e.append("knowledgeBaseId",this.$route.query.id),this.$emit("uploadDocuments",{file:t.file,formData:e,onProgress:t.onProgress})},handleBeforeRemove(t){this.$emit("beforeRemove",t)}}},Ae={class:"el-upload__text",style:{width:"678px"}},De={class:"dialog-footer"};function Ce(t,e,o,l,r,m){const _=S("upload-filled"),h=Z,w=Q,b=$,k=me,U=J,E=S("CommonFormItems"),R=ce,D=S("common-form-items"),a=ue,C=he,z=pe,j=X;return c(),f(j,{"model-value":o.show,"onUpdate:modelValue":e[1]||(e[1]=n=>t.$emit("update:show",n)),title:o.title,size:"70%"},{footer:s(()=>[A("div",De,[i(z,{onClick:e[0]||(e[0]=n=>t.$emit("update:show",!1))},{default:s(()=>e[4]||(e[4]=[G("取消")])),_:1}),i(z,{type:"primary",onClick:m.validateAndSubmit},{default:s(()=>e[5]||(e[5]=[G("保存")])),_:1},8,["onClick"])])]),default:s(()=>[i(C,{model:o.document,ref:"documentForm",rules:m.activeTabRules,"label-position":"right"},{default:s(()=>[i(a,{"model-value":o.tabActive,"onUpdate:modelValue":m.handleTabChange},{default:s(()=>[!o.document.type||!["link","virtual"].includes(o.document.type)?(c(),f(R,{key:0,label:"文件上传",name:"upload",lazy:!0},{default:s(()=>[i(U,{label:"选择文件：","label-width":"100px"},{default:s(()=>[i(k,{"file-list":o.files,action:"",drag:"","http-request":m.handleUpload,"show-file-list":!0,multiple:!0,limit:5,"before-upload":m.handleBeforeUpload,"on-remove":m.handleBeforeRemove,ref:"uploadRef"},{default:s(()=>[i(h,{class:"el-icon--upload"},{default:s(()=>[i(_)]),_:1}),A("div",Ae,[i(b,null,{default:s(()=>[i(w,{span:24},{default:s(()=>e[2]||(e[2]=[G(" 将文档拖动至此处，或 "),A("em",null,"点击上传",-1)])),_:1})]),_:1}),e[3]||(e[3]=A("span",{style:{color:"#e6a23c"}}," 单次上传文档数量为5个；单个文件小于50M；支持.doc | .txt | .docx | .pdf | .json | .excel ",-1))])]),_:1},8,["file-list","http-request","before-upload","on-remove"])]),_:1}),i(E,{document:o.document,models:o.models,"validate-rules":m.activeTabRules,"show-fields":["embeddingModelId","parseId","delimiter","chunkTokenNum"]},null,8,["document","models","validate-rules"])]),_:1})):y("",!0),!o.document.type||o.document.type==="link"?(c(),f(R,{key:1,label:"知识库",name:"link",lazy:!0},{default:s(()=>[i(D,{document:o.document,models:o.models,"validate-rules":m.activeTabRules,"show-fields":["title","sourceUrl","embeddingModelId","parseId","delimiter","chunkTokenNum","cronOpen","cronExpression"]},null,8,["document","models","validate-rules"])]),_:1})):y("",!0),!o.document.type||o.document.type==="virtual"?(c(),f(R,{key:2,label:"虚拟文档",name:"virtual",lazy:!0},{default:s(()=>[i(D,{document:o.document,models:o.models,"show-fields":["title","embeddingModelId"]},null,8,["document","models"])]),_:1})):y("",!0)]),_:1},8,["model-value","onUpdate:modelValue"])]),_:1},8,["model","rules"])]),_:1},8,["model-value","title"])}const Te=P(Ie,[["render",Ce]]),Me={props:{show:Boolean,documentSlices:Array},data(){return{renderedSlices:[]}},watch:{documentSlices:{immediate:!0,async handler(t){if(t&&t.length){const e=t.map(async o=>({...o,renderedContent:await be(o.content)}));this.renderedSlices=await Promise.all(e)}else this.renderedSlices=[]}}},emits:["update:show"]},Ve=["innerHTML"];function Oe(t,e,o,l,r,m){const _=fe,h=Q,w=$,b=X;return c(),f(b,{class:"slices-container","model-value":o.show,"onUpdate:modelValue":e[0]||(e[0]=k=>t.$emit("update:show",k)),title:"文档切片",size:"60%"},{default:s(()=>[i(w,{class:"table-container"},{default:s(()=>[(c(!0),x(q,null,L(r.renderedSlices,(k,U)=>(c(),f(h,{span:24,key:U,style:{"margin-bottom":"5px"}},{default:s(()=>[i(_,null,{default:s(()=>[A("div",{innerHTML:k.renderedContent},null,8,Ve)]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1},8,["model-value"])}const Be=P(Me,[["render",Oe],["__scopeId","data-v-7a47cc43"]]),V="/rag/api";var I=(t=>(t.QUERY_BY_ID=V+"/document/query/content",t.UPLOAD_FILE=V+"/document/file/upload",t.PAGING_URL=V+"/document/paging",t.DELETE_URL=V+"/document/delete",t.UPDATE_URL=V+"/document/update",t.UPDATE_STATE_URL=V+"/document/status/update",t.ADD_URL=V+"/document/add",t))(I||{});class Fe{getDocumentContent(e){const o={documentId:e,type:0};return T.post(I.QUERY_BY_ID,o)}upload(e){return T.post(I.UPLOAD_FILE,e,{headers:{"Content-Type":"multipart/form-data"}})}getDocuments(e){return T.post(I.PAGING_URL,e)}remove(e){return e.owner=N.loginUser(),T.post(I.DELETE_URL,e)}statusSwitch(e){return e.owner=N.loginUser(),T.put(I.UPDATE_STATE_URL,e)}edit(e){return T.put(I.UPDATE_URL,e)}create(e){return T.post(I.ADD_URL,e)}}const O=new Fe,Se={components:{DocumentDrawer:Te,SlicesDrawer:Be,ReactDocumentTable:ee},data(){return{dataInitialized:!1,tabActive:"upload",files:[],models:[],userGroups:[],config:{slices:{show:!1,title:""},drawer:{show:!1,title:""},loading:!0},statuses:[{key:1,value:"等待切片"},{key:2,value:"切片成功"},{key:3,value:"切片失败"},{key:4,value:"已被禁用"},{key:5,value:"切片中"}],book:{},document:{},documents:[],documentSlices:[],query:{title:"",owner:"",knowledgeBaseId:this.$route.query.id,status:"",page:1,size:10},paging:{items:[],size:10,page:1,total:0}}},async created(){await Promise.all([this.getDocuments(this.query),this.getBook(this.$route.query.id),this.getModels(),this.getUserGroups()]),this.dataInitialized=!0},methods:{handleRouteChange(t){this.$router.push({path:this.$route.path,query:{...this.$route.query,docId:t}})},getDocumentContent(t){this.config.slices.show=!0,O.getDocumentContent(t).then(e=>{e.code===200&&(this.documentSlices=e.data)})},getModels(){return ve.getEmbddingModels().then(t=>{t.code===200&&(this.models=t.data)})},beforeUpload(t){return console.log("Parent beforeUpload:",t.name),this.files.push(t),!0},uploadDocuments({file:t,formData:e,onProgress:o}){console.log("Parent upload:",t.name);const l=new FormData;l.append("files",t),l.append("knowledgeBaseId",this.$route.query.id),O.upload(l,{onUploadProgress:r=>{const m=Math.round(r.loaded*100/r.total);o({percent:m})}}).then(r=>{(r==null?void 0:r.code)===200&&r.data.forEach(m=>{this.documents.push({title:m.title,type:m.type,bosUrl:m.bosUrl,knowledgeBaseId:this.$route.query.id})})}).catch(r=>{console.error("上传失败：",r),this.$message.error("文件上传失败！")})},beforeRemove(t){this.files=this.files.filter(e=>e.uid!==t.uid),this.documents=this.documents.filter(e=>e.title!==t.name)},openDrawer(t,e){const o=N.loginUser(),l=this.book.owner.includes(o);let r=!1;if(this.book.groupId&&(r=this.checkUserInGroup(this.book.groupId)),!l&&!r){this.$message.error("您还不具备当前知识库的管理权限，不可导入文档");return}e?(this.config.drawer.title="修改文档",this.document={...e},this.document.embeddingRule=JSON.parse(this.document.embeddingRule),this.tabActive=this.document.type==="link"?"link":this.document.type==="virtual"?"virtual":"upload",console.log(this.document.type)):(this.document={embeddingRule:{delimiter:this.book.embeddingRule.delimiter,chunkTokenNum:this.book.embeddingRule.chunkTokenNum}},this.document.embeddingModelId=this.book.embeddingModelId,this.document.parseId=this.book.parseId,this.config.drawer.title="导入文档"),this.config.drawer.show=!0},getUserGroups(){return ge.getJoinedList().then(t=>{t.code===200&&(this.userGroups=t.data||[])})},checkUserInGroup(t){return this.userGroups.some(e=>e.id===t)},getBook(t){return _e.getBook(t).then(e=>{e.code===200&&(this.book=e.data,this.book.embeddingRule=JSON.parse(this.book.embeddingRule))})},getDocuments(t){return t&&(this.query={...this.query,...t}),O.getDocuments(this.query).then(e=>{e.code===200&&(this.paging=e.data,this.config.loading=!1)})},statusSwitch(t){if(t.status===2||t.status===4){const e={id:t.id,owner:t.owner,status:t.status===4?2:4};O.statusSwitch(e).then(o=>{o.code===200&&this.getDocuments(this.query)})}else this.$message.error("当前状态不支持操作")},remove(t){const e={id:t,owner:N.loginUser()};return O.remove(e).then(o=>(o.code===200&&this.getDocuments(this.query),o))},handleTabChange(t){this.tabActive=t,console.log("Current active tab:",this.tabActive)},editOrCreate(){const t=()=>{this.getDocuments(),this.files=[],this.documents=[],this.config.drawer.show=!1,this.config.loading=!1},e=l=>{console.error("操作失败:",l),this.$message.error("操作失败，请重试"),this.config.loading=!1};if(this.document.id>0){if(this.documents.length>1){this.$message.error("更新文档只能上传一个文件"),this.config.loading=!1;return}this.documents.length===1&&Object.assign(this.document,{title:this.documents[0].title,type:this.documents[0].type,bosUrl:this.documents[0].bosUrl,cronOpen:this.document.cronOpen||0,cronExpression:this.document.cronExpression}),O.edit(this.document).then(l=>(l==null?void 0:l.code)===200?t():e()).catch(e);return}const o={knowledgeBaseId:this.$route.query.id,owner:N.loginUser(),embeddingRule:this.document.embeddingRule||this.book.embeddingRule,embeddingModelId:this.document.embeddingModelId||this.book.embeddingModelId};switch(this.tabActive){case"upload":if(this.documents.length===0)return this.$message.error("请选择需要导入的文件"),this.config.loading=!1,!1;this.documents.forEach(l=>{l.cronOpen=this.document.cronOpen||0,l.cronExpression=this.document.cronExpression}),o.documents=this.documents;break;case"link":if(!this.document.sourceUrl||!this.document.title)return this.$message.error("请填写文档标题和URL地址"),this.config.loading=!1,!1;o.documents=[{title:this.document.title,type:"link",knowledgeBaseId:this.$route.query.id,sourceUrl:this.document.sourceUrl,cronOpen:this.document.cronOpen||0,cronExpression:this.document.cronExpression}];break;case"virtual":if(!this.document.embeddingModelId||!this.document.title)return this.$message.error("请填写文档标题和模型ID"),this.config.loading=!1,!1;o.documents=[{title:this.document.title,type:"virtual",knowledgeBaseId:this.$route.query.id,embeddingModelId:this.document.embeddingModelId,cronOpen:this.document.cronOpen||0,cronExpression:this.document.cronExpression}];break}O.create(o).then(l=>(l==null?void 0:l.code)===200?t():e()).catch(e)}}},qe={key:1,style:{"text-align":"center",padding:"50px"}};function Le(t,e,o,l,r,m){const _=ee,h=S("document-drawer"),w=S("slices-drawer");return c(),x("div",null,[r.dataInitialized?(c(),f(_,{key:0,book:r.book,statuses:r.statuses,onRouteChange:m.handleRouteChange},null,8,["book","statuses","onRouteChange"])):(c(),x("div",qe,e[2]||(e[2]=[A("div",{style:{"font-size":"16px",color:"#666"}},"加载中...",-1)]))),i(h,{show:r.config.drawer.show,"onUpdate:show":e[0]||(e[0]=b=>r.config.drawer.show=b),title:r.config.drawer.title,document:r.document,files:r.files,models:r.models,"tab-active":r.tabActive,onUploadDocuments:m.uploadDocuments,onBeforeUpload:m.beforeUpload,onBeforeRemove:m.beforeRemove,onEditOrCreate:m.editOrCreate,"onUpdate:tabActive":m.handleTabChange},null,8,["show","title","document","files","models","tab-active","onUploadDocuments","onBeforeUpload","onBeforeRemove","onEditOrCreate","onUpdate:tabActive"]),i(w,{show:r.config.slices.show,"onUpdate:show":e[1]||(e[1]=b=>r.config.slices.show=b),"document-slices":r.documentSlices},null,8,["show","document-slices"])])}const tt=P(Se,[["render",Le],["__scopeId","data-v-4c91308f"]]);export{tt as default};
