/* empty css                  *//* empty css                  *//* empty css                   */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  */import{h as b,a0 as v,aL as R,q as V,o as g,a as s,al as D,w as u,c as L,B as p,aM as T,b as $,g as c,_ as w,an as x,u as C,s as M,d as f,f as O,x as E,y,ab as z,H as F,Z as P}from"./index-BQ_QpmzA.js";/* empty css                    */const q={class:"avatar-container"},G=["src"],H=b({__name:"UploadImage",props:{modelValue:{type:String,default:""},size:{type:Number,default:40}},emits:["update:modelValue"],setup(n,{emit:i}){const _=`${window.location.origin}/rag/api/user/image/upload`,d=n,U=i,t=v(d.modelValue);R(()=>{t.value=d.modelValue});const r=l=>{var e;const o=l.data||((e=l.data)==null?void 0:e.url);console.log("上传成功:",o,l),o?(t.value=o,c.success("头像上传成功"),U("update:modelValue",o)):c.error("上传成功但未返回图片链接")},m=l=>{const o=l.type.startsWith("image/"),e=l.size/1024/1024<2;return o||c.error("只能上传图片格式的头像"),e||c.error("图片大小不能超过 2MB"),o&&e};return(l,o)=>{const e=$,a=D;return g(),V("div",q,[s(a,{class:"avatar-uploader",action:_,"show-file-list":!1,"on-success":r,"before-upload":m},{default:u(()=>[t.value?(g(),V("img",{key:0,src:t.value,class:"avatar"},null,8,G)):(g(),L(e,{key:1,class:"avatar-uploader-icon"},{default:u(()=>[s(p(T))]),_:1}))]),_:1})])}}}),W=w(H,[["__scopeId","data-v-90fa25f6"]]),A="/rag/api";var h=(n=>(n.UPLOAD_IMAGE_USER_URL=A+"/user/image/upload",n.UPDATE_USER_INFO=A+"/user/update",n))(h||{});class Z{uploadImage(i={name:"",hiImageUrl:""}){return x.post(h.UPDATE_USER_INFO,i)}uploadUserInfo(){return x.post(h.UPDATE_USER_INFO)}}const j=new Z,J={class:"user-info"},K={class:"left-content"},Q={class:"right-conent"},X={class:"dialog-footer"},Y=b({__name:"index",setup(n){const i=C(),{imageUrl:_,username:d,departmentName:U}=M(i),t=v(!1),r=v(""),m=v(!1),l=e=>{r.value=e},o=()=>{if(!r.value)return c.warning("请先上传图片");m.value=!0,j.uploadImage({name:d.value,hiImageUrl:r.value}).then(e=>{(e==null?void 0:e.code)===200&&(i.updateImageUrl(r.value),t.value=!1,r.value="",c.success((e==null?void 0:e.message)||"头像更新成功"))}).finally(()=>{m.value=!1})};return(e,a)=>{const k=F,B=O,S=P,N=z;return g(),V("div",J,[f("div",K,[s(B,{content:"点击修改头像",placement:"top"},{default:u(()=>[s(k,{class:"user-icon",src:p(_),size:100,onClick:a[0]||(a[0]=I=>t.value=!0)},null,8,["src"])]),_:1})]),f("div",Q,[f("h2",null,y(p(d)),1),E(" "+y(p(U)),1)]),s(N,{modelValue:t.value,"onUpdate:modelValue":a[2]||(a[2]=I=>t.value=I),title:"上传头像(点击后上传)",width:"400px",center:""},{footer:u(()=>[f("div",X,[s(S,{onClick:a[1]||(a[1]=I=>t.value=!1)},{default:u(()=>a[3]||(a[3]=[E("取消")])),_:1}),s(S,{type:"primary",loading:m.value,onClick:o},{default:u(()=>a[4]||(a[4]=[E("确定")])),_:1},8,["loading"])])]),default:u(()=>[s(W,{modelValue:p(_),"onUpdate:modelValue":l},null,8,["modelValue"])]),_:1},8,["modelValue"])])}}}),re=w(Y,[["__scopeId","data-v-542352b0"]]);export{re as default};
