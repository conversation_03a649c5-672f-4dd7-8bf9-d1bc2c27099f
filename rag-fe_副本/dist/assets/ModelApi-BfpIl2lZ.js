var R=Object.defineProperty;var d=(e,t,a)=>t in e?R(e,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[t]=a;var n=(e,t,a)=>d(e,typeof t!="symbol"?t+"":t,a);import{an as o}from"./index-BQ_QpmzA.js";const r="/rag/api";var s=(e=>(e.QUERY_BY_USER_URL=r+"/knowledge/base/user/query",e.QUERY_BY_GROUP_URL=r+"/knowledge/base/get/in/group",e.GET_DETAIL_URL=r+"/knowledge/base/detail",e.PAGING_URL=r+"/knowledge/base/paging",e.DELETE_URL=r+"/knowledge/base/delete",e.UPDATE_URL=r+"/knowledge/base/update",e.ADD_URL=r+"/knowledge/base/add",e))(s||{});class _{getBook(t){return o.get(s.GET_DETAIL_URL+"?id="+t)}getBooks(t){return o.post(s.PAGING_URL,t)}remove(t){return o.post(s.DELETE_URL,t)}edit(t){return o.put(s.UPDATE_URL,t)}create(t){return o.post(s.ADD_URL,t)}}const l=e=>o.get(`${s.QUERY_BY_GROUP_URL}?groupId=${e??0}`),u=new _,g="/rag/api";var U=(e=>(e.QUERY_URL=g+"/model/query",e))(U||{});class E{constructor(){n(this,"getEmbddingModels",()=>{const t={isDelete:0,type:0};return o.post(U.QUERY_URL,t)});n(this,"getChatModels",()=>{const t={isDelete:0,type:1};return o.post(U.QUERY_URL,t)})}}const i=new E;export{u as b,l as g,i as m};
