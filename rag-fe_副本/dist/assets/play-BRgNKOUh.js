/* empty css                  *//* empty css                  */import{h as D,_ as f,q as u,o as d,c as M,d as R,H as N,aH as T,a as O,r as g,aI as E,aJ as W,a0 as F,a1 as K,$ as G,aK as U,F as b,G as C,t as j}from"./index-BQ_QpmzA.js";const Q=D({name:"ChatMessage",props:{isUser:Boolean,content:String}}),I="/assets/chat-CJS3lwW7.webp",P={class:"stream"},k={key:1,src:I,height:"32px",width:"32px"},V={class:"message-container"},H=["innerHTML"];function q(s,n,i,o,l,p){const r=N;return d(),u("div",P,[s.isUser?(d(),M(r,{key:0,size:32,color:"blue",icon:"UserFilled"})):(d(),u("img",k)),R("div",V,[R("div",{class:T(["message-content",s.isUser?"ask-css":"response-css"]),innerHTML:s.content},null,10,H)])])}const S=f(Q,[["render",q],["__scopeId","data-v-55c94609"]]),Y=D({name:"ChatHistory",components:{ChatMessage:S},props:{conversation:{type:Object,required:!0}}}),J={class:"conversation-item"};function z(s,n,i,o,l,p){const r=g("ChatMessage");return d(),u("div",J,[O(r,{isUser:!0,content:s.conversation.question},null,8,["content"]),O(r,{isUser:!1,content:s.conversation.answer},null,8,["content"])])}const L=f(Y,[["render",z],["__scopeId","data-v-40eac77b"]]);{let s=function(r,e,t){var h;return function(){var c=this,A=arguments,a=function(){h=null,r.apply(c,A)};clearTimeout(h),h=setTimeout(a,e)}},n=function(r,e,t){return typeof t>"u"&&(t=2),parseFloat(Math.min(r+Math.random()*(e-r),e).toFixed(t))},i={width:window.innerWidth,height:window.innerHeight};class o{constructor(e,t,h){this.DOM={},this.options={shapeTypes:["circle","rect","polygon"],shapeColors:["#e07272","#0805b5","#49c6ff","#8bc34a","#1e1e21","#e24e81","#e0cd24"],shapeFill:!0,shapeStrokeWidth:1},Object.assign(this.options,h),this.type=e||this.options.shapeTypes[0],!(this.type!=="random"&&!this.options.types.includes(this.type))&&(this.type==="random"&&(this.type=this.options.shapeTypes[n(0,this.options.shapeTypes.length-1,0)]),this.letterRect=t,this.init())}init(){this.DOM.el=document.createElementNS("http://www.w3.org/2000/svg",this.type),this.DOM.el.style.opacity=0,this.configureShapeType(),this.options.shapeFill?this.DOM.el.setAttribute("fill",this.options.shapeColors[n(0,this.options.shapeColors.length-1,0)]):(this.DOM.el.setAttribute("fill","none"),this.DOM.el.setAttribute("stroke-width",this.options.shapeStrokeWidth),this.DOM.el.setAttribute("stroke",this.options.shapeColors[n(0,this.options.shapeColors.length-1,0)]))}configureShapeType(){if(this.DOM.el.style.transformOrigin=`${this.letterRect.left+this.letterRect.width/2}px ${this.letterRect.top+this.letterRect.height/2}px`,this.type==="circle"){const e=.5*this.letterRect.width;this.DOM.el.setAttribute("r",e),this.DOM.el.setAttribute("cx",this.letterRect.left+this.letterRect.width/2),this.DOM.el.setAttribute("cy",this.letterRect.top+this.letterRect.height/2)}else if(this.type==="rect"){const e=n(.05,.5,3)*this.letterRect.width,t=n(.05,.5,3)*this.letterRect.height;this.DOM.el.setAttribute("width",e),this.DOM.el.setAttribute("height",t),this.DOM.el.setAttribute("x",this.letterRect.left+(this.letterRect.width-e)/2),this.DOM.el.setAttribute("y",this.letterRect.top+(this.letterRect.height-t)/2)}else this.type==="polygon"&&this.DOM.el.setAttribute("points",`${this.letterRect.left} ${this.letterRect.top+this.letterRect.height}, ${this.letterRect.left+this.letterRect.width/2} ${this.letterRect.top+this.letterRect.height-this.letterRect.width}, ${this.letterRect.left+this.letterRect.width} ${this.letterRect.top+this.letterRect.height}`)}onResize(e){this.letterRect=e,this.configureShapeType()}}class l{constructor(e,t,h){this.DOM={},this.DOM.el=e,this.DOM.svg=t,this.options={totalShapes:10},Object.assign(this.options,h),this.resetBoundingClientRect(),this.totalShapes=this.options.totalShapes,this.init(),this.initEvents()}resetBoundingClientRect(){const{offsetTop:e,offsetLeft:t}=this.DOM.el.parentNode,{width:h,height:c}=this.DOM.el.getBoundingClientRect();this.rect={width:h,height:c,left:this.DOM.el.offsetLeft+t,top:this.DOM.el.offsetTop+e}}init(){this.shapes=[];for(let e=0;e<=this.totalShapes-1;++e){const t=new o("random",this.rect,this.options);this.shapes.push(t),this.DOM.svg.appendChild(t.DOM.el)}}initEvents(){window.addEventListener("resize",s(()=>{this.resetBoundingClientRect();for(let e=0;e<=this.totalShapes-1;++e)this.shapes[e].onResize(this.rect)},20))}}class p{constructor(e,t){this.DOM={},this.DOM.el=e,this.options={shapesOnTop:!1},Object.assign(this.options,t),this.init(),this.initEvents()}init(){this.createSVG(),window.charming(this.DOM.el),this.letters=[],Array.from(this.DOM.el.querySelectorAll("span")).forEach(e=>this.letters.push(new l(e,this.DOM.svg,this.options)))}initEvents(){window.addEventListener("resize",s(()=>{i={width:this.DOM.el.parentNode.clientWidth,height:this.DOM.el.parentNode.clientHeight},this.DOM.svg.setAttribute("width",`${i.width}px`),this.DOM.svg.setAttribute("height",`${i.width}px`),this.DOM.svg.setAttribute("viewbox",`0 0 ${i.width} ${i.height}`)},20))}createSVG(){i={width:this.DOM.el.parentNode.clientWidth,height:this.DOM.el.parentNode.clientHeight},this.DOM.svg=document.createElementNS("http://www.w3.org/2000/svg","svg"),this.DOM.svg.setAttribute("class","shapes"),this.DOM.svg.setAttribute("width",`${i.width}px`),this.DOM.svg.setAttribute("height",`${i.width}px`),this.DOM.svg.setAttribute("viewbox",`0 0 ${i.width} ${i.height}`),this.options.shapesOnTop?this.DOM.el.parentNode.insertBefore(this.DOM.svg,this.DOM.el.nextSibling):this.DOM.el.parentNode.insertBefore(this.DOM.svg,this.DOM.el)}show(e){return this.toggle("show",e)}hide(e){return this.toggle("hide",e)}toggle(e="show",t){return new Promise((h,c)=>{const A=()=>{for(let a=0,m=this.letters.length;a<=m-1;++a)this.letters[a].DOM.el.style.opacity=e==="show"?1:0;h()};if(t&&Object.keys(t).length!==0){if(t.shapesAnimationOpts)for(let a=0,m=this.letters.length;a<=m-1;++a){const v=this.letters[a];setTimeout(function(B){return()=>{t.shapesAnimationOpts.targets=B.shapes.map(x=>x.DOM.el),anime.remove(t.shapesAnimationOpts.targets),anime(t.shapesAnimationOpts)}}(v),t.lettersAnimationOpts&&t.lettersAnimationOpts.delay?t.lettersAnimationOpts.delay(v.DOM.el,a):0)}t.lettersAnimationOpts?(t.lettersAnimationOpts.targets=this.letters.map(a=>a.DOM.el),t.lettersAnimationOpts.complete=()=>{if(e==="hide")for(let a=0,m=t.lettersAnimationOpts.targets.length;a<=m-1;++a)t.lettersAnimationOpts.targets[a].style.transform="none";h()},anime(t.lettersAnimationOpts)):A()}else A()})}}window.Word=p,window.randomBetween=n}var w,y;function X(){return y||(y=1,w=function(s,{tagName:n="span",split:i,setClassName:o=function(l){return"char"+l}}={}){s.normalize();let l=1;function p(r){const e=r.parentNode,t=r.nodeValue;(i?i(t):t.split("")).forEach(function(c){const A=document.createElement(n),a=o(l++,c);a&&(A.className=a),A.appendChild(document.createTextNode(c)),A.setAttribute("aria-hidden","true"),e.insertBefore(A,r)}),t.trim()!==""&&e.setAttribute("aria-label",t),e.removeChild(r)}(function r(e){if(e.nodeType===3)return p(e);const t=Array.prototype.slice.call(e.childNodes);if(t.length===1&&t[0].nodeType===3)return p(t[0]);t.forEach(function(c){r(c)})})(s)}),w}var Z=X();const _=E(Z);window.charming=_;const $={name:"indexAction",mounted(){const s=[{options:{shapeColors:["#35c394","#9985ee","#f54665","#4718f5","#f5aa18","#ec4747","#5447ec","#ecb447","#a847ec","#635f65","#6435ea","#295ddc","#9fd7ff","#d65380","#0228f7","#242627"],shapesOnTop:!0},show:{lettersAnimationOpts:{duration:800,delay:()=>anime.random(0,75),easing:"easeInOutExpo",opacity:[0,1],translateY:["-300%","0%"],rotate:()=>[anime.random(-50,50),0]},shapesAnimationOpts:{duration:()=>anime.random(1e3,3e3),delay:(i,o)=>o*20,easing:"easeOutElastic",translateX:i=>{const o=anime.random(-400,400);return i.dataset.tx=o,[0,o]},translateY:i=>{const o=anime.random(-250,250);return i.dataset.ty=o,[0,o]},scale:i=>{const o=randomBetween(.1,.6);return i.dataset.s=o,[o,o]},rotate:()=>anime.random(-90,90),opacity:{value:()=>randomBetween(.3,.6),duration:1e3,easing:"linear"}}}}];class n{constructor(o){this.DOM={},this.DOM.el=o,this.DOM.slides=Array.from(this.DOM.el.querySelectorAll(".slide")),this.DOM.bgs=Array.from(this.DOM.el.querySelectorAll(".slide__bg")),this.DOM.words=Array.from(this.DOM.el.querySelectorAll(".word")),this.current=0,this.words=[],this.DOM.words.forEach((l,p)=>{this.words.push(new Word(l,s[p].options))}),this.isAnimating=!0,this.words[0].show(s[0].show).then(()=>this.isAnimating=!1)}show(){this.isAnimating||(this.isAnimating=!0,this.DOM.slides[0].style.opacity=1,anime({targets:this.DOM.bgs[0],duration:600,easing:[.2,1,.3,1],complete:()=>{this.words[0].show(s[0].show).then(()=>this.isAnimating=!1)}}))}}new n(document.querySelector(".slideshow"))}},tt={class:"demo-1"};function et(s,n,i,o,l,p){return d(),u("div",tt,n[0]||(n[0]=[W('<main data-v-3baa9d05><div class="content" data-v-3baa9d05><div class="slideshow" data-v-3baa9d05><div class="slide" data-v-3baa9d05><div class="slide__bg" data-v-3baa9d05></div><h2 class="word" data-v-3baa9d05>QE-RAG</h2></div></div></div></main>',1)]))}const st=f($,[["render",et],["__scopeId","data-v-3baa9d05"]]),it=D({name:"ChatContainer",components:{ChatHistory:L,ChatMessage:S,IndexAction:st},props:{conversations:{type:Array,default:()=>[]},currentQuestion:String,currentAnswer:String},setup(s){const n=F(null);return K(()=>[s.conversations,s.currentAnswer],()=>{G(()=>{n.value&&(n.value.scrollTop=n.value.scrollHeight)})},{deep:!0}),{container:n}}}),nt={class:"total-css",ref:"container"},ot={key:0,class:"conversation-item"};function rt(s,n,i,o,l,p){const r=g("indexAction"),e=g("ChatHistory"),t=g("ChatMessage");return d(),u("div",nt,[s.conversations.length===0&&!s.currentQuestion?U(s.$slots,"empty",{key:0},()=>[O(r)],!0):(d(),u(b,{key:1},[(d(!0),u(b,null,j(s.conversations,(h,c)=>(d(),M(e,{key:"conv-"+c,conversation:h},null,8,["conversation"]))),128)),s.currentQuestion?(d(),u("div",ot,[O(t,{isUser:!0,content:s.currentQuestion},null,8,["content"]),s.currentAnswer?(d(),M(t,{key:0,isUser:!1,content:s.currentAnswer},null,8,["content"])):C("",!0)])):C("",!0)],64))],512)}const ct=f(it,[["render",rt],["__scopeId","data-v-f420e11b"]]),dt="data:image/png;base64,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";export{ct as C,st as I,dt as _,I as a};
