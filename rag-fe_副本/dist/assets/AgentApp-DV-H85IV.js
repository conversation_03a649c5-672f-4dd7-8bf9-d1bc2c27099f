/* empty css                  *//* empty css                *//* empty css                     *//* empty css                     *//* empty css                  *//* empty css                  *//* empty css                   *//* empty css               */import{_ as Qe,c as on,o as J,w as S,a as m,m as kn,n as Gn,d as P,G as ot,Z as ut,x as De,a3 as Wa,g as $e,q as le,al as Vh,r as he,b as ro,X as Tr,as as $a,V as qa,F as st,t as at,U as lt,W as Ha,at as Pr,a0 as rn,a1 as Ln,au as Mr,av as $h,aw as qh,ax as Hh,ay as Kh,az as zh,i as Mt,aA as Rr,ab as Ka,$ as za,aB as Zh,aC as Yh,a7 as io,a9 as oo,aD as Xh,aE as Jh,y as Dn,f as Qh,h as Za,aF as jh,ap as Ya,Y as e_,af as n_,ag as t_,ar as r_,ao as i_,a2 as o_,a4 as s_,aG as a_,aH as u_}from"./index-BQ_QpmzA.js";import{a as to}from"./AgentApi-Ch0FWouw.js";/* empty css               *//* empty css                    *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";import{_ as l_,C as f_,a as d_}from"./play-BRgNKOUh.js";import{r as ka}from"./renderMarkdown-CDCJAqU1.js";/* empty css                    */import{g as c_,m as Ga}from"./ModelApi-BfpIl2lZ.js";import{m as p_}from"./McpServersApi-CPhbWLge.js";/* empty css                  */const g_={props:{isLoadingFaBu:Boolean,isLoadingUpdate:Boolean},methods:{saveAgent(){this.$emit("save")},updateAgent(){this.$emit("update")}}};function h_(u,f,a,y,b,I){const D=Gn,L=ut,M=kn;return J(),on(M,{class:"upper-row"},{default:S(()=>[m(D,{span:24},{default:S(()=>[m(M,{class:"header-content"},{default:S(()=>[m(D,{span:3,class:"header-title"},{default:S(()=>f[0]||(f[0]=[P("span",null,"我的Agent应用",-1)])),_:1}),m(D,{span:17}),m(D,{span:3,class:"header-actions"},{default:S(()=>[a.isLoadingFaBu?(J(),on(L,{key:0,type:"primary",class:"publish-btn",onClick:I.saveAgent},{default:S(()=>f[1]||(f[1]=[De(" 发布 ")])),_:1},8,["onClick"])):ot("",!0),a.isLoadingUpdate?(J(),on(L,{key:1,type:"primary",class:"publish-btn",onClick:I.updateAgent},{default:S(()=>f[2]||(f[2]=[De(" 更新 ")])),_:1},8,["onClick"])):ot("",!0)]),_:1}),m(D,{span:1})]),_:1})]),_:1})]),_:1})}const __=Qe(g_,[["render",h_],["__scopeId","data-v-11f1cd0e"]]),m_={props:{baseURL:String,imageUrl:String,agentName:String,description:String,groupId:Number},emits:["update:imageUrl","update:agentName","update:description","update:groupId"],data(){return{workgroups:[],allWorkgroups:[],workgroupsLoading:!1,allWorkgroupsLoading:!1,selectedGroupId:null}},watch:{groupId:{immediate:!0,handler(u){u&&(this.selectedGroupId=typeof u=="string"?parseInt(u,10):u,console.log("Updated selectedGroupId from prop:",this.selectedGroupId))}}},computed:{computedImageUrl:{get(){return this.imageUrl},set(u){this.$emit("update:imageUrl",u)}},computedAgentName:{get(){return this.agentName},set(u){this.$emit("update:agentName",u)}},computedDescription:{get(){return this.description},set(u){this.$emit("update:description",u)}},displayWorkgroups(){if(!this.selectedGroupId)return this.workgroups;if(this.workgroups.map(y=>y.id).includes(this.selectedGroupId))return this.workgroups;const a=this.allWorkgroups.find(y=>y.id===this.selectedGroupId);return a?[...this.workgroups,a]:this.workgroups}},methods:{handleAvatarSuccess(u){this.computedImageUrl=u.data},beforeAvatarUpload(u){return u.type!=="image/jpeg"&&u.type!=="image/png"?(this.$message.error("头像图片必须是JPG或PNG格式!"),!1):!0},async fetchWorkgroups(){this.workgroupsLoading=!0;try{const u=await Wa.getJoinedList();this.workgroups=(u==null?void 0:u.data)||[],await this.fetchAllWorkgroups()}catch(u){console.error("获取工作组列表错误:",u),$e.error("获取工作组列表失败")}finally{this.workgroupsLoading=!1}},async fetchAllWorkgroups(){this.allWorkgroupsLoading=!0;try{const u=await Wa.getAllGroupsList();this.allWorkgroups=(u==null?void 0:u.data)||[]}catch(u){console.error("获取所有工作组列表错误:",u)}finally{this.allWorkgroupsLoading=!1}},handleGroupChange(u){this.$emit("update:groupId",u)}},mounted(){this.fetchWorkgroups(),this.groupId&&(this.selectedGroupId=typeof this.groupId=="string"?parseInt(this.groupId,10):this.groupId)}},v_={class:"basic-settings"},w_={class:"settings-content"},S_=["src"];function b_(u,f,a,y,b,I){const D=he("Plus"),L=ro,M=Vh,q=Gn,x=Tr,K=kn,B=$a,F=Ha,Q=qa,k=lt;return J(),le("div",v_,[m(k,{style:{padding:"10px"}},{header:S(()=>f[3]||(f[3]=[P("div",{class:"section-header"},[P("span",{class:"group-title"},"基本信息")],-1)])),default:S(()=>[m(K,null,{default:S(()=>[m(q,null,{default:S(()=>[P("div",w_,[m(K,null,{default:S(()=>[m(q,{span:8},{default:S(()=>[m(M,{class:"avatar-uploader",action:a.baseURL,"show-file-list":!1,"on-success":I.handleAvatarSuccess,"before-upload":I.beforeAvatarUpload},{default:S(()=>[a.imageUrl?(J(),le("img",{key:0,src:a.imageUrl,class:"avatar"},null,8,S_)):(J(),on(L,{key:1,class:"avatar-uploader-icon"},{default:S(()=>[m(D)]),_:1}))]),_:1},8,["action","on-success","before-upload"])]),_:1}),m(q,{span:16},{default:S(()=>[P("div",null,[m(x,{modelValue:I.computedAgentName,"onUpdate:modelValue":f[0]||(f[0]=R=>I.computedAgentName=R),placeholder:"我的应用名"},null,8,["modelValue"]),m(x,{modelValue:I.computedDescription,"onUpdate:modelValue":f[1]||(f[1]=R=>I.computedDescription=R),rows:2,class:"description-input",type:"textarea",placeholder:"应用描述"},null,8,["modelValue"])])]),_:1})]),_:1}),m(B,{type:"success",class:"image-tip"},{default:S(()=>f[4]||(f[4]=[De("图片的长宽比为360*140为最优，其他的会被拉伸哦")])),_:1}),m(K,{class:"owner-row"},{default:S(()=>[m(q,{span:8},{default:S(()=>f[5]||(f[5]=[P("span",{class:"label"},"工作组",-1)])),_:1}),m(q,{span:16},{default:S(()=>[m(Q,{modelValue:b.selectedGroupId,"onUpdate:modelValue":f[2]||(f[2]=R=>b.selectedGroupId=R),placeholder:"请选择工作组",clearable:"","loading-text":"加载中...",loading:b.workgroupsLoading,onChange:I.handleGroupChange,style:{width:"100%"}},{default:S(()=>[(J(!0),le(st,null,at(I.displayWorkgroups,R=>(J(),on(F,{key:R.id,label:`${R.id} - ${R.name} (${R.business})`,value:R.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading","onChange"])]),_:1})]),_:1})])]),_:1})]),_:1})]),_:1})])}const y_=Qe(m_,[["render",b_],["__scopeId","data-v-c3b2e948"]]);var Rt={exports:{}};/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */var C_=Rt.exports,Va;function I_(){return Va||(Va=1,function(u,f){(function(){var a,y="4.17.21",b=200,I="Unsupported core-js use. Try https://npms.io/search?q=ponyfill.",D="Expected a function",L="Invalid `variable` option passed into `_.template`",M="__lodash_hash_undefined__",q=500,x="__lodash_placeholder__",K=1,B=2,F=4,Q=1,k=2,R=1,ye=2,qe=4,Le=8,sn=16,Y=32,mn=64,He=128,En=256,Ot=512,Or=30,Ut="...",_e=800,Pn=16,so=1,Xa=2,Ja=3,Bt=1/0,Vn=9007199254740991,Qa=17976931348623157e292,Nt=NaN,je=**********,ja=je-1,eu=je>>>1,nu=[["ary",He],["bind",R],["bindKey",ye],["curry",Le],["curryRight",sn],["flip",Ot],["partial",Y],["partialRight",mn],["rearg",En]],$n="[object Arguments]",Ft="[object Array]",tu="[object AsyncFunction]",ft="[object Boolean]",dt="[object Date]",ru="[object DOMException]",Wt="[object Error]",kt="[object Function]",ao="[object GeneratorFunction]",Ke="[object Map]",ct="[object Number]",iu="[object Null]",an="[object Object]",uo="[object Promise]",ou="[object Proxy]",pt="[object RegExp]",ze="[object Set]",gt="[object String]",Gt="[object Symbol]",su="[object Undefined]",ht="[object WeakMap]",au="[object WeakSet]",_t="[object ArrayBuffer]",qn="[object DataView]",Ur="[object Float32Array]",Br="[object Float64Array]",Nr="[object Int8Array]",Fr="[object Int16Array]",Wr="[object Int32Array]",kr="[object Uint8Array]",Gr="[object Uint8ClampedArray]",Vr="[object Uint16Array]",$r="[object Uint32Array]",uu=/\b__p \+= '';/g,lu=/\b(__p \+=) '' \+/g,fu=/(__e\(.*?\)|\b__t\)) \+\n'';/g,lo=/&(?:amp|lt|gt|quot|#39);/g,fo=/[&<>"']/g,du=RegExp(lo.source),cu=RegExp(fo.source),pu=/<%-([\s\S]+?)%>/g,gu=/<%([\s\S]+?)%>/g,co=/<%=([\s\S]+?)%>/g,hu=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,_u=/^\w*$/,mu=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,qr=/[\\^$.*+?()[\]{}|]/g,vu=RegExp(qr.source),Hr=/^\s+/,wu=/\s/,Su=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,bu=/\{\n\/\* \[wrapped with (.+)\] \*/,yu=/,? & /,Cu=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,Iu=/[()=,{}\[\]\/\s]/,xu=/\\(\\)?/g,Au=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,po=/\w*$/,Du=/^[-+]0x[0-9a-f]+$/i,Lu=/^0b[01]+$/i,Eu=/^\[object .+?Constructor\]$/,Pu=/^0o[0-7]+$/i,Tu=/^(?:0|[1-9]\d*)$/,Mu=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,Vt=/($^)/,Ru=/['\n\r\u2028\u2029\\]/g,$t="\\ud800-\\udfff",Ou="\\u0300-\\u036f",Uu="\\ufe20-\\ufe2f",Bu="\\u20d0-\\u20ff",go=Ou+Uu+Bu,ho="\\u2700-\\u27bf",_o="a-z\\xdf-\\xf6\\xf8-\\xff",Nu="\\xac\\xb1\\xd7\\xf7",Fu="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",Wu="\\u2000-\\u206f",ku=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",mo="A-Z\\xc0-\\xd6\\xd8-\\xde",vo="\\ufe0e\\ufe0f",wo=Nu+Fu+Wu+ku,Kr="['’]",Gu="["+$t+"]",So="["+wo+"]",qt="["+go+"]",bo="\\d+",Vu="["+ho+"]",yo="["+_o+"]",Co="[^"+$t+wo+bo+ho+_o+mo+"]",zr="\\ud83c[\\udffb-\\udfff]",$u="(?:"+qt+"|"+zr+")",Io="[^"+$t+"]",Zr="(?:\\ud83c[\\udde6-\\uddff]){2}",Yr="[\\ud800-\\udbff][\\udc00-\\udfff]",Hn="["+mo+"]",xo="\\u200d",Ao="(?:"+yo+"|"+Co+")",qu="(?:"+Hn+"|"+Co+")",Do="(?:"+Kr+"(?:d|ll|m|re|s|t|ve))?",Lo="(?:"+Kr+"(?:D|LL|M|RE|S|T|VE))?",Eo=$u+"?",Po="["+vo+"]?",Hu="(?:"+xo+"(?:"+[Io,Zr,Yr].join("|")+")"+Po+Eo+")*",Ku="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",zu="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",To=Po+Eo+Hu,Zu="(?:"+[Vu,Zr,Yr].join("|")+")"+To,Yu="(?:"+[Io+qt+"?",qt,Zr,Yr,Gu].join("|")+")",Xu=RegExp(Kr,"g"),Ju=RegExp(qt,"g"),Xr=RegExp(zr+"(?="+zr+")|"+Yu+To,"g"),Qu=RegExp([Hn+"?"+yo+"+"+Do+"(?="+[So,Hn,"$"].join("|")+")",qu+"+"+Lo+"(?="+[So,Hn+Ao,"$"].join("|")+")",Hn+"?"+Ao+"+"+Do,Hn+"+"+Lo,zu,Ku,bo,Zu].join("|"),"g"),ju=RegExp("["+xo+$t+go+vo+"]"),el=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,nl=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],tl=-1,re={};re[Ur]=re[Br]=re[Nr]=re[Fr]=re[Wr]=re[kr]=re[Gr]=re[Vr]=re[$r]=!0,re[$n]=re[Ft]=re[_t]=re[ft]=re[qn]=re[dt]=re[Wt]=re[kt]=re[Ke]=re[ct]=re[an]=re[pt]=re[ze]=re[gt]=re[ht]=!1;var te={};te[$n]=te[Ft]=te[_t]=te[qn]=te[ft]=te[dt]=te[Ur]=te[Br]=te[Nr]=te[Fr]=te[Wr]=te[Ke]=te[ct]=te[an]=te[pt]=te[ze]=te[gt]=te[Gt]=te[kr]=te[Gr]=te[Vr]=te[$r]=!0,te[Wt]=te[kt]=te[ht]=!1;var rl={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"},il={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},ol={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"},sl={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},al=parseFloat,ul=parseInt,Mo=typeof Pr=="object"&&Pr&&Pr.Object===Object&&Pr,ll=typeof self=="object"&&self&&self.Object===Object&&self,pe=Mo||ll||Function("return this")(),Jr=f&&!f.nodeType&&f,Tn=Jr&&!0&&u&&!u.nodeType&&u,Ro=Tn&&Tn.exports===Jr,Qr=Ro&&Mo.process,Ue=function(){try{var c=Tn&&Tn.require&&Tn.require("util").types;return c||Qr&&Qr.binding&&Qr.binding("util")}catch{}}(),Oo=Ue&&Ue.isArrayBuffer,Uo=Ue&&Ue.isDate,Bo=Ue&&Ue.isMap,No=Ue&&Ue.isRegExp,Fo=Ue&&Ue.isSet,Wo=Ue&&Ue.isTypedArray;function Ee(c,h,g){switch(g.length){case 0:return c.call(h);case 1:return c.call(h,g[0]);case 2:return c.call(h,g[0],g[1]);case 3:return c.call(h,g[0],g[1],g[2])}return c.apply(h,g)}function fl(c,h,g,A){for(var N=-1,X=c==null?0:c.length;++N<X;){var fe=c[N];h(A,fe,g(fe),c)}return A}function Be(c,h){for(var g=-1,A=c==null?0:c.length;++g<A&&h(c[g],g,c)!==!1;);return c}function dl(c,h){for(var g=c==null?0:c.length;g--&&h(c[g],g,c)!==!1;);return c}function ko(c,h){for(var g=-1,A=c==null?0:c.length;++g<A;)if(!h(c[g],g,c))return!1;return!0}function vn(c,h){for(var g=-1,A=c==null?0:c.length,N=0,X=[];++g<A;){var fe=c[g];h(fe,g,c)&&(X[N++]=fe)}return X}function Ht(c,h){var g=c==null?0:c.length;return!!g&&Kn(c,h,0)>-1}function jr(c,h,g){for(var A=-1,N=c==null?0:c.length;++A<N;)if(g(h,c[A]))return!0;return!1}function ie(c,h){for(var g=-1,A=c==null?0:c.length,N=Array(A);++g<A;)N[g]=h(c[g],g,c);return N}function wn(c,h){for(var g=-1,A=h.length,N=c.length;++g<A;)c[N+g]=h[g];return c}function ei(c,h,g,A){var N=-1,X=c==null?0:c.length;for(A&&X&&(g=c[++N]);++N<X;)g=h(g,c[N],N,c);return g}function cl(c,h,g,A){var N=c==null?0:c.length;for(A&&N&&(g=c[--N]);N--;)g=h(g,c[N],N,c);return g}function ni(c,h){for(var g=-1,A=c==null?0:c.length;++g<A;)if(h(c[g],g,c))return!0;return!1}var pl=ti("length");function gl(c){return c.split("")}function hl(c){return c.match(Cu)||[]}function Go(c,h,g){var A;return g(c,function(N,X,fe){if(h(N,X,fe))return A=X,!1}),A}function Kt(c,h,g,A){for(var N=c.length,X=g+(A?1:-1);A?X--:++X<N;)if(h(c[X],X,c))return X;return-1}function Kn(c,h,g){return h===h?Dl(c,h,g):Kt(c,Vo,g)}function _l(c,h,g,A){for(var N=g-1,X=c.length;++N<X;)if(A(c[N],h))return N;return-1}function Vo(c){return c!==c}function $o(c,h){var g=c==null?0:c.length;return g?ii(c,h)/g:Nt}function ti(c){return function(h){return h==null?a:h[c]}}function ri(c){return function(h){return c==null?a:c[h]}}function qo(c,h,g,A,N){return N(c,function(X,fe,ne){g=A?(A=!1,X):h(g,X,fe,ne)}),g}function ml(c,h){var g=c.length;for(c.sort(h);g--;)c[g]=c[g].value;return c}function ii(c,h){for(var g,A=-1,N=c.length;++A<N;){var X=h(c[A]);X!==a&&(g=g===a?X:g+X)}return g}function oi(c,h){for(var g=-1,A=Array(c);++g<c;)A[g]=h(g);return A}function vl(c,h){return ie(h,function(g){return[g,c[g]]})}function Ho(c){return c&&c.slice(0,Yo(c)+1).replace(Hr,"")}function Pe(c){return function(h){return c(h)}}function si(c,h){return ie(h,function(g){return c[g]})}function mt(c,h){return c.has(h)}function Ko(c,h){for(var g=-1,A=c.length;++g<A&&Kn(h,c[g],0)>-1;);return g}function zo(c,h){for(var g=c.length;g--&&Kn(h,c[g],0)>-1;);return g}function wl(c,h){for(var g=c.length,A=0;g--;)c[g]===h&&++A;return A}var Sl=ri(rl),bl=ri(il);function yl(c){return"\\"+sl[c]}function Cl(c,h){return c==null?a:c[h]}function zn(c){return ju.test(c)}function Il(c){return el.test(c)}function xl(c){for(var h,g=[];!(h=c.next()).done;)g.push(h.value);return g}function ai(c){var h=-1,g=Array(c.size);return c.forEach(function(A,N){g[++h]=[N,A]}),g}function Zo(c,h){return function(g){return c(h(g))}}function Sn(c,h){for(var g=-1,A=c.length,N=0,X=[];++g<A;){var fe=c[g];(fe===h||fe===x)&&(c[g]=x,X[N++]=g)}return X}function zt(c){var h=-1,g=Array(c.size);return c.forEach(function(A){g[++h]=A}),g}function Al(c){var h=-1,g=Array(c.size);return c.forEach(function(A){g[++h]=[A,A]}),g}function Dl(c,h,g){for(var A=g-1,N=c.length;++A<N;)if(c[A]===h)return A;return-1}function Ll(c,h,g){for(var A=g+1;A--;)if(c[A]===h)return A;return A}function Zn(c){return zn(c)?Pl(c):pl(c)}function Ze(c){return zn(c)?Tl(c):gl(c)}function Yo(c){for(var h=c.length;h--&&wu.test(c.charAt(h)););return h}var El=ri(ol);function Pl(c){for(var h=Xr.lastIndex=0;Xr.test(c);)++h;return h}function Tl(c){return c.match(Xr)||[]}function Ml(c){return c.match(Qu)||[]}var Rl=function c(h){h=h==null?pe:Yn.defaults(pe.Object(),h,Yn.pick(pe,nl));var g=h.Array,A=h.Date,N=h.Error,X=h.Function,fe=h.Math,ne=h.Object,ui=h.RegExp,Ol=h.String,Ne=h.TypeError,Zt=g.prototype,Ul=X.prototype,Xn=ne.prototype,Yt=h["__core-js_shared__"],Xt=Ul.toString,ee=Xn.hasOwnProperty,Bl=0,Xo=function(){var e=/[^.]+$/.exec(Yt&&Yt.keys&&Yt.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}(),Jt=Xn.toString,Nl=Xt.call(ne),Fl=pe._,Wl=ui("^"+Xt.call(ee).replace(qr,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Qt=Ro?h.Buffer:a,bn=h.Symbol,jt=h.Uint8Array,Jo=Qt?Qt.allocUnsafe:a,er=Zo(ne.getPrototypeOf,ne),Qo=ne.create,jo=Xn.propertyIsEnumerable,nr=Zt.splice,es=bn?bn.isConcatSpreadable:a,vt=bn?bn.iterator:a,Mn=bn?bn.toStringTag:a,tr=function(){try{var e=Nn(ne,"defineProperty");return e({},"",{}),e}catch{}}(),kl=h.clearTimeout!==pe.clearTimeout&&h.clearTimeout,Gl=A&&A.now!==pe.Date.now&&A.now,Vl=h.setTimeout!==pe.setTimeout&&h.setTimeout,rr=fe.ceil,ir=fe.floor,li=ne.getOwnPropertySymbols,$l=Qt?Qt.isBuffer:a,ns=h.isFinite,ql=Zt.join,Hl=Zo(ne.keys,ne),de=fe.max,me=fe.min,Kl=A.now,zl=h.parseInt,ts=fe.random,Zl=Zt.reverse,fi=Nn(h,"DataView"),wt=Nn(h,"Map"),di=Nn(h,"Promise"),Jn=Nn(h,"Set"),St=Nn(h,"WeakMap"),bt=Nn(ne,"create"),or=St&&new St,Qn={},Yl=Fn(fi),Xl=Fn(wt),Jl=Fn(di),Ql=Fn(Jn),jl=Fn(St),sr=bn?bn.prototype:a,yt=sr?sr.valueOf:a,rs=sr?sr.toString:a;function o(e){if(se(e)&&!W(e)&&!(e instanceof z)){if(e instanceof Fe)return e;if(ee.call(e,"__wrapped__"))return ia(e)}return new Fe(e)}var jn=function(){function e(){}return function(n){if(!oe(n))return{};if(Qo)return Qo(n);e.prototype=n;var t=new e;return e.prototype=a,t}}();function ar(){}function Fe(e,n){this.__wrapped__=e,this.__actions__=[],this.__chain__=!!n,this.__index__=0,this.__values__=a}o.templateSettings={escape:pu,evaluate:gu,interpolate:co,variable:"",imports:{_:o}},o.prototype=ar.prototype,o.prototype.constructor=o,Fe.prototype=jn(ar.prototype),Fe.prototype.constructor=Fe;function z(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=je,this.__views__=[]}function ef(){var e=new z(this.__wrapped__);return e.__actions__=Ce(this.__actions__),e.__dir__=this.__dir__,e.__filtered__=this.__filtered__,e.__iteratees__=Ce(this.__iteratees__),e.__takeCount__=this.__takeCount__,e.__views__=Ce(this.__views__),e}function nf(){if(this.__filtered__){var e=new z(this);e.__dir__=-1,e.__filtered__=!0}else e=this.clone(),e.__dir__*=-1;return e}function tf(){var e=this.__wrapped__.value(),n=this.__dir__,t=W(e),r=n<0,i=t?e.length:0,s=hd(0,i,this.__views__),l=s.start,d=s.end,p=d-l,_=r?d:l-1,v=this.__iteratees__,w=v.length,C=0,E=me(p,this.__takeCount__);if(!t||!r&&i==p&&E==p)return Ds(e,this.__actions__);var O=[];e:for(;p--&&C<E;){_+=n;for(var V=-1,U=e[_];++V<w;){var H=v[V],Z=H.iteratee,Re=H.type,be=Z(U);if(Re==Xa)U=be;else if(!be){if(Re==so)continue e;break e}}O[C++]=U}return O}z.prototype=jn(ar.prototype),z.prototype.constructor=z;function Rn(e){var n=-1,t=e==null?0:e.length;for(this.clear();++n<t;){var r=e[n];this.set(r[0],r[1])}}function rf(){this.__data__=bt?bt(null):{},this.size=0}function of(e){var n=this.has(e)&&delete this.__data__[e];return this.size-=n?1:0,n}function sf(e){var n=this.__data__;if(bt){var t=n[e];return t===M?a:t}return ee.call(n,e)?n[e]:a}function af(e){var n=this.__data__;return bt?n[e]!==a:ee.call(n,e)}function uf(e,n){var t=this.__data__;return this.size+=this.has(e)?0:1,t[e]=bt&&n===a?M:n,this}Rn.prototype.clear=rf,Rn.prototype.delete=of,Rn.prototype.get=sf,Rn.prototype.has=af,Rn.prototype.set=uf;function un(e){var n=-1,t=e==null?0:e.length;for(this.clear();++n<t;){var r=e[n];this.set(r[0],r[1])}}function lf(){this.__data__=[],this.size=0}function ff(e){var n=this.__data__,t=ur(n,e);if(t<0)return!1;var r=n.length-1;return t==r?n.pop():nr.call(n,t,1),--this.size,!0}function df(e){var n=this.__data__,t=ur(n,e);return t<0?a:n[t][1]}function cf(e){return ur(this.__data__,e)>-1}function pf(e,n){var t=this.__data__,r=ur(t,e);return r<0?(++this.size,t.push([e,n])):t[r][1]=n,this}un.prototype.clear=lf,un.prototype.delete=ff,un.prototype.get=df,un.prototype.has=cf,un.prototype.set=pf;function ln(e){var n=-1,t=e==null?0:e.length;for(this.clear();++n<t;){var r=e[n];this.set(r[0],r[1])}}function gf(){this.size=0,this.__data__={hash:new Rn,map:new(wt||un),string:new Rn}}function hf(e){var n=Sr(this,e).delete(e);return this.size-=n?1:0,n}function _f(e){return Sr(this,e).get(e)}function mf(e){return Sr(this,e).has(e)}function vf(e,n){var t=Sr(this,e),r=t.size;return t.set(e,n),this.size+=t.size==r?0:1,this}ln.prototype.clear=gf,ln.prototype.delete=hf,ln.prototype.get=_f,ln.prototype.has=mf,ln.prototype.set=vf;function On(e){var n=-1,t=e==null?0:e.length;for(this.__data__=new ln;++n<t;)this.add(e[n])}function wf(e){return this.__data__.set(e,M),this}function Sf(e){return this.__data__.has(e)}On.prototype.add=On.prototype.push=wf,On.prototype.has=Sf;function Ye(e){var n=this.__data__=new un(e);this.size=n.size}function bf(){this.__data__=new un,this.size=0}function yf(e){var n=this.__data__,t=n.delete(e);return this.size=n.size,t}function Cf(e){return this.__data__.get(e)}function If(e){return this.__data__.has(e)}function xf(e,n){var t=this.__data__;if(t instanceof un){var r=t.__data__;if(!wt||r.length<b-1)return r.push([e,n]),this.size=++t.size,this;t=this.__data__=new ln(r)}return t.set(e,n),this.size=t.size,this}Ye.prototype.clear=bf,Ye.prototype.delete=yf,Ye.prototype.get=Cf,Ye.prototype.has=If,Ye.prototype.set=xf;function is(e,n){var t=W(e),r=!t&&Wn(e),i=!t&&!r&&An(e),s=!t&&!r&&!i&&rt(e),l=t||r||i||s,d=l?oi(e.length,Ol):[],p=d.length;for(var _ in e)(n||ee.call(e,_))&&!(l&&(_=="length"||i&&(_=="offset"||_=="parent")||s&&(_=="buffer"||_=="byteLength"||_=="byteOffset")||pn(_,p)))&&d.push(_);return d}function os(e){var n=e.length;return n?e[yi(0,n-1)]:a}function Af(e,n){return br(Ce(e),Un(n,0,e.length))}function Df(e){return br(Ce(e))}function ci(e,n,t){(t!==a&&!Xe(e[n],t)||t===a&&!(n in e))&&fn(e,n,t)}function Ct(e,n,t){var r=e[n];(!(ee.call(e,n)&&Xe(r,t))||t===a&&!(n in e))&&fn(e,n,t)}function ur(e,n){for(var t=e.length;t--;)if(Xe(e[t][0],n))return t;return-1}function Lf(e,n,t,r){return yn(e,function(i,s,l){n(r,i,t(i),l)}),r}function ss(e,n){return e&&nn(n,ce(n),e)}function Ef(e,n){return e&&nn(n,xe(n),e)}function fn(e,n,t){n=="__proto__"&&tr?tr(e,n,{configurable:!0,enumerable:!0,value:t,writable:!0}):e[n]=t}function pi(e,n){for(var t=-1,r=n.length,i=g(r),s=e==null;++t<r;)i[t]=s?a:zi(e,n[t]);return i}function Un(e,n,t){return e===e&&(t!==a&&(e=e<=t?e:t),n!==a&&(e=e>=n?e:n)),e}function We(e,n,t,r,i,s){var l,d=n&K,p=n&B,_=n&F;if(t&&(l=i?t(e,r,i,s):t(e)),l!==a)return l;if(!oe(e))return e;var v=W(e);if(v){if(l=md(e),!d)return Ce(e,l)}else{var w=ve(e),C=w==kt||w==ao;if(An(e))return Ps(e,d);if(w==an||w==$n||C&&!i){if(l=p||C?{}:Ys(e),!d)return p?sd(e,Ef(l,e)):od(e,ss(l,e))}else{if(!te[w])return i?e:{};l=vd(e,w,d)}}s||(s=new Ye);var E=s.get(e);if(E)return E;s.set(e,l),Ia(e)?e.forEach(function(U){l.add(We(U,n,t,U,e,s))}):ya(e)&&e.forEach(function(U,H){l.set(H,We(U,n,t,H,e,s))});var O=_?p?Ri:Mi:p?xe:ce,V=v?a:O(e);return Be(V||e,function(U,H){V&&(H=U,U=e[H]),Ct(l,H,We(U,n,t,H,e,s))}),l}function Pf(e){var n=ce(e);return function(t){return as(t,e,n)}}function as(e,n,t){var r=t.length;if(e==null)return!r;for(e=ne(e);r--;){var i=t[r],s=n[i],l=e[i];if(l===a&&!(i in e)||!s(l))return!1}return!0}function us(e,n,t){if(typeof e!="function")throw new Ne(D);return Pt(function(){e.apply(a,t)},n)}function It(e,n,t,r){var i=-1,s=Ht,l=!0,d=e.length,p=[],_=n.length;if(!d)return p;t&&(n=ie(n,Pe(t))),r?(s=jr,l=!1):n.length>=b&&(s=mt,l=!1,n=new On(n));e:for(;++i<d;){var v=e[i],w=t==null?v:t(v);if(v=r||v!==0?v:0,l&&w===w){for(var C=_;C--;)if(n[C]===w)continue e;p.push(v)}else s(n,w,r)||p.push(v)}return p}var yn=Us(en),ls=Us(hi,!0);function Tf(e,n){var t=!0;return yn(e,function(r,i,s){return t=!!n(r,i,s),t}),t}function lr(e,n,t){for(var r=-1,i=e.length;++r<i;){var s=e[r],l=n(s);if(l!=null&&(d===a?l===l&&!Me(l):t(l,d)))var d=l,p=s}return p}function Mf(e,n,t,r){var i=e.length;for(t=G(t),t<0&&(t=-t>i?0:i+t),r=r===a||r>i?i:G(r),r<0&&(r+=i),r=t>r?0:Aa(r);t<r;)e[t++]=n;return e}function fs(e,n){var t=[];return yn(e,function(r,i,s){n(r,i,s)&&t.push(r)}),t}function ge(e,n,t,r,i){var s=-1,l=e.length;for(t||(t=Sd),i||(i=[]);++s<l;){var d=e[s];n>0&&t(d)?n>1?ge(d,n-1,t,r,i):wn(i,d):r||(i[i.length]=d)}return i}var gi=Bs(),ds=Bs(!0);function en(e,n){return e&&gi(e,n,ce)}function hi(e,n){return e&&ds(e,n,ce)}function fr(e,n){return vn(n,function(t){return gn(e[t])})}function Bn(e,n){n=In(n,e);for(var t=0,r=n.length;e!=null&&t<r;)e=e[tn(n[t++])];return t&&t==r?e:a}function cs(e,n,t){var r=n(e);return W(e)?r:wn(r,t(e))}function we(e){return e==null?e===a?su:iu:Mn&&Mn in ne(e)?gd(e):Dd(e)}function _i(e,n){return e>n}function Rf(e,n){return e!=null&&ee.call(e,n)}function Of(e,n){return e!=null&&n in ne(e)}function Uf(e,n,t){return e>=me(n,t)&&e<de(n,t)}function mi(e,n,t){for(var r=t?jr:Ht,i=e[0].length,s=e.length,l=s,d=g(s),p=1/0,_=[];l--;){var v=e[l];l&&n&&(v=ie(v,Pe(n))),p=me(v.length,p),d[l]=!t&&(n||i>=120&&v.length>=120)?new On(l&&v):a}v=e[0];var w=-1,C=d[0];e:for(;++w<i&&_.length<p;){var E=v[w],O=n?n(E):E;if(E=t||E!==0?E:0,!(C?mt(C,O):r(_,O,t))){for(l=s;--l;){var V=d[l];if(!(V?mt(V,O):r(e[l],O,t)))continue e}C&&C.push(O),_.push(E)}}return _}function Bf(e,n,t,r){return en(e,function(i,s,l){n(r,t(i),s,l)}),r}function xt(e,n,t){n=In(n,e),e=js(e,n);var r=e==null?e:e[tn(Ge(n))];return r==null?a:Ee(r,e,t)}function ps(e){return se(e)&&we(e)==$n}function Nf(e){return se(e)&&we(e)==_t}function Ff(e){return se(e)&&we(e)==dt}function At(e,n,t,r,i){return e===n?!0:e==null||n==null||!se(e)&&!se(n)?e!==e&&n!==n:Wf(e,n,t,r,At,i)}function Wf(e,n,t,r,i,s){var l=W(e),d=W(n),p=l?Ft:ve(e),_=d?Ft:ve(n);p=p==$n?an:p,_=_==$n?an:_;var v=p==an,w=_==an,C=p==_;if(C&&An(e)){if(!An(n))return!1;l=!0,v=!1}if(C&&!v)return s||(s=new Ye),l||rt(e)?Ks(e,n,t,r,i,s):cd(e,n,p,t,r,i,s);if(!(t&Q)){var E=v&&ee.call(e,"__wrapped__"),O=w&&ee.call(n,"__wrapped__");if(E||O){var V=E?e.value():e,U=O?n.value():n;return s||(s=new Ye),i(V,U,t,r,s)}}return C?(s||(s=new Ye),pd(e,n,t,r,i,s)):!1}function kf(e){return se(e)&&ve(e)==Ke}function vi(e,n,t,r){var i=t.length,s=i,l=!r;if(e==null)return!s;for(e=ne(e);i--;){var d=t[i];if(l&&d[2]?d[1]!==e[d[0]]:!(d[0]in e))return!1}for(;++i<s;){d=t[i];var p=d[0],_=e[p],v=d[1];if(l&&d[2]){if(_===a&&!(p in e))return!1}else{var w=new Ye;if(r)var C=r(_,v,p,e,n,w);if(!(C===a?At(v,_,Q|k,r,w):C))return!1}}return!0}function gs(e){if(!oe(e)||yd(e))return!1;var n=gn(e)?Wl:Eu;return n.test(Fn(e))}function Gf(e){return se(e)&&we(e)==pt}function Vf(e){return se(e)&&ve(e)==ze}function $f(e){return se(e)&&Dr(e.length)&&!!re[we(e)]}function hs(e){return typeof e=="function"?e:e==null?Ae:typeof e=="object"?W(e)?vs(e[0],e[1]):ms(e):Na(e)}function wi(e){if(!Et(e))return Hl(e);var n=[];for(var t in ne(e))ee.call(e,t)&&t!="constructor"&&n.push(t);return n}function qf(e){if(!oe(e))return Ad(e);var n=Et(e),t=[];for(var r in e)r=="constructor"&&(n||!ee.call(e,r))||t.push(r);return t}function Si(e,n){return e<n}function _s(e,n){var t=-1,r=Ie(e)?g(e.length):[];return yn(e,function(i,s,l){r[++t]=n(i,s,l)}),r}function ms(e){var n=Ui(e);return n.length==1&&n[0][2]?Js(n[0][0],n[0][1]):function(t){return t===e||vi(t,e,n)}}function vs(e,n){return Ni(e)&&Xs(n)?Js(tn(e),n):function(t){var r=zi(t,e);return r===a&&r===n?Zi(t,e):At(n,r,Q|k)}}function dr(e,n,t,r,i){e!==n&&gi(n,function(s,l){if(i||(i=new Ye),oe(s))Hf(e,n,l,t,dr,r,i);else{var d=r?r(Wi(e,l),s,l+"",e,n,i):a;d===a&&(d=s),ci(e,l,d)}},xe)}function Hf(e,n,t,r,i,s,l){var d=Wi(e,t),p=Wi(n,t),_=l.get(p);if(_){ci(e,t,_);return}var v=s?s(d,p,t+"",e,n,l):a,w=v===a;if(w){var C=W(p),E=!C&&An(p),O=!C&&!E&&rt(p);v=p,C||E||O?W(d)?v=d:ae(d)?v=Ce(d):E?(w=!1,v=Ps(p,!0)):O?(w=!1,v=Ts(p,!0)):v=[]:Tt(p)||Wn(p)?(v=d,Wn(d)?v=Da(d):(!oe(d)||gn(d))&&(v=Ys(p))):w=!1}w&&(l.set(p,v),i(v,p,r,s,l),l.delete(p)),ci(e,t,v)}function ws(e,n){var t=e.length;if(t)return n+=n<0?t:0,pn(n,t)?e[n]:a}function Ss(e,n,t){n.length?n=ie(n,function(s){return W(s)?function(l){return Bn(l,s.length===1?s[0]:s)}:s}):n=[Ae];var r=-1;n=ie(n,Pe(T()));var i=_s(e,function(s,l,d){var p=ie(n,function(_){return _(s)});return{criteria:p,index:++r,value:s}});return ml(i,function(s,l){return id(s,l,t)})}function Kf(e,n){return bs(e,n,function(t,r){return Zi(e,r)})}function bs(e,n,t){for(var r=-1,i=n.length,s={};++r<i;){var l=n[r],d=Bn(e,l);t(d,l)&&Dt(s,In(l,e),d)}return s}function zf(e){return function(n){return Bn(n,e)}}function bi(e,n,t,r){var i=r?_l:Kn,s=-1,l=n.length,d=e;for(e===n&&(n=Ce(n)),t&&(d=ie(e,Pe(t)));++s<l;)for(var p=0,_=n[s],v=t?t(_):_;(p=i(d,v,p,r))>-1;)d!==e&&nr.call(d,p,1),nr.call(e,p,1);return e}function ys(e,n){for(var t=e?n.length:0,r=t-1;t--;){var i=n[t];if(t==r||i!==s){var s=i;pn(i)?nr.call(e,i,1):xi(e,i)}}return e}function yi(e,n){return e+ir(ts()*(n-e+1))}function Zf(e,n,t,r){for(var i=-1,s=de(rr((n-e)/(t||1)),0),l=g(s);s--;)l[r?s:++i]=e,e+=t;return l}function Ci(e,n){var t="";if(!e||n<1||n>Vn)return t;do n%2&&(t+=e),n=ir(n/2),n&&(e+=e);while(n);return t}function $(e,n){return ki(Qs(e,n,Ae),e+"")}function Yf(e){return os(it(e))}function Xf(e,n){var t=it(e);return br(t,Un(n,0,t.length))}function Dt(e,n,t,r){if(!oe(e))return e;n=In(n,e);for(var i=-1,s=n.length,l=s-1,d=e;d!=null&&++i<s;){var p=tn(n[i]),_=t;if(p==="__proto__"||p==="constructor"||p==="prototype")return e;if(i!=l){var v=d[p];_=r?r(v,p,d):a,_===a&&(_=oe(v)?v:pn(n[i+1])?[]:{})}Ct(d,p,_),d=d[p]}return e}var Cs=or?function(e,n){return or.set(e,n),e}:Ae,Jf=tr?function(e,n){return tr(e,"toString",{configurable:!0,enumerable:!1,value:Xi(n),writable:!0})}:Ae;function Qf(e){return br(it(e))}function ke(e,n,t){var r=-1,i=e.length;n<0&&(n=-n>i?0:i+n),t=t>i?i:t,t<0&&(t+=i),i=n>t?0:t-n>>>0,n>>>=0;for(var s=g(i);++r<i;)s[r]=e[r+n];return s}function jf(e,n){var t;return yn(e,function(r,i,s){return t=n(r,i,s),!t}),!!t}function cr(e,n,t){var r=0,i=e==null?r:e.length;if(typeof n=="number"&&n===n&&i<=eu){for(;r<i;){var s=r+i>>>1,l=e[s];l!==null&&!Me(l)&&(t?l<=n:l<n)?r=s+1:i=s}return i}return Ii(e,n,Ae,t)}function Ii(e,n,t,r){var i=0,s=e==null?0:e.length;if(s===0)return 0;n=t(n);for(var l=n!==n,d=n===null,p=Me(n),_=n===a;i<s;){var v=ir((i+s)/2),w=t(e[v]),C=w!==a,E=w===null,O=w===w,V=Me(w);if(l)var U=r||O;else _?U=O&&(r||C):d?U=O&&C&&(r||!E):p?U=O&&C&&!E&&(r||!V):E||V?U=!1:U=r?w<=n:w<n;U?i=v+1:s=v}return me(s,ja)}function Is(e,n){for(var t=-1,r=e.length,i=0,s=[];++t<r;){var l=e[t],d=n?n(l):l;if(!t||!Xe(d,p)){var p=d;s[i++]=l===0?0:l}}return s}function xs(e){return typeof e=="number"?e:Me(e)?Nt:+e}function Te(e){if(typeof e=="string")return e;if(W(e))return ie(e,Te)+"";if(Me(e))return rs?rs.call(e):"";var n=e+"";return n=="0"&&1/e==-1/0?"-0":n}function Cn(e,n,t){var r=-1,i=Ht,s=e.length,l=!0,d=[],p=d;if(t)l=!1,i=jr;else if(s>=b){var _=n?null:fd(e);if(_)return zt(_);l=!1,i=mt,p=new On}else p=n?[]:d;e:for(;++r<s;){var v=e[r],w=n?n(v):v;if(v=t||v!==0?v:0,l&&w===w){for(var C=p.length;C--;)if(p[C]===w)continue e;n&&p.push(w),d.push(v)}else i(p,w,t)||(p!==d&&p.push(w),d.push(v))}return d}function xi(e,n){return n=In(n,e),e=js(e,n),e==null||delete e[tn(Ge(n))]}function As(e,n,t,r){return Dt(e,n,t(Bn(e,n)),r)}function pr(e,n,t,r){for(var i=e.length,s=r?i:-1;(r?s--:++s<i)&&n(e[s],s,e););return t?ke(e,r?0:s,r?s+1:i):ke(e,r?s+1:0,r?i:s)}function Ds(e,n){var t=e;return t instanceof z&&(t=t.value()),ei(n,function(r,i){return i.func.apply(i.thisArg,wn([r],i.args))},t)}function Ai(e,n,t){var r=e.length;if(r<2)return r?Cn(e[0]):[];for(var i=-1,s=g(r);++i<r;)for(var l=e[i],d=-1;++d<r;)d!=i&&(s[i]=It(s[i]||l,e[d],n,t));return Cn(ge(s,1),n,t)}function Ls(e,n,t){for(var r=-1,i=e.length,s=n.length,l={};++r<i;){var d=r<s?n[r]:a;t(l,e[r],d)}return l}function Di(e){return ae(e)?e:[]}function Li(e){return typeof e=="function"?e:Ae}function In(e,n){return W(e)?e:Ni(e,n)?[e]:ra(j(e))}var ed=$;function xn(e,n,t){var r=e.length;return t=t===a?r:t,!n&&t>=r?e:ke(e,n,t)}var Es=kl||function(e){return pe.clearTimeout(e)};function Ps(e,n){if(n)return e.slice();var t=e.length,r=Jo?Jo(t):new e.constructor(t);return e.copy(r),r}function Ei(e){var n=new e.constructor(e.byteLength);return new jt(n).set(new jt(e)),n}function nd(e,n){var t=n?Ei(e.buffer):e.buffer;return new e.constructor(t,e.byteOffset,e.byteLength)}function td(e){var n=new e.constructor(e.source,po.exec(e));return n.lastIndex=e.lastIndex,n}function rd(e){return yt?ne(yt.call(e)):{}}function Ts(e,n){var t=n?Ei(e.buffer):e.buffer;return new e.constructor(t,e.byteOffset,e.length)}function Ms(e,n){if(e!==n){var t=e!==a,r=e===null,i=e===e,s=Me(e),l=n!==a,d=n===null,p=n===n,_=Me(n);if(!d&&!_&&!s&&e>n||s&&l&&p&&!d&&!_||r&&l&&p||!t&&p||!i)return 1;if(!r&&!s&&!_&&e<n||_&&t&&i&&!r&&!s||d&&t&&i||!l&&i||!p)return-1}return 0}function id(e,n,t){for(var r=-1,i=e.criteria,s=n.criteria,l=i.length,d=t.length;++r<l;){var p=Ms(i[r],s[r]);if(p){if(r>=d)return p;var _=t[r];return p*(_=="desc"?-1:1)}}return e.index-n.index}function Rs(e,n,t,r){for(var i=-1,s=e.length,l=t.length,d=-1,p=n.length,_=de(s-l,0),v=g(p+_),w=!r;++d<p;)v[d]=n[d];for(;++i<l;)(w||i<s)&&(v[t[i]]=e[i]);for(;_--;)v[d++]=e[i++];return v}function Os(e,n,t,r){for(var i=-1,s=e.length,l=-1,d=t.length,p=-1,_=n.length,v=de(s-d,0),w=g(v+_),C=!r;++i<v;)w[i]=e[i];for(var E=i;++p<_;)w[E+p]=n[p];for(;++l<d;)(C||i<s)&&(w[E+t[l]]=e[i++]);return w}function Ce(e,n){var t=-1,r=e.length;for(n||(n=g(r));++t<r;)n[t]=e[t];return n}function nn(e,n,t,r){var i=!t;t||(t={});for(var s=-1,l=n.length;++s<l;){var d=n[s],p=r?r(t[d],e[d],d,t,e):a;p===a&&(p=e[d]),i?fn(t,d,p):Ct(t,d,p)}return t}function od(e,n){return nn(e,Bi(e),n)}function sd(e,n){return nn(e,zs(e),n)}function gr(e,n){return function(t,r){var i=W(t)?fl:Lf,s=n?n():{};return i(t,e,T(r,2),s)}}function et(e){return $(function(n,t){var r=-1,i=t.length,s=i>1?t[i-1]:a,l=i>2?t[2]:a;for(s=e.length>3&&typeof s=="function"?(i--,s):a,l&&Se(t[0],t[1],l)&&(s=i<3?a:s,i=1),n=ne(n);++r<i;){var d=t[r];d&&e(n,d,r,s)}return n})}function Us(e,n){return function(t,r){if(t==null)return t;if(!Ie(t))return e(t,r);for(var i=t.length,s=n?i:-1,l=ne(t);(n?s--:++s<i)&&r(l[s],s,l)!==!1;);return t}}function Bs(e){return function(n,t,r){for(var i=-1,s=ne(n),l=r(n),d=l.length;d--;){var p=l[e?d:++i];if(t(s[p],p,s)===!1)break}return n}}function ad(e,n,t){var r=n&R,i=Lt(e);function s(){var l=this&&this!==pe&&this instanceof s?i:e;return l.apply(r?t:this,arguments)}return s}function Ns(e){return function(n){n=j(n);var t=zn(n)?Ze(n):a,r=t?t[0]:n.charAt(0),i=t?xn(t,1).join(""):n.slice(1);return r[e]()+i}}function nt(e){return function(n){return ei(Ua(Oa(n).replace(Xu,"")),e,"")}}function Lt(e){return function(){var n=arguments;switch(n.length){case 0:return new e;case 1:return new e(n[0]);case 2:return new e(n[0],n[1]);case 3:return new e(n[0],n[1],n[2]);case 4:return new e(n[0],n[1],n[2],n[3]);case 5:return new e(n[0],n[1],n[2],n[3],n[4]);case 6:return new e(n[0],n[1],n[2],n[3],n[4],n[5]);case 7:return new e(n[0],n[1],n[2],n[3],n[4],n[5],n[6])}var t=jn(e.prototype),r=e.apply(t,n);return oe(r)?r:t}}function ud(e,n,t){var r=Lt(e);function i(){for(var s=arguments.length,l=g(s),d=s,p=tt(i);d--;)l[d]=arguments[d];var _=s<3&&l[0]!==p&&l[s-1]!==p?[]:Sn(l,p);if(s-=_.length,s<t)return Vs(e,n,hr,i.placeholder,a,l,_,a,a,t-s);var v=this&&this!==pe&&this instanceof i?r:e;return Ee(v,this,l)}return i}function Fs(e){return function(n,t,r){var i=ne(n);if(!Ie(n)){var s=T(t,3);n=ce(n),t=function(d){return s(i[d],d,i)}}var l=e(n,t,r);return l>-1?i[s?n[l]:l]:a}}function Ws(e){return cn(function(n){var t=n.length,r=t,i=Fe.prototype.thru;for(e&&n.reverse();r--;){var s=n[r];if(typeof s!="function")throw new Ne(D);if(i&&!l&&wr(s)=="wrapper")var l=new Fe([],!0)}for(r=l?r:t;++r<t;){s=n[r];var d=wr(s),p=d=="wrapper"?Oi(s):a;p&&Fi(p[0])&&p[1]==(He|Le|Y|En)&&!p[4].length&&p[9]==1?l=l[wr(p[0])].apply(l,p[3]):l=s.length==1&&Fi(s)?l[d]():l.thru(s)}return function(){var _=arguments,v=_[0];if(l&&_.length==1&&W(v))return l.plant(v).value();for(var w=0,C=t?n[w].apply(this,_):v;++w<t;)C=n[w].call(this,C);return C}})}function hr(e,n,t,r,i,s,l,d,p,_){var v=n&He,w=n&R,C=n&ye,E=n&(Le|sn),O=n&Ot,V=C?a:Lt(e);function U(){for(var H=arguments.length,Z=g(H),Re=H;Re--;)Z[Re]=arguments[Re];if(E)var be=tt(U),Oe=wl(Z,be);if(r&&(Z=Rs(Z,r,i,E)),s&&(Z=Os(Z,s,l,E)),H-=Oe,E&&H<_){var ue=Sn(Z,be);return Vs(e,n,hr,U.placeholder,t,Z,ue,d,p,_-H)}var Je=w?t:this,_n=C?Je[e]:e;return H=Z.length,d?Z=Ld(Z,d):O&&H>1&&Z.reverse(),v&&p<H&&(Z.length=p),this&&this!==pe&&this instanceof U&&(_n=V||Lt(_n)),_n.apply(Je,Z)}return U}function ks(e,n){return function(t,r){return Bf(t,e,n(r),{})}}function _r(e,n){return function(t,r){var i;if(t===a&&r===a)return n;if(t!==a&&(i=t),r!==a){if(i===a)return r;typeof t=="string"||typeof r=="string"?(t=Te(t),r=Te(r)):(t=xs(t),r=xs(r)),i=e(t,r)}return i}}function Pi(e){return cn(function(n){return n=ie(n,Pe(T())),$(function(t){var r=this;return e(n,function(i){return Ee(i,r,t)})})})}function mr(e,n){n=n===a?" ":Te(n);var t=n.length;if(t<2)return t?Ci(n,e):n;var r=Ci(n,rr(e/Zn(n)));return zn(n)?xn(Ze(r),0,e).join(""):r.slice(0,e)}function ld(e,n,t,r){var i=n&R,s=Lt(e);function l(){for(var d=-1,p=arguments.length,_=-1,v=r.length,w=g(v+p),C=this&&this!==pe&&this instanceof l?s:e;++_<v;)w[_]=r[_];for(;p--;)w[_++]=arguments[++d];return Ee(C,i?t:this,w)}return l}function Gs(e){return function(n,t,r){return r&&typeof r!="number"&&Se(n,t,r)&&(t=r=a),n=hn(n),t===a?(t=n,n=0):t=hn(t),r=r===a?n<t?1:-1:hn(r),Zf(n,t,r,e)}}function vr(e){return function(n,t){return typeof n=="string"&&typeof t=="string"||(n=Ve(n),t=Ve(t)),e(n,t)}}function Vs(e,n,t,r,i,s,l,d,p,_){var v=n&Le,w=v?l:a,C=v?a:l,E=v?s:a,O=v?a:s;n|=v?Y:mn,n&=~(v?mn:Y),n&qe||(n&=-4);var V=[e,n,i,E,w,O,C,d,p,_],U=t.apply(a,V);return Fi(e)&&ea(U,V),U.placeholder=r,na(U,e,n)}function Ti(e){var n=fe[e];return function(t,r){if(t=Ve(t),r=r==null?0:me(G(r),292),r&&ns(t)){var i=(j(t)+"e").split("e"),s=n(i[0]+"e"+(+i[1]+r));return i=(j(s)+"e").split("e"),+(i[0]+"e"+(+i[1]-r))}return n(t)}}var fd=Jn&&1/zt(new Jn([,-0]))[1]==Bt?function(e){return new Jn(e)}:ji;function $s(e){return function(n){var t=ve(n);return t==Ke?ai(n):t==ze?Al(n):vl(n,e(n))}}function dn(e,n,t,r,i,s,l,d){var p=n&ye;if(!p&&typeof e!="function")throw new Ne(D);var _=r?r.length:0;if(_||(n&=-97,r=i=a),l=l===a?l:de(G(l),0),d=d===a?d:G(d),_-=i?i.length:0,n&mn){var v=r,w=i;r=i=a}var C=p?a:Oi(e),E=[e,n,t,r,i,v,w,s,l,d];if(C&&xd(E,C),e=E[0],n=E[1],t=E[2],r=E[3],i=E[4],d=E[9]=E[9]===a?p?0:e.length:de(E[9]-_,0),!d&&n&(Le|sn)&&(n&=-25),!n||n==R)var O=ad(e,n,t);else n==Le||n==sn?O=ud(e,n,d):(n==Y||n==(R|Y))&&!i.length?O=ld(e,n,t,r):O=hr.apply(a,E);var V=C?Cs:ea;return na(V(O,E),e,n)}function qs(e,n,t,r){return e===a||Xe(e,Xn[t])&&!ee.call(r,t)?n:e}function Hs(e,n,t,r,i,s){return oe(e)&&oe(n)&&(s.set(n,e),dr(e,n,a,Hs,s),s.delete(n)),e}function dd(e){return Tt(e)?a:e}function Ks(e,n,t,r,i,s){var l=t&Q,d=e.length,p=n.length;if(d!=p&&!(l&&p>d))return!1;var _=s.get(e),v=s.get(n);if(_&&v)return _==n&&v==e;var w=-1,C=!0,E=t&k?new On:a;for(s.set(e,n),s.set(n,e);++w<d;){var O=e[w],V=n[w];if(r)var U=l?r(V,O,w,n,e,s):r(O,V,w,e,n,s);if(U!==a){if(U)continue;C=!1;break}if(E){if(!ni(n,function(H,Z){if(!mt(E,Z)&&(O===H||i(O,H,t,r,s)))return E.push(Z)})){C=!1;break}}else if(!(O===V||i(O,V,t,r,s))){C=!1;break}}return s.delete(e),s.delete(n),C}function cd(e,n,t,r,i,s,l){switch(t){case qn:if(e.byteLength!=n.byteLength||e.byteOffset!=n.byteOffset)return!1;e=e.buffer,n=n.buffer;case _t:return!(e.byteLength!=n.byteLength||!s(new jt(e),new jt(n)));case ft:case dt:case ct:return Xe(+e,+n);case Wt:return e.name==n.name&&e.message==n.message;case pt:case gt:return e==n+"";case Ke:var d=ai;case ze:var p=r&Q;if(d||(d=zt),e.size!=n.size&&!p)return!1;var _=l.get(e);if(_)return _==n;r|=k,l.set(e,n);var v=Ks(d(e),d(n),r,i,s,l);return l.delete(e),v;case Gt:if(yt)return yt.call(e)==yt.call(n)}return!1}function pd(e,n,t,r,i,s){var l=t&Q,d=Mi(e),p=d.length,_=Mi(n),v=_.length;if(p!=v&&!l)return!1;for(var w=p;w--;){var C=d[w];if(!(l?C in n:ee.call(n,C)))return!1}var E=s.get(e),O=s.get(n);if(E&&O)return E==n&&O==e;var V=!0;s.set(e,n),s.set(n,e);for(var U=l;++w<p;){C=d[w];var H=e[C],Z=n[C];if(r)var Re=l?r(Z,H,C,n,e,s):r(H,Z,C,e,n,s);if(!(Re===a?H===Z||i(H,Z,t,r,s):Re)){V=!1;break}U||(U=C=="constructor")}if(V&&!U){var be=e.constructor,Oe=n.constructor;be!=Oe&&"constructor"in e&&"constructor"in n&&!(typeof be=="function"&&be instanceof be&&typeof Oe=="function"&&Oe instanceof Oe)&&(V=!1)}return s.delete(e),s.delete(n),V}function cn(e){return ki(Qs(e,a,aa),e+"")}function Mi(e){return cs(e,ce,Bi)}function Ri(e){return cs(e,xe,zs)}var Oi=or?function(e){return or.get(e)}:ji;function wr(e){for(var n=e.name+"",t=Qn[n],r=ee.call(Qn,n)?t.length:0;r--;){var i=t[r],s=i.func;if(s==null||s==e)return i.name}return n}function tt(e){var n=ee.call(o,"placeholder")?o:e;return n.placeholder}function T(){var e=o.iteratee||Ji;return e=e===Ji?hs:e,arguments.length?e(arguments[0],arguments[1]):e}function Sr(e,n){var t=e.__data__;return bd(n)?t[typeof n=="string"?"string":"hash"]:t.map}function Ui(e){for(var n=ce(e),t=n.length;t--;){var r=n[t],i=e[r];n[t]=[r,i,Xs(i)]}return n}function Nn(e,n){var t=Cl(e,n);return gs(t)?t:a}function gd(e){var n=ee.call(e,Mn),t=e[Mn];try{e[Mn]=a;var r=!0}catch{}var i=Jt.call(e);return r&&(n?e[Mn]=t:delete e[Mn]),i}var Bi=li?function(e){return e==null?[]:(e=ne(e),vn(li(e),function(n){return jo.call(e,n)}))}:eo,zs=li?function(e){for(var n=[];e;)wn(n,Bi(e)),e=er(e);return n}:eo,ve=we;(fi&&ve(new fi(new ArrayBuffer(1)))!=qn||wt&&ve(new wt)!=Ke||di&&ve(di.resolve())!=uo||Jn&&ve(new Jn)!=ze||St&&ve(new St)!=ht)&&(ve=function(e){var n=we(e),t=n==an?e.constructor:a,r=t?Fn(t):"";if(r)switch(r){case Yl:return qn;case Xl:return Ke;case Jl:return uo;case Ql:return ze;case jl:return ht}return n});function hd(e,n,t){for(var r=-1,i=t.length;++r<i;){var s=t[r],l=s.size;switch(s.type){case"drop":e+=l;break;case"dropRight":n-=l;break;case"take":n=me(n,e+l);break;case"takeRight":e=de(e,n-l);break}}return{start:e,end:n}}function _d(e){var n=e.match(bu);return n?n[1].split(yu):[]}function Zs(e,n,t){n=In(n,e);for(var r=-1,i=n.length,s=!1;++r<i;){var l=tn(n[r]);if(!(s=e!=null&&t(e,l)))break;e=e[l]}return s||++r!=i?s:(i=e==null?0:e.length,!!i&&Dr(i)&&pn(l,i)&&(W(e)||Wn(e)))}function md(e){var n=e.length,t=new e.constructor(n);return n&&typeof e[0]=="string"&&ee.call(e,"index")&&(t.index=e.index,t.input=e.input),t}function Ys(e){return typeof e.constructor=="function"&&!Et(e)?jn(er(e)):{}}function vd(e,n,t){var r=e.constructor;switch(n){case _t:return Ei(e);case ft:case dt:return new r(+e);case qn:return nd(e,t);case Ur:case Br:case Nr:case Fr:case Wr:case kr:case Gr:case Vr:case $r:return Ts(e,t);case Ke:return new r;case ct:case gt:return new r(e);case pt:return td(e);case ze:return new r;case Gt:return rd(e)}}function wd(e,n){var t=n.length;if(!t)return e;var r=t-1;return n[r]=(t>1?"& ":"")+n[r],n=n.join(t>2?", ":" "),e.replace(Su,`{
/* [wrapped with `+n+`] */
`)}function Sd(e){return W(e)||Wn(e)||!!(es&&e&&e[es])}function pn(e,n){var t=typeof e;return n=n??Vn,!!n&&(t=="number"||t!="symbol"&&Tu.test(e))&&e>-1&&e%1==0&&e<n}function Se(e,n,t){if(!oe(t))return!1;var r=typeof n;return(r=="number"?Ie(t)&&pn(n,t.length):r=="string"&&n in t)?Xe(t[n],e):!1}function Ni(e,n){if(W(e))return!1;var t=typeof e;return t=="number"||t=="symbol"||t=="boolean"||e==null||Me(e)?!0:_u.test(e)||!hu.test(e)||n!=null&&e in ne(n)}function bd(e){var n=typeof e;return n=="string"||n=="number"||n=="symbol"||n=="boolean"?e!=="__proto__":e===null}function Fi(e){var n=wr(e),t=o[n];if(typeof t!="function"||!(n in z.prototype))return!1;if(e===t)return!0;var r=Oi(t);return!!r&&e===r[0]}function yd(e){return!!Xo&&Xo in e}var Cd=Yt?gn:no;function Et(e){var n=e&&e.constructor,t=typeof n=="function"&&n.prototype||Xn;return e===t}function Xs(e){return e===e&&!oe(e)}function Js(e,n){return function(t){return t==null?!1:t[e]===n&&(n!==a||e in ne(t))}}function Id(e){var n=xr(e,function(r){return t.size===q&&t.clear(),r}),t=n.cache;return n}function xd(e,n){var t=e[1],r=n[1],i=t|r,s=i<(R|ye|He),l=r==He&&t==Le||r==He&&t==En&&e[7].length<=n[8]||r==(He|En)&&n[7].length<=n[8]&&t==Le;if(!(s||l))return e;r&R&&(e[2]=n[2],i|=t&R?0:qe);var d=n[3];if(d){var p=e[3];e[3]=p?Rs(p,d,n[4]):d,e[4]=p?Sn(e[3],x):n[4]}return d=n[5],d&&(p=e[5],e[5]=p?Os(p,d,n[6]):d,e[6]=p?Sn(e[5],x):n[6]),d=n[7],d&&(e[7]=d),r&He&&(e[8]=e[8]==null?n[8]:me(e[8],n[8])),e[9]==null&&(e[9]=n[9]),e[0]=n[0],e[1]=i,e}function Ad(e){var n=[];if(e!=null)for(var t in ne(e))n.push(t);return n}function Dd(e){return Jt.call(e)}function Qs(e,n,t){return n=de(n===a?e.length-1:n,0),function(){for(var r=arguments,i=-1,s=de(r.length-n,0),l=g(s);++i<s;)l[i]=r[n+i];i=-1;for(var d=g(n+1);++i<n;)d[i]=r[i];return d[n]=t(l),Ee(e,this,d)}}function js(e,n){return n.length<2?e:Bn(e,ke(n,0,-1))}function Ld(e,n){for(var t=e.length,r=me(n.length,t),i=Ce(e);r--;){var s=n[r];e[r]=pn(s,t)?i[s]:a}return e}function Wi(e,n){if(!(n==="constructor"&&typeof e[n]=="function")&&n!="__proto__")return e[n]}var ea=ta(Cs),Pt=Vl||function(e,n){return pe.setTimeout(e,n)},ki=ta(Jf);function na(e,n,t){var r=n+"";return ki(e,wd(r,Ed(_d(r),t)))}function ta(e){var n=0,t=0;return function(){var r=Kl(),i=Pn-(r-t);if(t=r,i>0){if(++n>=_e)return arguments[0]}else n=0;return e.apply(a,arguments)}}function br(e,n){var t=-1,r=e.length,i=r-1;for(n=n===a?r:n;++t<n;){var s=yi(t,i),l=e[s];e[s]=e[t],e[t]=l}return e.length=n,e}var ra=Id(function(e){var n=[];return e.charCodeAt(0)===46&&n.push(""),e.replace(mu,function(t,r,i,s){n.push(i?s.replace(xu,"$1"):r||t)}),n});function tn(e){if(typeof e=="string"||Me(e))return e;var n=e+"";return n=="0"&&1/e==-1/0?"-0":n}function Fn(e){if(e!=null){try{return Xt.call(e)}catch{}try{return e+""}catch{}}return""}function Ed(e,n){return Be(nu,function(t){var r="_."+t[0];n&t[1]&&!Ht(e,r)&&e.push(r)}),e.sort()}function ia(e){if(e instanceof z)return e.clone();var n=new Fe(e.__wrapped__,e.__chain__);return n.__actions__=Ce(e.__actions__),n.__index__=e.__index__,n.__values__=e.__values__,n}function Pd(e,n,t){(t?Se(e,n,t):n===a)?n=1:n=de(G(n),0);var r=e==null?0:e.length;if(!r||n<1)return[];for(var i=0,s=0,l=g(rr(r/n));i<r;)l[s++]=ke(e,i,i+=n);return l}function Td(e){for(var n=-1,t=e==null?0:e.length,r=0,i=[];++n<t;){var s=e[n];s&&(i[r++]=s)}return i}function Md(){var e=arguments.length;if(!e)return[];for(var n=g(e-1),t=arguments[0],r=e;r--;)n[r-1]=arguments[r];return wn(W(t)?Ce(t):[t],ge(n,1))}var Rd=$(function(e,n){return ae(e)?It(e,ge(n,1,ae,!0)):[]}),Od=$(function(e,n){var t=Ge(n);return ae(t)&&(t=a),ae(e)?It(e,ge(n,1,ae,!0),T(t,2)):[]}),Ud=$(function(e,n){var t=Ge(n);return ae(t)&&(t=a),ae(e)?It(e,ge(n,1,ae,!0),a,t):[]});function Bd(e,n,t){var r=e==null?0:e.length;return r?(n=t||n===a?1:G(n),ke(e,n<0?0:n,r)):[]}function Nd(e,n,t){var r=e==null?0:e.length;return r?(n=t||n===a?1:G(n),n=r-n,ke(e,0,n<0?0:n)):[]}function Fd(e,n){return e&&e.length?pr(e,T(n,3),!0,!0):[]}function Wd(e,n){return e&&e.length?pr(e,T(n,3),!0):[]}function kd(e,n,t,r){var i=e==null?0:e.length;return i?(t&&typeof t!="number"&&Se(e,n,t)&&(t=0,r=i),Mf(e,n,t,r)):[]}function oa(e,n,t){var r=e==null?0:e.length;if(!r)return-1;var i=t==null?0:G(t);return i<0&&(i=de(r+i,0)),Kt(e,T(n,3),i)}function sa(e,n,t){var r=e==null?0:e.length;if(!r)return-1;var i=r-1;return t!==a&&(i=G(t),i=t<0?de(r+i,0):me(i,r-1)),Kt(e,T(n,3),i,!0)}function aa(e){var n=e==null?0:e.length;return n?ge(e,1):[]}function Gd(e){var n=e==null?0:e.length;return n?ge(e,Bt):[]}function Vd(e,n){var t=e==null?0:e.length;return t?(n=n===a?1:G(n),ge(e,n)):[]}function $d(e){for(var n=-1,t=e==null?0:e.length,r={};++n<t;){var i=e[n];r[i[0]]=i[1]}return r}function ua(e){return e&&e.length?e[0]:a}function qd(e,n,t){var r=e==null?0:e.length;if(!r)return-1;var i=t==null?0:G(t);return i<0&&(i=de(r+i,0)),Kn(e,n,i)}function Hd(e){var n=e==null?0:e.length;return n?ke(e,0,-1):[]}var Kd=$(function(e){var n=ie(e,Di);return n.length&&n[0]===e[0]?mi(n):[]}),zd=$(function(e){var n=Ge(e),t=ie(e,Di);return n===Ge(t)?n=a:t.pop(),t.length&&t[0]===e[0]?mi(t,T(n,2)):[]}),Zd=$(function(e){var n=Ge(e),t=ie(e,Di);return n=typeof n=="function"?n:a,n&&t.pop(),t.length&&t[0]===e[0]?mi(t,a,n):[]});function Yd(e,n){return e==null?"":ql.call(e,n)}function Ge(e){var n=e==null?0:e.length;return n?e[n-1]:a}function Xd(e,n,t){var r=e==null?0:e.length;if(!r)return-1;var i=r;return t!==a&&(i=G(t),i=i<0?de(r+i,0):me(i,r-1)),n===n?Ll(e,n,i):Kt(e,Vo,i,!0)}function Jd(e,n){return e&&e.length?ws(e,G(n)):a}var Qd=$(la);function la(e,n){return e&&e.length&&n&&n.length?bi(e,n):e}function jd(e,n,t){return e&&e.length&&n&&n.length?bi(e,n,T(t,2)):e}function ec(e,n,t){return e&&e.length&&n&&n.length?bi(e,n,a,t):e}var nc=cn(function(e,n){var t=e==null?0:e.length,r=pi(e,n);return ys(e,ie(n,function(i){return pn(i,t)?+i:i}).sort(Ms)),r});function tc(e,n){var t=[];if(!(e&&e.length))return t;var r=-1,i=[],s=e.length;for(n=T(n,3);++r<s;){var l=e[r];n(l,r,e)&&(t.push(l),i.push(r))}return ys(e,i),t}function Gi(e){return e==null?e:Zl.call(e)}function rc(e,n,t){var r=e==null?0:e.length;return r?(t&&typeof t!="number"&&Se(e,n,t)?(n=0,t=r):(n=n==null?0:G(n),t=t===a?r:G(t)),ke(e,n,t)):[]}function ic(e,n){return cr(e,n)}function oc(e,n,t){return Ii(e,n,T(t,2))}function sc(e,n){var t=e==null?0:e.length;if(t){var r=cr(e,n);if(r<t&&Xe(e[r],n))return r}return-1}function ac(e,n){return cr(e,n,!0)}function uc(e,n,t){return Ii(e,n,T(t,2),!0)}function lc(e,n){var t=e==null?0:e.length;if(t){var r=cr(e,n,!0)-1;if(Xe(e[r],n))return r}return-1}function fc(e){return e&&e.length?Is(e):[]}function dc(e,n){return e&&e.length?Is(e,T(n,2)):[]}function cc(e){var n=e==null?0:e.length;return n?ke(e,1,n):[]}function pc(e,n,t){return e&&e.length?(n=t||n===a?1:G(n),ke(e,0,n<0?0:n)):[]}function gc(e,n,t){var r=e==null?0:e.length;return r?(n=t||n===a?1:G(n),n=r-n,ke(e,n<0?0:n,r)):[]}function hc(e,n){return e&&e.length?pr(e,T(n,3),!1,!0):[]}function _c(e,n){return e&&e.length?pr(e,T(n,3)):[]}var mc=$(function(e){return Cn(ge(e,1,ae,!0))}),vc=$(function(e){var n=Ge(e);return ae(n)&&(n=a),Cn(ge(e,1,ae,!0),T(n,2))}),wc=$(function(e){var n=Ge(e);return n=typeof n=="function"?n:a,Cn(ge(e,1,ae,!0),a,n)});function Sc(e){return e&&e.length?Cn(e):[]}function bc(e,n){return e&&e.length?Cn(e,T(n,2)):[]}function yc(e,n){return n=typeof n=="function"?n:a,e&&e.length?Cn(e,a,n):[]}function Vi(e){if(!(e&&e.length))return[];var n=0;return e=vn(e,function(t){if(ae(t))return n=de(t.length,n),!0}),oi(n,function(t){return ie(e,ti(t))})}function fa(e,n){if(!(e&&e.length))return[];var t=Vi(e);return n==null?t:ie(t,function(r){return Ee(n,a,r)})}var Cc=$(function(e,n){return ae(e)?It(e,n):[]}),Ic=$(function(e){return Ai(vn(e,ae))}),xc=$(function(e){var n=Ge(e);return ae(n)&&(n=a),Ai(vn(e,ae),T(n,2))}),Ac=$(function(e){var n=Ge(e);return n=typeof n=="function"?n:a,Ai(vn(e,ae),a,n)}),Dc=$(Vi);function Lc(e,n){return Ls(e||[],n||[],Ct)}function Ec(e,n){return Ls(e||[],n||[],Dt)}var Pc=$(function(e){var n=e.length,t=n>1?e[n-1]:a;return t=typeof t=="function"?(e.pop(),t):a,fa(e,t)});function da(e){var n=o(e);return n.__chain__=!0,n}function Tc(e,n){return n(e),e}function yr(e,n){return n(e)}var Mc=cn(function(e){var n=e.length,t=n?e[0]:0,r=this.__wrapped__,i=function(s){return pi(s,e)};return n>1||this.__actions__.length||!(r instanceof z)||!pn(t)?this.thru(i):(r=r.slice(t,+t+(n?1:0)),r.__actions__.push({func:yr,args:[i],thisArg:a}),new Fe(r,this.__chain__).thru(function(s){return n&&!s.length&&s.push(a),s}))});function Rc(){return da(this)}function Oc(){return new Fe(this.value(),this.__chain__)}function Uc(){this.__values__===a&&(this.__values__=xa(this.value()));var e=this.__index__>=this.__values__.length,n=e?a:this.__values__[this.__index__++];return{done:e,value:n}}function Bc(){return this}function Nc(e){for(var n,t=this;t instanceof ar;){var r=ia(t);r.__index__=0,r.__values__=a,n?i.__wrapped__=r:n=r;var i=r;t=t.__wrapped__}return i.__wrapped__=e,n}function Fc(){var e=this.__wrapped__;if(e instanceof z){var n=e;return this.__actions__.length&&(n=new z(this)),n=n.reverse(),n.__actions__.push({func:yr,args:[Gi],thisArg:a}),new Fe(n,this.__chain__)}return this.thru(Gi)}function Wc(){return Ds(this.__wrapped__,this.__actions__)}var kc=gr(function(e,n,t){ee.call(e,t)?++e[t]:fn(e,t,1)});function Gc(e,n,t){var r=W(e)?ko:Tf;return t&&Se(e,n,t)&&(n=a),r(e,T(n,3))}function Vc(e,n){var t=W(e)?vn:fs;return t(e,T(n,3))}var $c=Fs(oa),qc=Fs(sa);function Hc(e,n){return ge(Cr(e,n),1)}function Kc(e,n){return ge(Cr(e,n),Bt)}function zc(e,n,t){return t=t===a?1:G(t),ge(Cr(e,n),t)}function ca(e,n){var t=W(e)?Be:yn;return t(e,T(n,3))}function pa(e,n){var t=W(e)?dl:ls;return t(e,T(n,3))}var Zc=gr(function(e,n,t){ee.call(e,t)?e[t].push(n):fn(e,t,[n])});function Yc(e,n,t,r){e=Ie(e)?e:it(e),t=t&&!r?G(t):0;var i=e.length;return t<0&&(t=de(i+t,0)),Lr(e)?t<=i&&e.indexOf(n,t)>-1:!!i&&Kn(e,n,t)>-1}var Xc=$(function(e,n,t){var r=-1,i=typeof n=="function",s=Ie(e)?g(e.length):[];return yn(e,function(l){s[++r]=i?Ee(n,l,t):xt(l,n,t)}),s}),Jc=gr(function(e,n,t){fn(e,t,n)});function Cr(e,n){var t=W(e)?ie:_s;return t(e,T(n,3))}function Qc(e,n,t,r){return e==null?[]:(W(n)||(n=n==null?[]:[n]),t=r?a:t,W(t)||(t=t==null?[]:[t]),Ss(e,n,t))}var jc=gr(function(e,n,t){e[t?0:1].push(n)},function(){return[[],[]]});function ep(e,n,t){var r=W(e)?ei:qo,i=arguments.length<3;return r(e,T(n,4),t,i,yn)}function np(e,n,t){var r=W(e)?cl:qo,i=arguments.length<3;return r(e,T(n,4),t,i,ls)}function tp(e,n){var t=W(e)?vn:fs;return t(e,Ar(T(n,3)))}function rp(e){var n=W(e)?os:Yf;return n(e)}function ip(e,n,t){(t?Se(e,n,t):n===a)?n=1:n=G(n);var r=W(e)?Af:Xf;return r(e,n)}function op(e){var n=W(e)?Df:Qf;return n(e)}function sp(e){if(e==null)return 0;if(Ie(e))return Lr(e)?Zn(e):e.length;var n=ve(e);return n==Ke||n==ze?e.size:wi(e).length}function ap(e,n,t){var r=W(e)?ni:jf;return t&&Se(e,n,t)&&(n=a),r(e,T(n,3))}var up=$(function(e,n){if(e==null)return[];var t=n.length;return t>1&&Se(e,n[0],n[1])?n=[]:t>2&&Se(n[0],n[1],n[2])&&(n=[n[0]]),Ss(e,ge(n,1),[])}),Ir=Gl||function(){return pe.Date.now()};function lp(e,n){if(typeof n!="function")throw new Ne(D);return e=G(e),function(){if(--e<1)return n.apply(this,arguments)}}function ga(e,n,t){return n=t?a:n,n=e&&n==null?e.length:n,dn(e,He,a,a,a,a,n)}function ha(e,n){var t;if(typeof n!="function")throw new Ne(D);return e=G(e),function(){return--e>0&&(t=n.apply(this,arguments)),e<=1&&(n=a),t}}var $i=$(function(e,n,t){var r=R;if(t.length){var i=Sn(t,tt($i));r|=Y}return dn(e,r,n,t,i)}),_a=$(function(e,n,t){var r=R|ye;if(t.length){var i=Sn(t,tt(_a));r|=Y}return dn(n,r,e,t,i)});function ma(e,n,t){n=t?a:n;var r=dn(e,Le,a,a,a,a,a,n);return r.placeholder=ma.placeholder,r}function va(e,n,t){n=t?a:n;var r=dn(e,sn,a,a,a,a,a,n);return r.placeholder=va.placeholder,r}function wa(e,n,t){var r,i,s,l,d,p,_=0,v=!1,w=!1,C=!0;if(typeof e!="function")throw new Ne(D);n=Ve(n)||0,oe(t)&&(v=!!t.leading,w="maxWait"in t,s=w?de(Ve(t.maxWait)||0,n):s,C="trailing"in t?!!t.trailing:C);function E(ue){var Je=r,_n=i;return r=i=a,_=ue,l=e.apply(_n,Je),l}function O(ue){return _=ue,d=Pt(H,n),v?E(ue):l}function V(ue){var Je=ue-p,_n=ue-_,Fa=n-Je;return w?me(Fa,s-_n):Fa}function U(ue){var Je=ue-p,_n=ue-_;return p===a||Je>=n||Je<0||w&&_n>=s}function H(){var ue=Ir();if(U(ue))return Z(ue);d=Pt(H,V(ue))}function Z(ue){return d=a,C&&r?E(ue):(r=i=a,l)}function Re(){d!==a&&Es(d),_=0,r=p=i=d=a}function be(){return d===a?l:Z(Ir())}function Oe(){var ue=Ir(),Je=U(ue);if(r=arguments,i=this,p=ue,Je){if(d===a)return O(p);if(w)return Es(d),d=Pt(H,n),E(p)}return d===a&&(d=Pt(H,n)),l}return Oe.cancel=Re,Oe.flush=be,Oe}var fp=$(function(e,n){return us(e,1,n)}),dp=$(function(e,n,t){return us(e,Ve(n)||0,t)});function cp(e){return dn(e,Ot)}function xr(e,n){if(typeof e!="function"||n!=null&&typeof n!="function")throw new Ne(D);var t=function(){var r=arguments,i=n?n.apply(this,r):r[0],s=t.cache;if(s.has(i))return s.get(i);var l=e.apply(this,r);return t.cache=s.set(i,l)||s,l};return t.cache=new(xr.Cache||ln),t}xr.Cache=ln;function Ar(e){if(typeof e!="function")throw new Ne(D);return function(){var n=arguments;switch(n.length){case 0:return!e.call(this);case 1:return!e.call(this,n[0]);case 2:return!e.call(this,n[0],n[1]);case 3:return!e.call(this,n[0],n[1],n[2])}return!e.apply(this,n)}}function pp(e){return ha(2,e)}var gp=ed(function(e,n){n=n.length==1&&W(n[0])?ie(n[0],Pe(T())):ie(ge(n,1),Pe(T()));var t=n.length;return $(function(r){for(var i=-1,s=me(r.length,t);++i<s;)r[i]=n[i].call(this,r[i]);return Ee(e,this,r)})}),qi=$(function(e,n){var t=Sn(n,tt(qi));return dn(e,Y,a,n,t)}),Sa=$(function(e,n){var t=Sn(n,tt(Sa));return dn(e,mn,a,n,t)}),hp=cn(function(e,n){return dn(e,En,a,a,a,n)});function _p(e,n){if(typeof e!="function")throw new Ne(D);return n=n===a?n:G(n),$(e,n)}function mp(e,n){if(typeof e!="function")throw new Ne(D);return n=n==null?0:de(G(n),0),$(function(t){var r=t[n],i=xn(t,0,n);return r&&wn(i,r),Ee(e,this,i)})}function vp(e,n,t){var r=!0,i=!0;if(typeof e!="function")throw new Ne(D);return oe(t)&&(r="leading"in t?!!t.leading:r,i="trailing"in t?!!t.trailing:i),wa(e,n,{leading:r,maxWait:n,trailing:i})}function wp(e){return ga(e,1)}function Sp(e,n){return qi(Li(n),e)}function bp(){if(!arguments.length)return[];var e=arguments[0];return W(e)?e:[e]}function yp(e){return We(e,F)}function Cp(e,n){return n=typeof n=="function"?n:a,We(e,F,n)}function Ip(e){return We(e,K|F)}function xp(e,n){return n=typeof n=="function"?n:a,We(e,K|F,n)}function Ap(e,n){return n==null||as(e,n,ce(n))}function Xe(e,n){return e===n||e!==e&&n!==n}var Dp=vr(_i),Lp=vr(function(e,n){return e>=n}),Wn=ps(function(){return arguments}())?ps:function(e){return se(e)&&ee.call(e,"callee")&&!jo.call(e,"callee")},W=g.isArray,Ep=Oo?Pe(Oo):Nf;function Ie(e){return e!=null&&Dr(e.length)&&!gn(e)}function ae(e){return se(e)&&Ie(e)}function Pp(e){return e===!0||e===!1||se(e)&&we(e)==ft}var An=$l||no,Tp=Uo?Pe(Uo):Ff;function Mp(e){return se(e)&&e.nodeType===1&&!Tt(e)}function Rp(e){if(e==null)return!0;if(Ie(e)&&(W(e)||typeof e=="string"||typeof e.splice=="function"||An(e)||rt(e)||Wn(e)))return!e.length;var n=ve(e);if(n==Ke||n==ze)return!e.size;if(Et(e))return!wi(e).length;for(var t in e)if(ee.call(e,t))return!1;return!0}function Op(e,n){return At(e,n)}function Up(e,n,t){t=typeof t=="function"?t:a;var r=t?t(e,n):a;return r===a?At(e,n,a,t):!!r}function Hi(e){if(!se(e))return!1;var n=we(e);return n==Wt||n==ru||typeof e.message=="string"&&typeof e.name=="string"&&!Tt(e)}function Bp(e){return typeof e=="number"&&ns(e)}function gn(e){if(!oe(e))return!1;var n=we(e);return n==kt||n==ao||n==tu||n==ou}function ba(e){return typeof e=="number"&&e==G(e)}function Dr(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=Vn}function oe(e){var n=typeof e;return e!=null&&(n=="object"||n=="function")}function se(e){return e!=null&&typeof e=="object"}var ya=Bo?Pe(Bo):kf;function Np(e,n){return e===n||vi(e,n,Ui(n))}function Fp(e,n,t){return t=typeof t=="function"?t:a,vi(e,n,Ui(n),t)}function Wp(e){return Ca(e)&&e!=+e}function kp(e){if(Cd(e))throw new N(I);return gs(e)}function Gp(e){return e===null}function Vp(e){return e==null}function Ca(e){return typeof e=="number"||se(e)&&we(e)==ct}function Tt(e){if(!se(e)||we(e)!=an)return!1;var n=er(e);if(n===null)return!0;var t=ee.call(n,"constructor")&&n.constructor;return typeof t=="function"&&t instanceof t&&Xt.call(t)==Nl}var Ki=No?Pe(No):Gf;function $p(e){return ba(e)&&e>=-9007199254740991&&e<=Vn}var Ia=Fo?Pe(Fo):Vf;function Lr(e){return typeof e=="string"||!W(e)&&se(e)&&we(e)==gt}function Me(e){return typeof e=="symbol"||se(e)&&we(e)==Gt}var rt=Wo?Pe(Wo):$f;function qp(e){return e===a}function Hp(e){return se(e)&&ve(e)==ht}function Kp(e){return se(e)&&we(e)==au}var zp=vr(Si),Zp=vr(function(e,n){return e<=n});function xa(e){if(!e)return[];if(Ie(e))return Lr(e)?Ze(e):Ce(e);if(vt&&e[vt])return xl(e[vt]());var n=ve(e),t=n==Ke?ai:n==ze?zt:it;return t(e)}function hn(e){if(!e)return e===0?e:0;if(e=Ve(e),e===Bt||e===-1/0){var n=e<0?-1:1;return n*Qa}return e===e?e:0}function G(e){var n=hn(e),t=n%1;return n===n?t?n-t:n:0}function Aa(e){return e?Un(G(e),0,je):0}function Ve(e){if(typeof e=="number")return e;if(Me(e))return Nt;if(oe(e)){var n=typeof e.valueOf=="function"?e.valueOf():e;e=oe(n)?n+"":n}if(typeof e!="string")return e===0?e:+e;e=Ho(e);var t=Lu.test(e);return t||Pu.test(e)?ul(e.slice(2),t?2:8):Du.test(e)?Nt:+e}function Da(e){return nn(e,xe(e))}function Yp(e){return e?Un(G(e),-9007199254740991,Vn):e===0?e:0}function j(e){return e==null?"":Te(e)}var Xp=et(function(e,n){if(Et(n)||Ie(n)){nn(n,ce(n),e);return}for(var t in n)ee.call(n,t)&&Ct(e,t,n[t])}),La=et(function(e,n){nn(n,xe(n),e)}),Er=et(function(e,n,t,r){nn(n,xe(n),e,r)}),Jp=et(function(e,n,t,r){nn(n,ce(n),e,r)}),Qp=cn(pi);function jp(e,n){var t=jn(e);return n==null?t:ss(t,n)}var eg=$(function(e,n){e=ne(e);var t=-1,r=n.length,i=r>2?n[2]:a;for(i&&Se(n[0],n[1],i)&&(r=1);++t<r;)for(var s=n[t],l=xe(s),d=-1,p=l.length;++d<p;){var _=l[d],v=e[_];(v===a||Xe(v,Xn[_])&&!ee.call(e,_))&&(e[_]=s[_])}return e}),ng=$(function(e){return e.push(a,Hs),Ee(Ea,a,e)});function tg(e,n){return Go(e,T(n,3),en)}function rg(e,n){return Go(e,T(n,3),hi)}function ig(e,n){return e==null?e:gi(e,T(n,3),xe)}function og(e,n){return e==null?e:ds(e,T(n,3),xe)}function sg(e,n){return e&&en(e,T(n,3))}function ag(e,n){return e&&hi(e,T(n,3))}function ug(e){return e==null?[]:fr(e,ce(e))}function lg(e){return e==null?[]:fr(e,xe(e))}function zi(e,n,t){var r=e==null?a:Bn(e,n);return r===a?t:r}function fg(e,n){return e!=null&&Zs(e,n,Rf)}function Zi(e,n){return e!=null&&Zs(e,n,Of)}var dg=ks(function(e,n,t){n!=null&&typeof n.toString!="function"&&(n=Jt.call(n)),e[n]=t},Xi(Ae)),cg=ks(function(e,n,t){n!=null&&typeof n.toString!="function"&&(n=Jt.call(n)),ee.call(e,n)?e[n].push(t):e[n]=[t]},T),pg=$(xt);function ce(e){return Ie(e)?is(e):wi(e)}function xe(e){return Ie(e)?is(e,!0):qf(e)}function gg(e,n){var t={};return n=T(n,3),en(e,function(r,i,s){fn(t,n(r,i,s),r)}),t}function hg(e,n){var t={};return n=T(n,3),en(e,function(r,i,s){fn(t,i,n(r,i,s))}),t}var _g=et(function(e,n,t){dr(e,n,t)}),Ea=et(function(e,n,t,r){dr(e,n,t,r)}),mg=cn(function(e,n){var t={};if(e==null)return t;var r=!1;n=ie(n,function(s){return s=In(s,e),r||(r=s.length>1),s}),nn(e,Ri(e),t),r&&(t=We(t,K|B|F,dd));for(var i=n.length;i--;)xi(t,n[i]);return t});function vg(e,n){return Pa(e,Ar(T(n)))}var wg=cn(function(e,n){return e==null?{}:Kf(e,n)});function Pa(e,n){if(e==null)return{};var t=ie(Ri(e),function(r){return[r]});return n=T(n),bs(e,t,function(r,i){return n(r,i[0])})}function Sg(e,n,t){n=In(n,e);var r=-1,i=n.length;for(i||(i=1,e=a);++r<i;){var s=e==null?a:e[tn(n[r])];s===a&&(r=i,s=t),e=gn(s)?s.call(e):s}return e}function bg(e,n,t){return e==null?e:Dt(e,n,t)}function yg(e,n,t,r){return r=typeof r=="function"?r:a,e==null?e:Dt(e,n,t,r)}var Ta=$s(ce),Ma=$s(xe);function Cg(e,n,t){var r=W(e),i=r||An(e)||rt(e);if(n=T(n,4),t==null){var s=e&&e.constructor;i?t=r?new s:[]:oe(e)?t=gn(s)?jn(er(e)):{}:t={}}return(i?Be:en)(e,function(l,d,p){return n(t,l,d,p)}),t}function Ig(e,n){return e==null?!0:xi(e,n)}function xg(e,n,t){return e==null?e:As(e,n,Li(t))}function Ag(e,n,t,r){return r=typeof r=="function"?r:a,e==null?e:As(e,n,Li(t),r)}function it(e){return e==null?[]:si(e,ce(e))}function Dg(e){return e==null?[]:si(e,xe(e))}function Lg(e,n,t){return t===a&&(t=n,n=a),t!==a&&(t=Ve(t),t=t===t?t:0),n!==a&&(n=Ve(n),n=n===n?n:0),Un(Ve(e),n,t)}function Eg(e,n,t){return n=hn(n),t===a?(t=n,n=0):t=hn(t),e=Ve(e),Uf(e,n,t)}function Pg(e,n,t){if(t&&typeof t!="boolean"&&Se(e,n,t)&&(n=t=a),t===a&&(typeof n=="boolean"?(t=n,n=a):typeof e=="boolean"&&(t=e,e=a)),e===a&&n===a?(e=0,n=1):(e=hn(e),n===a?(n=e,e=0):n=hn(n)),e>n){var r=e;e=n,n=r}if(t||e%1||n%1){var i=ts();return me(e+i*(n-e+al("1e-"+((i+"").length-1))),n)}return yi(e,n)}var Tg=nt(function(e,n,t){return n=n.toLowerCase(),e+(t?Ra(n):n)});function Ra(e){return Yi(j(e).toLowerCase())}function Oa(e){return e=j(e),e&&e.replace(Mu,Sl).replace(Ju,"")}function Mg(e,n,t){e=j(e),n=Te(n);var r=e.length;t=t===a?r:Un(G(t),0,r);var i=t;return t-=n.length,t>=0&&e.slice(t,i)==n}function Rg(e){return e=j(e),e&&cu.test(e)?e.replace(fo,bl):e}function Og(e){return e=j(e),e&&vu.test(e)?e.replace(qr,"\\$&"):e}var Ug=nt(function(e,n,t){return e+(t?"-":"")+n.toLowerCase()}),Bg=nt(function(e,n,t){return e+(t?" ":"")+n.toLowerCase()}),Ng=Ns("toLowerCase");function Fg(e,n,t){e=j(e),n=G(n);var r=n?Zn(e):0;if(!n||r>=n)return e;var i=(n-r)/2;return mr(ir(i),t)+e+mr(rr(i),t)}function Wg(e,n,t){e=j(e),n=G(n);var r=n?Zn(e):0;return n&&r<n?e+mr(n-r,t):e}function kg(e,n,t){e=j(e),n=G(n);var r=n?Zn(e):0;return n&&r<n?mr(n-r,t)+e:e}function Gg(e,n,t){return t||n==null?n=0:n&&(n=+n),zl(j(e).replace(Hr,""),n||0)}function Vg(e,n,t){return(t?Se(e,n,t):n===a)?n=1:n=G(n),Ci(j(e),n)}function $g(){var e=arguments,n=j(e[0]);return e.length<3?n:n.replace(e[1],e[2])}var qg=nt(function(e,n,t){return e+(t?"_":"")+n.toLowerCase()});function Hg(e,n,t){return t&&typeof t!="number"&&Se(e,n,t)&&(n=t=a),t=t===a?je:t>>>0,t?(e=j(e),e&&(typeof n=="string"||n!=null&&!Ki(n))&&(n=Te(n),!n&&zn(e))?xn(Ze(e),0,t):e.split(n,t)):[]}var Kg=nt(function(e,n,t){return e+(t?" ":"")+Yi(n)});function zg(e,n,t){return e=j(e),t=t==null?0:Un(G(t),0,e.length),n=Te(n),e.slice(t,t+n.length)==n}function Zg(e,n,t){var r=o.templateSettings;t&&Se(e,n,t)&&(n=a),e=j(e),n=Er({},n,r,qs);var i=Er({},n.imports,r.imports,qs),s=ce(i),l=si(i,s),d,p,_=0,v=n.interpolate||Vt,w="__p += '",C=ui((n.escape||Vt).source+"|"+v.source+"|"+(v===co?Au:Vt).source+"|"+(n.evaluate||Vt).source+"|$","g"),E="//# sourceURL="+(ee.call(n,"sourceURL")?(n.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++tl+"]")+`
`;e.replace(C,function(U,H,Z,Re,be,Oe){return Z||(Z=Re),w+=e.slice(_,Oe).replace(Ru,yl),H&&(d=!0,w+=`' +
__e(`+H+`) +
'`),be&&(p=!0,w+=`';
`+be+`;
__p += '`),Z&&(w+=`' +
((__t = (`+Z+`)) == null ? '' : __t) +
'`),_=Oe+U.length,U}),w+=`';
`;var O=ee.call(n,"variable")&&n.variable;if(!O)w=`with (obj) {
`+w+`
}
`;else if(Iu.test(O))throw new N(L);w=(p?w.replace(uu,""):w).replace(lu,"$1").replace(fu,"$1;"),w="function("+(O||"obj")+`) {
`+(O?"":`obj || (obj = {});
`)+"var __t, __p = ''"+(d?", __e = _.escape":"")+(p?`, __j = Array.prototype.join;
function print() { __p += __j.call(arguments, '') }
`:`;
`)+w+`return __p
}`;var V=Ba(function(){return X(s,E+"return "+w).apply(a,l)});if(V.source=w,Hi(V))throw V;return V}function Yg(e){return j(e).toLowerCase()}function Xg(e){return j(e).toUpperCase()}function Jg(e,n,t){if(e=j(e),e&&(t||n===a))return Ho(e);if(!e||!(n=Te(n)))return e;var r=Ze(e),i=Ze(n),s=Ko(r,i),l=zo(r,i)+1;return xn(r,s,l).join("")}function Qg(e,n,t){if(e=j(e),e&&(t||n===a))return e.slice(0,Yo(e)+1);if(!e||!(n=Te(n)))return e;var r=Ze(e),i=zo(r,Ze(n))+1;return xn(r,0,i).join("")}function jg(e,n,t){if(e=j(e),e&&(t||n===a))return e.replace(Hr,"");if(!e||!(n=Te(n)))return e;var r=Ze(e),i=Ko(r,Ze(n));return xn(r,i).join("")}function eh(e,n){var t=Or,r=Ut;if(oe(n)){var i="separator"in n?n.separator:i;t="length"in n?G(n.length):t,r="omission"in n?Te(n.omission):r}e=j(e);var s=e.length;if(zn(e)){var l=Ze(e);s=l.length}if(t>=s)return e;var d=t-Zn(r);if(d<1)return r;var p=l?xn(l,0,d).join(""):e.slice(0,d);if(i===a)return p+r;if(l&&(d+=p.length-d),Ki(i)){if(e.slice(d).search(i)){var _,v=p;for(i.global||(i=ui(i.source,j(po.exec(i))+"g")),i.lastIndex=0;_=i.exec(v);)var w=_.index;p=p.slice(0,w===a?d:w)}}else if(e.indexOf(Te(i),d)!=d){var C=p.lastIndexOf(i);C>-1&&(p=p.slice(0,C))}return p+r}function nh(e){return e=j(e),e&&du.test(e)?e.replace(lo,El):e}var th=nt(function(e,n,t){return e+(t?" ":"")+n.toUpperCase()}),Yi=Ns("toUpperCase");function Ua(e,n,t){return e=j(e),n=t?a:n,n===a?Il(e)?Ml(e):hl(e):e.match(n)||[]}var Ba=$(function(e,n){try{return Ee(e,a,n)}catch(t){return Hi(t)?t:new N(t)}}),rh=cn(function(e,n){return Be(n,function(t){t=tn(t),fn(e,t,$i(e[t],e))}),e});function ih(e){var n=e==null?0:e.length,t=T();return e=n?ie(e,function(r){if(typeof r[1]!="function")throw new Ne(D);return[t(r[0]),r[1]]}):[],$(function(r){for(var i=-1;++i<n;){var s=e[i];if(Ee(s[0],this,r))return Ee(s[1],this,r)}})}function oh(e){return Pf(We(e,K))}function Xi(e){return function(){return e}}function sh(e,n){return e==null||e!==e?n:e}var ah=Ws(),uh=Ws(!0);function Ae(e){return e}function Ji(e){return hs(typeof e=="function"?e:We(e,K))}function lh(e){return ms(We(e,K))}function fh(e,n){return vs(e,We(n,K))}var dh=$(function(e,n){return function(t){return xt(t,e,n)}}),ch=$(function(e,n){return function(t){return xt(e,t,n)}});function Qi(e,n,t){var r=ce(n),i=fr(n,r);t==null&&!(oe(n)&&(i.length||!r.length))&&(t=n,n=e,e=this,i=fr(n,ce(n)));var s=!(oe(t)&&"chain"in t)||!!t.chain,l=gn(e);return Be(i,function(d){var p=n[d];e[d]=p,l&&(e.prototype[d]=function(){var _=this.__chain__;if(s||_){var v=e(this.__wrapped__),w=v.__actions__=Ce(this.__actions__);return w.push({func:p,args:arguments,thisArg:e}),v.__chain__=_,v}return p.apply(e,wn([this.value()],arguments))})}),e}function ph(){return pe._===this&&(pe._=Fl),this}function ji(){}function gh(e){return e=G(e),$(function(n){return ws(n,e)})}var hh=Pi(ie),_h=Pi(ko),mh=Pi(ni);function Na(e){return Ni(e)?ti(tn(e)):zf(e)}function vh(e){return function(n){return e==null?a:Bn(e,n)}}var wh=Gs(),Sh=Gs(!0);function eo(){return[]}function no(){return!1}function bh(){return{}}function yh(){return""}function Ch(){return!0}function Ih(e,n){if(e=G(e),e<1||e>Vn)return[];var t=je,r=me(e,je);n=T(n),e-=je;for(var i=oi(r,n);++t<e;)n(t);return i}function xh(e){return W(e)?ie(e,tn):Me(e)?[e]:Ce(ra(j(e)))}function Ah(e){var n=++Bl;return j(e)+n}var Dh=_r(function(e,n){return e+n},0),Lh=Ti("ceil"),Eh=_r(function(e,n){return e/n},1),Ph=Ti("floor");function Th(e){return e&&e.length?lr(e,Ae,_i):a}function Mh(e,n){return e&&e.length?lr(e,T(n,2),_i):a}function Rh(e){return $o(e,Ae)}function Oh(e,n){return $o(e,T(n,2))}function Uh(e){return e&&e.length?lr(e,Ae,Si):a}function Bh(e,n){return e&&e.length?lr(e,T(n,2),Si):a}var Nh=_r(function(e,n){return e*n},1),Fh=Ti("round"),Wh=_r(function(e,n){return e-n},0);function kh(e){return e&&e.length?ii(e,Ae):0}function Gh(e,n){return e&&e.length?ii(e,T(n,2)):0}return o.after=lp,o.ary=ga,o.assign=Xp,o.assignIn=La,o.assignInWith=Er,o.assignWith=Jp,o.at=Qp,o.before=ha,o.bind=$i,o.bindAll=rh,o.bindKey=_a,o.castArray=bp,o.chain=da,o.chunk=Pd,o.compact=Td,o.concat=Md,o.cond=ih,o.conforms=oh,o.constant=Xi,o.countBy=kc,o.create=jp,o.curry=ma,o.curryRight=va,o.debounce=wa,o.defaults=eg,o.defaultsDeep=ng,o.defer=fp,o.delay=dp,o.difference=Rd,o.differenceBy=Od,o.differenceWith=Ud,o.drop=Bd,o.dropRight=Nd,o.dropRightWhile=Fd,o.dropWhile=Wd,o.fill=kd,o.filter=Vc,o.flatMap=Hc,o.flatMapDeep=Kc,o.flatMapDepth=zc,o.flatten=aa,o.flattenDeep=Gd,o.flattenDepth=Vd,o.flip=cp,o.flow=ah,o.flowRight=uh,o.fromPairs=$d,o.functions=ug,o.functionsIn=lg,o.groupBy=Zc,o.initial=Hd,o.intersection=Kd,o.intersectionBy=zd,o.intersectionWith=Zd,o.invert=dg,o.invertBy=cg,o.invokeMap=Xc,o.iteratee=Ji,o.keyBy=Jc,o.keys=ce,o.keysIn=xe,o.map=Cr,o.mapKeys=gg,o.mapValues=hg,o.matches=lh,o.matchesProperty=fh,o.memoize=xr,o.merge=_g,o.mergeWith=Ea,o.method=dh,o.methodOf=ch,o.mixin=Qi,o.negate=Ar,o.nthArg=gh,o.omit=mg,o.omitBy=vg,o.once=pp,o.orderBy=Qc,o.over=hh,o.overArgs=gp,o.overEvery=_h,o.overSome=mh,o.partial=qi,o.partialRight=Sa,o.partition=jc,o.pick=wg,o.pickBy=Pa,o.property=Na,o.propertyOf=vh,o.pull=Qd,o.pullAll=la,o.pullAllBy=jd,o.pullAllWith=ec,o.pullAt=nc,o.range=wh,o.rangeRight=Sh,o.rearg=hp,o.reject=tp,o.remove=tc,o.rest=_p,o.reverse=Gi,o.sampleSize=ip,o.set=bg,o.setWith=yg,o.shuffle=op,o.slice=rc,o.sortBy=up,o.sortedUniq=fc,o.sortedUniqBy=dc,o.split=Hg,o.spread=mp,o.tail=cc,o.take=pc,o.takeRight=gc,o.takeRightWhile=hc,o.takeWhile=_c,o.tap=Tc,o.throttle=vp,o.thru=yr,o.toArray=xa,o.toPairs=Ta,o.toPairsIn=Ma,o.toPath=xh,o.toPlainObject=Da,o.transform=Cg,o.unary=wp,o.union=mc,o.unionBy=vc,o.unionWith=wc,o.uniq=Sc,o.uniqBy=bc,o.uniqWith=yc,o.unset=Ig,o.unzip=Vi,o.unzipWith=fa,o.update=xg,o.updateWith=Ag,o.values=it,o.valuesIn=Dg,o.without=Cc,o.words=Ua,o.wrap=Sp,o.xor=Ic,o.xorBy=xc,o.xorWith=Ac,o.zip=Dc,o.zipObject=Lc,o.zipObjectDeep=Ec,o.zipWith=Pc,o.entries=Ta,o.entriesIn=Ma,o.extend=La,o.extendWith=Er,Qi(o,o),o.add=Dh,o.attempt=Ba,o.camelCase=Tg,o.capitalize=Ra,o.ceil=Lh,o.clamp=Lg,o.clone=yp,o.cloneDeep=Ip,o.cloneDeepWith=xp,o.cloneWith=Cp,o.conformsTo=Ap,o.deburr=Oa,o.defaultTo=sh,o.divide=Eh,o.endsWith=Mg,o.eq=Xe,o.escape=Rg,o.escapeRegExp=Og,o.every=Gc,o.find=$c,o.findIndex=oa,o.findKey=tg,o.findLast=qc,o.findLastIndex=sa,o.findLastKey=rg,o.floor=Ph,o.forEach=ca,o.forEachRight=pa,o.forIn=ig,o.forInRight=og,o.forOwn=sg,o.forOwnRight=ag,o.get=zi,o.gt=Dp,o.gte=Lp,o.has=fg,o.hasIn=Zi,o.head=ua,o.identity=Ae,o.includes=Yc,o.indexOf=qd,o.inRange=Eg,o.invoke=pg,o.isArguments=Wn,o.isArray=W,o.isArrayBuffer=Ep,o.isArrayLike=Ie,o.isArrayLikeObject=ae,o.isBoolean=Pp,o.isBuffer=An,o.isDate=Tp,o.isElement=Mp,o.isEmpty=Rp,o.isEqual=Op,o.isEqualWith=Up,o.isError=Hi,o.isFinite=Bp,o.isFunction=gn,o.isInteger=ba,o.isLength=Dr,o.isMap=ya,o.isMatch=Np,o.isMatchWith=Fp,o.isNaN=Wp,o.isNative=kp,o.isNil=Vp,o.isNull=Gp,o.isNumber=Ca,o.isObject=oe,o.isObjectLike=se,o.isPlainObject=Tt,o.isRegExp=Ki,o.isSafeInteger=$p,o.isSet=Ia,o.isString=Lr,o.isSymbol=Me,o.isTypedArray=rt,o.isUndefined=qp,o.isWeakMap=Hp,o.isWeakSet=Kp,o.join=Yd,o.kebabCase=Ug,o.last=Ge,o.lastIndexOf=Xd,o.lowerCase=Bg,o.lowerFirst=Ng,o.lt=zp,o.lte=Zp,o.max=Th,o.maxBy=Mh,o.mean=Rh,o.meanBy=Oh,o.min=Uh,o.minBy=Bh,o.stubArray=eo,o.stubFalse=no,o.stubObject=bh,o.stubString=yh,o.stubTrue=Ch,o.multiply=Nh,o.nth=Jd,o.noConflict=ph,o.noop=ji,o.now=Ir,o.pad=Fg,o.padEnd=Wg,o.padStart=kg,o.parseInt=Gg,o.random=Pg,o.reduce=ep,o.reduceRight=np,o.repeat=Vg,o.replace=$g,o.result=Sg,o.round=Fh,o.runInContext=c,o.sample=rp,o.size=sp,o.snakeCase=qg,o.some=ap,o.sortedIndex=ic,o.sortedIndexBy=oc,o.sortedIndexOf=sc,o.sortedLastIndex=ac,o.sortedLastIndexBy=uc,o.sortedLastIndexOf=lc,o.startCase=Kg,o.startsWith=zg,o.subtract=Wh,o.sum=kh,o.sumBy=Gh,o.template=Zg,o.times=Ih,o.toFinite=hn,o.toInteger=G,o.toLength=Aa,o.toLower=Yg,o.toNumber=Ve,o.toSafeInteger=Yp,o.toString=j,o.toUpper=Xg,o.trim=Jg,o.trimEnd=Qg,o.trimStart=jg,o.truncate=eh,o.unescape=nh,o.uniqueId=Ah,o.upperCase=th,o.upperFirst=Yi,o.each=ca,o.eachRight=pa,o.first=ua,Qi(o,function(){var e={};return en(o,function(n,t){ee.call(o.prototype,t)||(e[t]=n)}),e}(),{chain:!1}),o.VERSION=y,Be(["bind","bindKey","curry","curryRight","partial","partialRight"],function(e){o[e].placeholder=o}),Be(["drop","take"],function(e,n){z.prototype[e]=function(t){t=t===a?1:de(G(t),0);var r=this.__filtered__&&!n?new z(this):this.clone();return r.__filtered__?r.__takeCount__=me(t,r.__takeCount__):r.__views__.push({size:me(t,je),type:e+(r.__dir__<0?"Right":"")}),r},z.prototype[e+"Right"]=function(t){return this.reverse()[e](t).reverse()}}),Be(["filter","map","takeWhile"],function(e,n){var t=n+1,r=t==so||t==Ja;z.prototype[e]=function(i){var s=this.clone();return s.__iteratees__.push({iteratee:T(i,3),type:t}),s.__filtered__=s.__filtered__||r,s}}),Be(["head","last"],function(e,n){var t="take"+(n?"Right":"");z.prototype[e]=function(){return this[t](1).value()[0]}}),Be(["initial","tail"],function(e,n){var t="drop"+(n?"":"Right");z.prototype[e]=function(){return this.__filtered__?new z(this):this[t](1)}}),z.prototype.compact=function(){return this.filter(Ae)},z.prototype.find=function(e){return this.filter(e).head()},z.prototype.findLast=function(e){return this.reverse().find(e)},z.prototype.invokeMap=$(function(e,n){return typeof e=="function"?new z(this):this.map(function(t){return xt(t,e,n)})}),z.prototype.reject=function(e){return this.filter(Ar(T(e)))},z.prototype.slice=function(e,n){e=G(e);var t=this;return t.__filtered__&&(e>0||n<0)?new z(t):(e<0?t=t.takeRight(-e):e&&(t=t.drop(e)),n!==a&&(n=G(n),t=n<0?t.dropRight(-n):t.take(n-e)),t)},z.prototype.takeRightWhile=function(e){return this.reverse().takeWhile(e).reverse()},z.prototype.toArray=function(){return this.take(je)},en(z.prototype,function(e,n){var t=/^(?:filter|find|map|reject)|While$/.test(n),r=/^(?:head|last)$/.test(n),i=o[r?"take"+(n=="last"?"Right":""):n],s=r||/^find/.test(n);i&&(o.prototype[n]=function(){var l=this.__wrapped__,d=r?[1]:arguments,p=l instanceof z,_=d[0],v=p||W(l),w=function(H){var Z=i.apply(o,wn([H],d));return r&&C?Z[0]:Z};v&&t&&typeof _=="function"&&_.length!=1&&(p=v=!1);var C=this.__chain__,E=!!this.__actions__.length,O=s&&!C,V=p&&!E;if(!s&&v){l=V?l:new z(this);var U=e.apply(l,d);return U.__actions__.push({func:yr,args:[w],thisArg:a}),new Fe(U,C)}return O&&V?e.apply(this,d):(U=this.thru(w),O?r?U.value()[0]:U.value():U)})}),Be(["pop","push","shift","sort","splice","unshift"],function(e){var n=Zt[e],t=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",r=/^(?:pop|shift)$/.test(e);o.prototype[e]=function(){var i=arguments;if(r&&!this.__chain__){var s=this.value();return n.apply(W(s)?s:[],i)}return this[t](function(l){return n.apply(W(l)?l:[],i)})}}),en(z.prototype,function(e,n){var t=o[n];if(t){var r=t.name+"";ee.call(Qn,r)||(Qn[r]=[]),Qn[r].push({name:n,func:t})}}),Qn[hr(a,ye).name]=[{name:"wrapper",func:a}],z.prototype.clone=ef,z.prototype.reverse=nf,z.prototype.value=tf,o.prototype.at=Mc,o.prototype.chain=Rc,o.prototype.commit=Oc,o.prototype.next=Uc,o.prototype.plant=Nc,o.prototype.reverse=Fc,o.prototype.toJSON=o.prototype.valueOf=o.prototype.value=Wc,o.prototype.first=o.prototype.head,vt&&(o.prototype[vt]=Bc),o},Yn=Rl();Tn?((Tn.exports=Yn)._=Yn,Jr._=Yn):pe._=Yn}).call(C_)}(Rt,Rt.exports)),Rt.exports}I_();Mr.registerLanguage("json",$h);Mr.registerLanguage("javascript",qh);Mr.registerLanguage("bash",Hh);Kh.use(zh,{Hljs:Mr});const x_={props:{modelValue:String,height:{type:String,default:"300px"}},emits:["update:modelValue"],setup(u,{emit:f}){const a=rn(u.modelValue||"");return Ln(()=>u.modelValue,b=>{a.value=b}),{localValue:a,handleChange:b=>{f("update:modelValue",b)}}}};function A_(u,f,a,y,b,I){const D=he("v-md-editor");return J(),on(D,{modelValue:y.localValue,"onUpdate:modelValue":f[0]||(f[0]=L=>y.localValue=L),height:a.height,"left-toolbar":"undo redo clear | h bold italic strikethrough quote | ul ol table hr | link image code",onChange:y.handleChange},null,8,["modelValue","height","onChange"])}const D_=Qe(x_,[["render",A_]]),L_={name:"ModelSettings",components:{MarkdownEditor:D_},props:{modelOptions:{type:Array,default:()=>[],validator:u=>u.every(f=>f.id&&f.platform&&f.name)},embeddingModelOptions:{type:Array,default:()=>[],validator:u=>u.every(f=>f.id&&f.platform&&f.name)},modelId:{type:String,required:!0,default:""},embeddingModelId:{type:String,default:""},rolePrompt:{type:String,default:""},rulePrompt:{type:String,default:"{}",validator:u=>!0},responsePrompt:{type:String,default:"{}",validator:u=>!0}},emits:{"update:modelId":u=>typeof u=="string","update:embeddingModelId":u=>typeof u=="string","update:rolePrompt":u=>typeof u=="string","update:modelSettings":u=>typeof u.rolePrompt=="string"&&typeof u.rulePrompt=="string"&&typeof u.responsePrompt=="string","update:functionCallSupport":u=>typeof u=="boolean"},setup(u,{emit:f}){const a=rn(!1),y=rn(0),b=rn(u.rolePrompt),I=rn(u.rulePrompt),D=rn(u.responsePrompt),L=Mt({get:()=>u.modelId,set:R=>f("update:modelId",R)}),M=Mt({get:()=>u.embeddingModelId,set:R=>f("update:embeddingModelId",R)}),q=Mt(()=>u.modelOptions.filter(R=>R.id)),x=Mt(()=>u.embeddingModelOptions.filter(R=>R.id)),K=Mt(()=>{if(!L.value)return!1;const R=u.modelOptions.find(ye=>ye.id.toString()===L.value.toString());return R&&R.functionCall===1});return Ln(()=>u.rulePrompt,R=>{I.value=R}),Ln(()=>u.responsePrompt,R=>{D.value=R}),Ln(()=>u.rolePrompt,R=>{b.value=R}),Ln(()=>K.value,R=>{f("update:functionCallSupport",R)},{immediate:!0}),Ln(()=>L.value,()=>{f("update:functionCallSupport",K.value)}),{Edit:Rr,selectedModel:L,selectedEmbeddingModel:M,validatedModelOptions:q,validatedEmbeddingModelOptions:x,dialogVisible:a,localRolePrompt:b,editorKey:y,updateRolePrompt:R=>{f("update:rolePrompt",R)},openPromptDialog:async()=>{y.value+=1,await za(),a.value=!0},handleDialogClose:()=>{},savePrompts:()=>{if(b.value.trim()===""){$e.error("角色提示不能为空");return}f("update:modelSettings",{rolePrompt:b.value,rulePrompt:I.value,responsePrompt:D.value}),$e.success("设置保存成功"),a.value=!1},localRulePrompt:I,localResponsePrompt:D,currentModelSupportsFunctionCall:K}}},E_={class:"model-settings"},P_={class:"section-header"};function T_(u,f,a,y,b,I){const D=ut,L=Gn,M=Ha,q=qa,x=kn,K=lt,B=Tr,F=he("markdown-editor"),Q=Ka;return J(),le("div",E_,[m(K,{class:"settings-card"},{header:S(()=>[P("div",P_,[f[8]||(f[8]=P("span",{class:"group-title"},"模型选择",-1)),m(D,{type:"primary",text:"",icon:y.Edit,class:"action-button",onClick:y.openPromptDialog},{default:S(()=>f[7]||(f[7]=[De(" 用户指令 ")])),_:1},8,["icon","onClick"])])]),default:S(()=>[m(x,{class:"model-selection"},{default:S(()=>[m(L,{span:6},{default:S(()=>f[9]||(f[9]=[P("span",{class:"selection-label"},"生成模型",-1)])),_:1}),m(L,{span:16,offset:2},{default:S(()=>[m(q,{modelValue:y.selectedModel,"onUpdate:modelValue":f[0]||(f[0]=k=>y.selectedModel=k),placeholder:"选择模型",size:"large",class:"model-selector"},{default:S(()=>[(J(!0),le(st,null,at(y.validatedModelOptions,k=>(J(),on(M,{key:k.id,label:`${k.platform}-${k.name}`,value:k.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),m(x,{class:"model-selection"},{default:S(()=>[m(L,{span:6},{default:S(()=>f[10]||(f[10]=[P("span",{class:"selection-label"},"切词模型",-1)])),_:1}),m(L,{span:16,offset:2},{default:S(()=>[m(q,{modelValue:y.selectedEmbeddingModel,"onUpdate:modelValue":f[1]||(f[1]=k=>y.selectedEmbeddingModel=k),placeholder:"选择切词模型",size:"large",class:"model-selector"},{default:S(()=>[(J(!0),le(st,null,at(y.validatedEmbeddingModelOptions,k=>(J(),on(M,{key:k.id,label:`${k.platform}-${k.name}`,value:k.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),m(Q,{modelValue:y.dialogVisible,"onUpdate:modelValue":f[6]||(f[6]=k=>y.dialogVisible=k),title:"模型交互设置",width:"70%",onClosed:y.handleDialogClose},{footer:S(()=>[m(D,{onClick:f[5]||(f[5]=k=>y.dialogVisible=!1)},{default:S(()=>f[14]||(f[14]=[De("取消")])),_:1}),m(D,{type:"primary",onClick:y.savePrompts},{default:S(()=>f[15]||(f[15]=[De("保存")])),_:1},8,["onClick"])]),default:S(()=>[m(x,{class:"model-selection"},{default:S(()=>[m(L,{span:4},{default:S(()=>f[11]||(f[11]=[P("span",{class:"selection-label"},"系统角色",-1)])),_:1}),m(L,{span:20},{default:S(()=>[m(B,{modelValue:y.localRolePrompt,"onUpdate:modelValue":f[2]||(f[2]=k=>y.localRolePrompt=k),placeholder:"设置模型的角色",onInput:y.updateRolePrompt},null,8,["modelValue","onInput"])]),_:1})]),_:1}),m(x,{class:"model-selection"},{default:S(()=>[m(L,{span:4},{default:S(()=>f[12]||(f[12]=[P("span",{class:"selection-label"},"用户指令",-1)])),_:1}),m(L,{span:20},{default:S(()=>[m(F,{modelValue:y.localRulePrompt,"onUpdate:modelValue":f[3]||(f[3]=k=>y.localRulePrompt=k),height:"300px"},null,8,["modelValue"])]),_:1})]),_:1}),m(x,{class:"model-selection"},{default:S(()=>[m(L,{span:4},{default:S(()=>f[13]||(f[13]=[P("span",{class:"selection-label"},"应用回复",-1)])),_:1}),m(L,{span:20},{default:S(()=>[m(F,{modelValue:y.localResponsePrompt,"onUpdate:modelValue":f[4]||(f[4]=k=>y.localResponsePrompt=k),height:"300px"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue","onClosed"])])}const M_=Qe(L_,[["render",T_],["__scopeId","data-v-9a59af13"]]),R_={name:"KnowledgeSettings",components:{CopyDocument:Zh},props:{recallCount:{type:Number,default:1},similarity:{type:Number,default:.1},selectedKnowledgeBases:{type:Array,default:()=>[]}},emits:["update:recallCount","update:similarity","openAssociationDialog"],setup(u,{emit:f}){return{Edit:Rr,updateRecallCount:I=>{f("update:recallCount",I)},updateSimilarity:I=>{f("update:similarity",I)},openAssociationDialog:()=>{f("openAssociationDialog")}}}},O_={class:"knowledge-settings"},U_={class:"section-header"},B_={class:"settings-content"},N_={class:"setting-control"},F_={class:"setting-control"};function W_(u,f,a,y,b,I){const D=ut,L=Yh,M=$a,q=kn,x=he("copy-document"),K=ro,B=oo,F=io,Q=Gn,k=lt;return J(),le("div",O_,[m(k,{class:"settings-card"},{header:S(()=>[P("div",U_,[f[1]||(f[1]=P("span",{class:"group-title"},"知识",-1)),m(D,{type:"primary",text:"",icon:y.Edit,class:"association-btn",onClick:y.openAssociationDialog},{default:S(()=>f[0]||(f[0]=[De(" 关联知识库 ")])),_:1},8,["icon","onClick"])])]),default:S(()=>[P("div",B_,[m(q,{class:"setting-item"},{default:S(()=>[P("div",N_,[f[2]||(f[2]=P("span",{class:"setting-label"},"召回数",-1)),m(L,{"model-value":a.recallCount,"onUpdate:modelValue":y.updateRecallCount,min:1,max:10,size:"small","controls-position":"right"},null,8,["model-value","onUpdate:modelValue"])]),m(M,{type:"info",class:"hint-tag"},{default:S(()=>f[3]||(f[3]=[De("召回切片的字符总数不应超过所选模型上下文长度")])),_:1})]),_:1}),m(q,{class:"setting-item"},{default:S(()=>[P("div",F_,[f[4]||(f[4]=P("span",{class:"setting-label"},"相似度",-1)),m(L,{"model-value":a.similarity,"onUpdate:modelValue":y.updateSimilarity,min:.1,max:1,step:.1,size:"small","controls-position":"right"},null,8,["model-value","onUpdate:modelValue"])]),m(M,{type:"info",class:"hint-tag"},{default:S(()=>f[5]||(f[5]=[De("调整匹配分阈值以过滤得到最相关答案")])),_:1})]),_:1}),m(q,null,{default:S(()=>[m(Q,null,{default:S(()=>[m(F,{data:a.selectedKnowledgeBases,style:{width:"100%"},"empty-text":"暂无关联知识库",class:"knowledge-table"},{default:S(()=>[m(B,{width:"40px"},{default:S(()=>[m(K,null,{default:S(()=>[m(x)]),_:1})]),_:1}),m(B,{prop:"name",label:"知识库名称"})]),_:1},8,["data"])]),_:1})]),_:1})])]),_:1})])}const k_=Qe(R_,[["render",W_],["__scopeId","data-v-2b722cac"]]),G_={name:"MCPServerSettings",props:{selectedMCPServers:{type:Array,default:()=>[]},functionCallSupported:{type:Boolean,default:!1}},emits:["update:selectedMCPServers","openMCPServerDialog"],setup(u,{emit:f}){return{Edit:Rr,Delete:Jh,Monitor:Xh,openMCPServerDialog:()=>{u.functionCallSupported&&(console.log("MCPServerSettings: openMCPServerDialog被调用"),f("openMCPServerDialog"))},updateMCPServers:()=>{f("update:selectedMCPServers",[...u.selectedMCPServers])},removeMCPServer:I=>{const D=[...u.selectedMCPServers];D.splice(I,1),f("update:selectedMCPServers",D)}}}},V_={class:"mcpserver-settings"},$_={class:"section-header"},q_={class:"settings-content"},H_={class:"order-number"};function K_(u,f,a,y,b,I){const D=ut,L=Qh,M=oo,q=io,x=Gn,K=kn,B=lt;return J(),le("div",V_,[m(B,{class:"settings-card"},{header:S(()=>[P("div",$_,[f[1]||(f[1]=P("span",{class:"group-title"},"MCP Server",-1)),m(L,{content:a.functionCallSupported?"":"当前模型不支持关联MCP Server",placement:"top",disabled:a.functionCallSupported},{default:S(()=>[m(D,{type:"primary",text:"",icon:y.Edit,class:"association-btn",onClick:y.openMCPServerDialog,disabled:!a.functionCallSupported},{default:S(()=>f[0]||(f[0]=[De(" 关联 MCP Server ")])),_:1},8,["icon","onClick","disabled"])]),_:1},8,["content","disabled"])])]),default:S(()=>[P("div",q_,[m(K,null,{default:S(()=>[m(x,null,{default:S(()=>[m(q,{data:a.selectedMCPServers,style:{width:"100%"},"empty-text":"暂无关联 MCP Server",class:"mcpserver-table"},{default:S(()=>[m(M,{width:"40px"},{default:S(F=>[P("div",H_,Dn(F.$index+1),1)]),_:1}),m(M,{prop:"mcpServerName",width:"120px",label:"Server 名称"}),m(M,{prop:"description",label:"功能描述"}),m(M,{width:"80px"},{default:S(F=>[m(D,{type:"danger",text:"",icon:y.Delete,onClick:Q=>y.removeMCPServer(F.$index)},null,8,["icon","onClick"])]),_:1})]),_:1},8,["data"])]),_:1})]),_:1})])]),_:1})])}const z_=Qe(G_,[["render",K_],["__scopeId","data-v-fd170a76"]]),Z_=Za({name:"AgentConfig",components:{BasicSettings:y_,ModelSettings:M_,KnowledgeSettings:k_,MCPServerSettings:z_,UserFilled:Ya,Tools:jh},props:{agentData:{type:Object,required:!0},modelOptions:{type:Array,default:()=>[]},embeddingModelOptions:{type:Array,default:()=>[]},baseURL:{type:String,default:""}},emits:["update:agentData","openAssociationDialog","openMCPServerDialog","createSuccess"],setup(u,{emit:f}){const a=rn(!1),y=rn(!1),b=rn(!1),I=(B,F)=>{f("update:agentData",{...u.agentData,[B]:F})},D=B=>{f("update:agentData",{...u.agentData,...B})},L=B=>{b.value=B},M=()=>{var B;return(B=u.agentData.name)!=null&&B.trim()?u.agentData.groupId?!0:($e.error("工作组不能为空，请选择工作组"),!1):($e.error("智能体标题不能为空"),!1)},q=()=>{const{name:B,description:F="",imageUrl:Q="",knowledgeBaseIds:k="",recallCount:R=1,similarity:ye=.1,modelId:qe,embeddingModelId:Le="",groupId:sn,rolePrompt:Y="qa",responsePrompt:mn="",rulePrompt:He="",flowNodes:En=[]}=u.agentData,Or=[...En].sort((_e,Pn)=>_e.numbering-Pn.numbering).map((_e,Pn)=>({..._e,numbering:Pn}));let Ut="";if(qe&&u.modelOptions){const _e=u.modelOptions.find(Pn=>Pn.id===qe);_e&&(Ut=_e.name)}return{name:B,description:F,url:Q,knowledgeBaseIds:k,recallCount:R,similarity:ye,modelId:qe,embeddingModelId:Le,modelName:Ut,groupId:sn,rolePrompt:Y,responsePrompt:mn,rulePrompt:He,flowNodes:Or.map(_e=>({..._e.id?{id:_e.id}:{},mcpServerId:_e.mcpServerId,mcpServerName:_e.mcpServerName||"",numbering:_e.numbering,inputLimit:_e.inputLimit||"",outputLimit:_e.outputLimit||""}))}};return{dialogTableVisible:a,loading:y,functionCallSupported:b,updateField:I,updateModelSettings:D,updateFunctionCallSupport:L,createAgent:async()=>{if(M()){y.value=!0;try{const B=q(),F=await to.saveAgent(B);if(F&&F.data&&F.code===200)$e.success("创建智能体成功"),f("createSuccess",parseInt(F.data));else{const Q=(F==null?void 0:F.message)||"未知错误";$e.error(`创建失败: ${Q}`)}}catch(B){console.error("创建智能体出错:",B),$e.error("创建智能体时发生错误，请稍后重试")}finally{y.value=!1}}},updateAgent:async()=>{if(M()){if(!u.agentData.id){$e.error("缺少智能体ID，无法更新");return}y.value=!0;try{const F={...q(),id:u.agentData.id},Q=await to.updateAgent(F);(Q==null?void 0:Q.code)===200&&$e.success((Q==null?void 0:Q.data)||"更新智能体成功")}finally{y.value=!1}}}}}}),Y_={class:"agent-config"},X_={class:"config-container"},J_={class:"config-section"},Q_={class:"section-content"},j_={class:"setting-group"},em={class:"setting-group"},nm={class:"setting-group"},tm={class:"setting-group"};function rm(u,f,a,y,b,I){const D=he("BasicSettings"),L=he("ModelSettings"),M=he("KnowledgeSettings"),q=he("MCPServerSettings");return J(),le("div",Y_,[P("div",X_,[P("div",J_,[P("div",Q_,[P("div",j_,[m(D,{baseURL:u.baseURL,imageUrl:u.agentData.imageUrl,agentName:u.agentData.name,description:u.agentData.description,groupId:u.agentData.groupId,"onUpdate:imageUrl":f[0]||(f[0]=x=>u.updateField("imageUrl",x)),"onUpdate:agentName":f[1]||(f[1]=x=>u.updateField("name",x)),"onUpdate:description":f[2]||(f[2]=x=>u.updateField("description",x)),"onUpdate:groupId":f[3]||(f[3]=x=>u.updateField("groupId",x))},null,8,["baseURL","imageUrl","agentName","description","groupId"])]),P("div",em,[m(L,{modelOptions:u.modelOptions,embeddingModelOptions:u.embeddingModelOptions,modelId:u.agentData.modelId,embeddingModelId:u.agentData.embeddingModelId,rolePrompt:u.agentData.rolePrompt,rulePrompt:u.agentData.rulePrompt,responsePrompt:u.agentData.responsePrompt,"onUpdate:modelId":f[4]||(f[4]=x=>u.updateField("modelId",x)),"onUpdate:embeddingModelId":f[5]||(f[5]=x=>u.updateField("embeddingModelId",x)),"onUpdate:modelSettings":u.updateModelSettings,"onUpdate:functionCallSupport":u.updateFunctionCallSupport},null,8,["modelOptions","embeddingModelOptions","modelId","embeddingModelId","rolePrompt","rulePrompt","responsePrompt","onUpdate:modelSettings","onUpdate:functionCallSupport"])]),P("div",nm,[m(M,{recallCount:u.agentData.recallCount,similarity:u.agentData.similarity,selectedKnowledgeBases:u.agentData.selectedRows,"onUpdate:recallCount":f[6]||(f[6]=x=>u.updateField("recallCount",x)),"onUpdate:similarity":f[7]||(f[7]=x=>u.updateField("similarity",x)),onOpenAssociationDialog:f[8]||(f[8]=x=>u.$emit("openAssociationDialog"))},null,8,["recallCount","similarity","selectedKnowledgeBases"])]),P("div",tm,[m(q,{selectedMCPServers:u.agentData.flowNodes||[],functionCallSupported:u.functionCallSupported,"onUpdate:selectedMCPServers":f[9]||(f[9]=x=>u.updateField("flowNodes",x)),onOpenMCPServerDialog:f[10]||(f[10]=x=>u.$emit("openMCPServerDialog"))},null,8,["selectedMCPServers","functionCallSupported"])])])])])])}const im=Qe(Z_,[["render",rm],["__scopeId","data-v-a494d5b2"]]),om=Za({name:"AgentInput",props:{isLoading:Boolean,agentId:{type:[Number,String],default:0},modelValue:String},emits:["submit","update:modelValue"],setup(u,{emit:f}){const a=rn(u.modelValue||""),y=()=>{var b;console.log("onSubmit",a.value),(b=a.value)!=null&&b.trim()?f("submit",{prompt:a.value,agentId:u.agentId||0}):$e.error("请输入问题")};return Ln(()=>u.modelValue,b=>{a.value=b||""}),Ln(a,b=>{f("update:modelValue",b)}),{inputPrompt:a,onSubmit:y}}}),sm={class:"input-container"},am={class:"grid-content bg-purple-dark",style:{margin:"10px",display:"flex","justify-content":"flex-end","align-items":"center"}},um={key:0,src:l_,height:"25px",width:"25px"};function lm(u,f,a,y,b,I){const D=Tr,L=ut,M=lt,q=Gn,x=kn;return J(),le("div",sm,[m(x,{justify:"center"},{default:S(()=>[m(q,{span:22,style:{"border-radius":"12px"}},{default:S(()=>[m(M,{"body-style":{padding:"0px"},class:"fixed-input"},{default:S(()=>{var K;return[P("div",am,[m(D,{modelValue:u.inputPrompt,"onUpdate:modelValue":f[0]||(f[0]=B=>u.inputPrompt=B),placeholder:"输入你的问题...",style:{height:"40px",border:"none !important",flex:"1"},onKeyup:e_(u.onSubmit,["enter"]),disabled:u.isLoading},null,8,["modelValue","onKeyup","disabled"]),m(L,{link:"",onClick:u.onSubmit,style:{width:"40px"},loading:u.isLoading,disabled:!((K=u.inputPrompt)!=null&&K.trim())||u.isLoading},{default:S(()=>[u.isLoading?ot("",!0):(J(),le("img",um))]),_:1},8,["onClick","loading","disabled"])])]}),_:1})]),_:1})]),_:1})])}const fm=Qe(om,[["render",lm],["__scopeId","data-v-c75e4363"]]),dm={name:"AgentPreview",components:{ChatContainer:f_,AgentInput:fm},props:{agentId:{type:Number,default:0}},data(){return{prompt:"",isLoading:!1,currentQuestion:"",currentAnswer:"",conversations:[],showOutput:!1,docUrls:[],docLinks:[],rawBuffer:"",thinkBuffer:""}},methods:{async startChat(u){var y;const{prompt:f,agentId:a}=u;this.isLoading=!0,this.currentQuestion=f,this.currentAnswer="",this.prompt="",this.showOutput=!0,this.docUrls=[],this.docLinks=[],this.rawBuffer="",this.thinkBuffer="";try{const b=await fetch("/rag/api/agent/stream?stream=true",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:this.currentQuestion,agentId:a})});if(!b.ok)throw new Error("Network response was not ok");const I=(y=b.body)==null?void 0:y.getReader(),D=new TextDecoder("utf-8");if(I){let L="";for(;;){const{value:M,done:q}=await I.read();if(q)break;const x=D.decode(M,{stream:!0});L+=x;const K=L.split(`
`);L=K.pop()||"";for(const B of K)if(B.startsWith(":"),B.startsWith('data:"')){const F=B.substring(6,B.length-1);if(F.startsWith("docurl:")){const k=F.substring(7).split("-");if(k.length>=3){const R=k[0],ye=k[1],qe=k.slice(2).join("-").replace(/\\n/g,"").trim();this.docLinks.push({id:R,fileName:qe,docId:ye}),await this.updateDocumentsDisplay()}this.docUrls.push(F);continue}if(F.startsWith("think:")){const Q=F.substring(6);this.thinkBuffer+=Q,await this.updateDocumentsDisplay();continue}this.rawBuffer+=F,await this.updateDocumentsDisplay()}}if(L.startsWith('data:"')){const M=L.substring(6,L.length-1);if(M.startsWith("docurl:")){const x=M.substring(7).split("-");if(x.length>=3){const K=x[0],B=x[1],F=x.slice(2).join("-").replace(/\\n/g,"").trim();this.docLinks.push({id:K,fileName:F,docId:B}),await this.updateDocumentsDisplay()}this.docUrls.push(M)}else if(M.startsWith("think:")){const q=M.substring(6);this.thinkBuffer+=q,await this.updateDocumentsDisplay()}else this.rawBuffer+=M,await this.updateDocumentsDisplay()}await this.updateDocumentsDisplay(!0),this.conversations.push({question:this.currentQuestion,answer:this.currentAnswer,docLinks:[]}),this.currentQuestion="",this.currentAnswer="",this.docLinks=[],this.rawBuffer=""}}catch(b){console.error("Error:",b),$e.error("请求失败，请重试"),this.currentAnswer="请求失败，请重试"}finally{this.isLoading=!1}},setupDocumentListeners(){document.querySelectorAll(".reference-docs-header, .think-process-header").forEach(u=>{u.getAttribute("data-has-click")||(u.setAttribute("data-has-click","true"),u.addEventListener("click",function(){const f=this.nextElementSibling,a=this.querySelector(".custom-arrow");f.style.display==="none"?(f.style.display="flex",a&&a.classList.remove("arrow-right"),a&&a.classList.add("arrow-down")):(f.style.display="none",a&&a.classList.remove("arrow-down"),a&&a.classList.add("arrow-right"))}))})},async updateDocumentsDisplay(u=!1){let f=await ka(this.rawBuffer.replace(/\\"/g,'"').replace(/\\n/g,`
`)),a="";if(this.docLinks.length>0){const y=document.createElement("div");y.className="reference-docs-container",y.id="docs-container";const b=document.createElement("div");b.className="reference-docs-header";const I=document.createElement("span");I.className="reference-docs-title",I.textContent="参考文档 ";const D=document.createElement("span");D.className="custom-arrow arrow-down",I.appendChild(D),b.appendChild(I);const L=document.createElement("div");L.className="reference-docs-content",this.docLinks.forEach((M,q)=>{const x=document.createElement("div");x.className="doc-link-item";const K=document.createElement("span");K.textContent=`文档 ${q+1}： `;const B=document.createElement("a");B.href=`${window.location.origin}/#/knowledge/docs?id=${M.id}&docId=${M.docId}`,B.target="_blank",B.textContent=M.fileName,x.appendChild(K),x.appendChild(B),L.appendChild(x)}),y.appendChild(b),y.appendChild(L),a+=y.outerHTML}if(this.thinkBuffer.trim()){const y=document.createElement("div");y.className="think-process-container",y.id="think-container";const b=document.createElement("div");b.className="think-process-header";const I=document.createElement("span");I.className="think-process-title",I.textContent="思考和行动过程 ";const D=document.createElement("span");D.className="custom-arrow arrow-down",I.appendChild(D),b.appendChild(I);const L=document.createElement("div");L.className="think-process-content",L.style.display="flex";const M=await ka(this.thinkBuffer.replace(/\\"/g,'"').replace(/\\n/g,`
`));L.innerHTML=M,y.appendChild(b),y.appendChild(L),a+=y.outerHTML}this.currentAnswer=a+f,u||(await za(),this.setupDocumentListeners())}},mounted(){this.$nextTick(()=>{this.setupDocumentListeners();const u=new MutationObserver(()=>{this.setupDocumentListeners()}),f=document.querySelector(".total-css");f&&u.observe(f,{childList:!0,subtree:!0})})}},cm={class:"agent-preview"},pm={class:"chat-container-wrapper"},gm={class:"input-container"};function hm(u,f,a,y,b,I){const D=Gn,L=kn,M=he("chat-container"),q=he("agent-input");return J(),le("div",cm,[P("div",pm,[m(M,{conversations:b.conversations,"current-question":b.currentQuestion,"current-answer":b.currentAnswer},{empty:S(()=>[m(L,{justify:"center"},{default:S(()=>[m(D,{span:20},{default:S(()=>f[1]||(f[1]=[P("div",{style:{"text-align":"center","font-size":"10px","font-family":"'Courier New', Courier, monospace"}},null,-1)])),_:1})]),_:1}),b.showOutput?ot("",!0):(J(),on(L,{key:0,style:{"min-height":"400px"}},{default:S(()=>[m(D,null,{default:S(()=>[m(L,{style:{"margin-top":"100px"}},{default:S(()=>[m(D,{span:24,style:{display:"flex","justify-content":"center","align-items":"center",height:"100%"}},{default:S(()=>f[2]||(f[2]=[P("img",{src:d_,style:{width:"72px",height:"72px"}},null,-1)])),_:1})]),_:1}),m(L,null,{default:S(()=>[m(D,{span:24,style:{display:"flex","justify-content":"center","align-items":"center",height:"100%"}},{default:S(()=>f[3]||(f[3]=[P("span",null,"我的Agent",-1)])),_:1})]),_:1})]),_:1})]),_:1}))]),_:1},8,["conversations","current-question","current-answer"])]),P("div",gm,[m(q,{modelValue:b.prompt,"onUpdate:modelValue":f[0]||(f[0]=x=>b.prompt=x),agentId:a.agentId,isLoading:b.isLoading,onSubmit:I.startChat},null,8,["modelValue","agentId","isLoading","onSubmit"])])])}const _m=Qe(dm,[["render",hm],["__scopeId","data-v-92967e90"]]),mm={name:"AgentTable",props:{groupId:{type:Number,default:0},embeddingModelId:{type:[Number,String],default:""}},data(){return{selectedRows:[],tableData:[],allTableData:[],activeTab:"",tableNames:[],selectedTabData:{}}},watch:{groupId:{immediate:!0,handler(u){u&&u!==0&&this.getTableData()}},embeddingModelId:{immediate:!0,handler(){this.allTableData&&this.allTableData.length>0&&(this.tableData=this.filterTableData(this.allTableData),this.initTabs())}}},methods:{getTableData(){!this.groupId||this.groupId===0||c_(this.groupId).then(u=>{this.allTableData=u.data,this.tableData=this.filterTableData(u.data),this.initTabs()}).catch(u=>{console.error("获取知识库数据失败:",u),this.allTableData=[],this.tableData=[],this.tableNames=[],this.activeTab=""})},initTabs(){const u=new Set;this.tableData.forEach(f=>{f.tableName&&u.add(f.tableName)}),this.tableNames=Array.from(u),this.tableNames.length>0&&!this.activeTab&&(this.activeTab=this.tableNames[0]),this.tableNames.forEach(f=>{this.selectedTabData[f]||(this.selectedTabData[f]=[])})},filterTableData(u){if(!u||u.length===0)return[];if(this.embeddingModelId){const f=typeof this.embeddingModelId=="string"?parseInt(this.embeddingModelId,10):this.embeddingModelId;return u.filter(a=>a.embeddingModelId===f)}else return u},getTableDataByTab(u){return this.tableData.filter(f=>f.tableName===u)},handleTabChange(u){this.activeTab=u,this.selectedRows=this.selectedTabData[u]||[],this.$emit("updateValue",this.selectedRows)},handleSelectionChange(u){this.selectedTabData[this.activeTab]=u,this.selectedRows=u,this.$emit("updateValue",this.selectedRows)}}};function vm(u,f,a,y,b,I){const D=oo,L=io,M=t_,q=n_;return J(),le("div",null,[m(q,{modelValue:b.activeTab,"onUpdate:modelValue":f[0]||(f[0]=x=>b.activeTab=x),onTabChange:I.handleTabChange},{default:S(()=>[(J(!0),le(st,null,at(b.tableNames,x=>(J(),on(M,{key:x,label:x,name:x},{default:S(()=>[m(L,{onSelectionChange:I.handleSelectionChange,data:I.getTableDataByTab(x),ref_for:!0,ref:"tableRefs"},{default:S(()=>[m(D,{type:"selection",width:"55"}),m(D,{property:"name",label:"名字",width:"300"}),m(D,{property:"description",label:"描述",width:"400"})]),_:2},1032,["onSelectionChange","data"])]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue","onTabChange"])])}const wm=Qe(mm,[["render",vm]]),Sm={components:{AgentHeader:__,AgentConfig:im,AgentPreview:_m,AgentTable:wm,UserFilled:Ya,ViewIcon:i_,Search:r_},data(){return{agentData:{id:0,imageUrl:"",name:"",description:"",groupId:0,responsePrompt:"",rulePrompt:"",rolePrompt:"qa",recallCount:1,similarity:.1,modelId:"",embeddingModelId:"",selectedRows:[],knowledgeBaseIds:"",mcpServerIds:"",selectedMcpServers:[],flowNodes:[]},dialogTableVisible:!1,mcpServerDialogVisible:!1,isLoading:!1,isLoadingFaBu:!0,isLoadingUpdate:!1,baseURL:window.location.origin+"/rag/api/agent/image/upload",modelOptions:[],embeddingModelOptions:[],Edit:Rr,mcpServers:[],mcpSearchKeyword:"",selectedServersInOrder:[],nextOrder:1}},computed:{filteredMcpServers(){if(!this.mcpSearchKeyword)return this.mcpServers;const u=this.mcpSearchKeyword.toLowerCase();return this.mcpServers.filter(f=>f.name.toLowerCase().includes(u))},sortedSelectedServers(){return[...this.selectedServersInOrder].sort((u,f)=>u.order-f.order)}},watch:{mcpServerDialogVisible(u){u&&this.initSelectedMCPServers()},"agentData.embeddingModelId"(u,f){f&&f!==""&&u!==f&&(this.agentData.selectedRows=[],this.agentData.knowledgeBaseIds="")}},created(){this.initData()},methods:{handleOpenDialog(){this.dialogTableVisible=!0},handleOpenMCPServerDialog(){this.fetchMCPServers(),this.mcpServerDialogVisible=!0},initSelectedMCPServers(){this.selectedServersInOrder=[],this.nextOrder=1,this.agentData.flowNodes&&this.agentData.flowNodes.length>0&&[...this.agentData.flowNodes].sort((f,a)=>f.numbering-a.numbering).forEach(f=>{this.selectedServersInOrder.push({id:f.mcpServerId,order:this.nextOrder++,description:f.description||""})})},isServerSelected(u){return this.selectedServersInOrder.some(f=>f.id===u)},getServerOrder(u){const f=this.selectedServersInOrder.find(a=>a.id===u);return f?f.order:""},getServerData(u){const f=this.selectedServersInOrder.find(a=>a.id===u);return f?("description"in f||(f.description=""),f):{description:""}},toggleServer(u,f){if(f){const a=this.mcpServers.find(y=>y.id===u);this.selectedServersInOrder.push({id:u,order:this.nextOrder++,description:a.description||""})}else{const a=this.selectedServersInOrder.findIndex(y=>y.id===u);a!==-1&&(this.selectedServersInOrder.splice(a,1),this.reorderSelectedServers())}},reorderSelectedServers(){this.selectedServersInOrder.sort((u,f)=>u.order-f.order),this.selectedServersInOrder.forEach((u,f)=>{u.order=f+1}),this.nextOrder=this.selectedServersInOrder.length+1},confirmMCPServerSelection(){const u=this.selectedServersInOrder.map((f,a)=>{const y=this.mcpServers.find(I=>I.id===f.id),b=this.agentData.flowNodes?this.agentData.flowNodes.find(I=>I.mcpServerId===f.id):null;return b?{...b,numbering:a,description:y.description||""}:{mcpServerId:f.id,mcpServerName:y.name,numbering:a,description:y.description||""}});this.agentData.flowNodes=u,this.mcpServerDialogVisible=!1},handleMCPServerSelected(u){const f=this.agentData.flowNodes?this.agentData.flowNodes.map(b=>b.mcpServerId):[],a=u.filter(b=>!f.includes(b.mcpServerId)),y=[...this.agentData.flowNodes||[],...a];this.agentData.flowNodes=y},initData(){this.getModels(),this.getEmbeddingModels(),this.baseURL=window.location.origin+"/rag/api/agent/image/upload";const u=this.$route.query.id;this.agentData.id=typeof u=="string"&&parseInt(u,10)||0,this.agentData.id!==0&&this.loadAgentData()},async loadAgentData(){try{const u=await to.getAgentById(this.agentData.id);u.code===200&&(this.isLoadingFaBu=!1,this.isLoadingUpdate=!0,Object.assign(this.agentData,{similarity:u.data.similarity,recallCount:u.data.recallCount,name:u.data.name,description:u.data.description,imageUrl:u.data.url,modelId:u.data.modelId,embeddingModelId:u.data.embeddingModelId||"",knowledgeBaseIds:u.data.knowledgeBaseIds,selectedRows:u.data.knowledgeBaseList||[],rulePrompt:u.data.rulePrompt,rolePrompt:u.data.rolePrompt,responsePrompt:u.data.responsePrompt,groupId:u.data.groupId,flowNodes:u.data.flowNodes||[]}))}catch(u){console.error("加载Agent数据失败:",u)}},async getModels(){try{const u=await Ga.getChatModels();u.code===200&&(this.modelOptions=u.data)}catch(u){console.error("获取模型列表失败:",u)}},async getEmbeddingModels(){try{const u=await Ga.getEmbddingModels();u.code===200&&(this.embeddingModelOptions=u.data)}catch(u){console.error("获取切词模型列表失败:",u)}},updateAgentData(u){this.agentData={...this.agentData,...u}},handleSelectedData(u){this.agentData.selectedRows=u,this.agentData.knowledgeBaseIds=u.map(f=>f.id).join(";")},checkData(){const u=[{field:"name",message:"Agent名称为空，请填写！"},{field:"description",message:"描述为空，请填写！"},{field:"groupId",message:"工作组为空，请选择！"},{field:"recallCount",message:"召回次数为空，请填写！"},{field:"similarity",message:"相似度为空，请填写！"},{field:"modelId",message:"模型ID为空，请填写！"}];for(const{field:f,message:a}of u)if(!this.agentData[f])return this.$message.error(a),!1;return!0},async saveAgent(){this.checkData()&&this.$refs.agentConfigRef&&await this.$refs.agentConfigRef.createAgent()},async updateAgent(){this.checkData()&&this.$refs.agentConfigRef&&await this.$refs.agentConfigRef.updateAgent()},handleCreateSuccess(u){this.isLoadingUpdate=!0,this.isLoadingFaBu=!1,this.agentData.id=u},async fetchMCPServers(){try{const u=await p_.getListByGroup(this.agentData.groupId);u.code===200?this.mcpServers=u.data.map(f=>({id:f.id,name:f.name,description:f.description||""})):console.error("获取MCP服务器列表失败:",u.message)}catch(u){console.error("获取MCP服务器列表异常:",u)}},getMCPServerName(u){const f=this.mcpServers.find(a=>a.id===u);return f?f.name:`服务器 ${u}`}}},bm={class:"agent-app"},ym={key:0,class:"selected-servers-display"},Cm={class:"selected-servers-list"},Im={class:"server-order-badge"},xm={class:"selected-server-name"},Am={class:"server-list-container"},Dm={class:"server-list-header"},Lm={class:"server-count"},Em={class:"server-checkbox"},Pm=["onClick"],Tm={key:0,class:"checkbox-number"},Mm={class:"server-info"},Rm={class:"server-name"},Om={class:"dialog-footer"},Um={class:"card-container"},Bm={class:"section-header"},Nm={class:"section-header"};function Fm(u,f,a,y,b,I){const D=he("agent-header"),L=he("agent-table"),M=Ka,q=he("Search"),x=ro,K=Tr,B=s_,F=o_,Q=a_,k=ut,R=he("UserFilled"),ye=he("agent-config"),qe=lt,Le=he("ViewIcon"),sn=he("agent-preview");return J(),le("div",bm,[m(D,{"is-loading-fa-bu":b.isLoadingFaBu,"is-loading-update":b.isLoadingUpdate,onSave:I.saveAgent,onUpdate:I.updateAgent},null,8,["is-loading-fa-bu","is-loading-update","onSave","onUpdate"]),m(M,{modelValue:b.dialogTableVisible,"onUpdate:modelValue":f[0]||(f[0]=Y=>b.dialogTableVisible=Y),title:"知识库关联"},{default:S(()=>[m(L,{onUpdateValue:I.handleSelectedData,groupId:b.agentData.groupId,embeddingModelId:b.agentData.embeddingModelId},null,8,["onUpdateValue","groupId","embeddingModelId"])]),_:1},8,["modelValue"]),m(M,{modelValue:b.mcpServerDialogVisible,"onUpdate:modelValue":f[3]||(f[3]=Y=>b.mcpServerDialogVisible=Y),title:"关联MCP服务器",width:"600px","destroy-on-close":""},{footer:S(()=>[P("div",Om,[m(k,{onClick:f[2]||(f[2]=Y=>b.mcpServerDialogVisible=!1)},{default:S(()=>f[6]||(f[6]=[De("取消")])),_:1}),m(k,{type:"primary",onClick:I.confirmMCPServerSelection,disabled:b.selectedServersInOrder.length===0},{default:S(()=>f[7]||(f[7]=[De(" 确定 ")])),_:1},8,["onClick","disabled"])])]),default:S(()=>[m(F,null,{default:S(()=>[m(B,null,{default:S(()=>[m(K,{modelValue:b.mcpSearchKeyword,"onUpdate:modelValue":f[1]||(f[1]=Y=>b.mcpSearchKeyword=Y),placeholder:"搜索 MCP Server",clearable:""},{prefix:S(()=>[m(x,null,{default:S(()=>[m(q)]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),b.selectedServersInOrder.length>0?(J(),le("div",ym,[f[4]||(f[4]=P("div",{class:"selected-servers-header"},[P("span",null,"已选执行顺序")],-1)),P("div",Cm,[(J(!0),le(st,null,at(I.sortedSelectedServers,Y=>(J(),le("div",{key:Y.id,class:"selected-server-tag"},[P("span",Im,Dn(Y.order),1),P("span",xm,Dn(I.getMCPServerName(Y.id)),1)]))),128))])])):ot("",!0),P("div",Am,[P("div",Dm,[f[5]||(f[5]=P("span",null,"可选 MCP Server （选择顺序决定执行顺序）",-1)),P("span",Lm,Dn(I.filteredMcpServers.length)+"个",1)]),m(Q,{height:"300px"},{default:S(()=>[(J(!0),le(st,null,at(I.filteredMcpServers,Y=>(J(),le("div",{class:"server-item",key:Y.id},[P("div",Em,[P("div",{class:u_(["custom-order-checkbox",{"is-selected":I.isServerSelected(Y.id)}]),onClick:mn=>I.toggleServer(Y.id,!I.isServerSelected(Y.id))},[I.isServerSelected(Y.id)?(J(),le("span",Tm,Dn(I.getServerOrder(Y.id)),1)):ot("",!0)],10,Pm),P("div",Mm,[P("span",Rm,Dn(Y.id)+" - "+Dn(Y.name)+" - "+Dn(Y.description),1)])])]))),128))]),_:1})])]),_:1},8,["modelValue"]),P("div",Um,[m(qe,{class:"wide-card"},{header:S(()=>[P("span",Bm,[m(x,null,{default:S(()=>[m(R)]),_:1}),f[8]||(f[8]=De(" 应用设定 "))])]),default:S(()=>[m(ye,{baseURL:b.baseURL,"agent-data":b.agentData,"model-options":b.modelOptions,"embedding-model-options":b.embeddingModelOptions,"dialog-visible":b.dialogTableVisible,"onUpdate:agentData":I.updateAgentData,onOpenAssociationDialog:I.handleOpenDialog,onOpenMCPServerDialog:I.handleOpenMCPServerDialog,onCreateSuccess:I.handleCreateSuccess,ref:"agentConfigRef"},null,8,["baseURL","agent-data","model-options","embedding-model-options","dialog-visible","onUpdate:agentData","onOpenAssociationDialog","onOpenMCPServerDialog","onCreateSuccess"])]),_:1}),m(qe,{class:"narrow-card"},{header:S(()=>[P("span",Nm,[m(x,null,{default:S(()=>[m(Le)]),_:1}),f[9]||(f[9]=De(" 预览和调试 "))])]),default:S(()=>[m(sn,{"agent-id":b.agentData.id},null,8,["agent-id"])]),_:1})])])}const ov=Qe(Sm,[["render",Fm],["__scopeId","data-v-26148f10"]]);export{ov as default};
