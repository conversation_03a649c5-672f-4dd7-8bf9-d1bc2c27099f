/* empty css                  *//* empty css               */import{_ as I,I as N,C as B}from"./play-BRgNKOUh.js";/* empty css                *//* empty css                  *//* empty css               *//* empty css                     */import{h as b,g as L,_ as E,q as y,o as g,a as m,w as f,m as v,n as S,U as $,d as x,V as T,F as O,t as Q,c as U,W,X as q,Y as M,Z as j,G as F,$ as K,r as k}from"./index-BQ_QpmzA.js";import{a as G}from"./AgentApi-Ch0FWouw.js";import{r as _}from"./renderMarkdown-CDCJAqU1.js";/* empty css                  */const C="lastSelectedAgent",H=b({name:"ChatInput",props:{isLoading:Boolean,options:{type:Array,default:()=>[]},modelValue:String},emits:["update:modelValue","submit"],data(){return{selectedValue:this.getLastSelectedAgent(),isOptionsLoaded:!1}},computed:{prompt:{get(){return this.modelValue},set(e){this.$emit("update:modelValue",e)}}},watch:{options:{immediate:!0,handler(e){e.length>0&&!this.isOptionsLoaded&&(this.isOptionsLoaded=!0,this.selectedValue=this.getLastSelectedAgent()),this.selectedValue&&!e.some(t=>t.value===this.selectedValue)&&(this.selectedValue="")}},selectedValue(e){e?localStorage.setItem(C,e):localStorage.removeItem(C)}},methods:{onSubmit(){this.selectedValue&&this.prompt.trim()?this.$emit("submit",{prompt:this.prompt,agentId:this.selectedValue}):L.error("请选择Agent并输入问题")},getLastSelectedAgent(){try{const e=localStorage.getItem(C);if(!e||!this.options.length)return console.log("No stored value or options not loaded"),"";const t=this.options.find(n=>String(n.value)===String(e));return t?t.value:(console.log("No matching option found"),"")}catch{return""}}}}),R={class:"input-container"},Y={class:"grid-content bg-purple-dark",style:{margin:"10px",display:"flex","justify-content":"flex-end","align-items":"center"}},z={key:0,src:I,height:"25px",width:"25px"};function J(e,t,n,o,u,l){const i=W,s=T,r=q,p=j,c=$,d=S,a=v;return g(),y("div",R,[m(a,{justify:"center",style:{"margin-top":"20px"}},{default:f(()=>[m(d,{span:22,style:{"margin-left":"10px","border-radius":"12px"}},{default:f(()=>[m(c,{"body-style":{padding:"0px"},class:"fixed-input"},{default:f(()=>[x("div",Y,[m(s,{modelValue:e.selectedValue,"onUpdate:modelValue":t[0]||(t[0]=h=>e.selectedValue=h),placeholder:"Agent",size:"large",style:{width:"150px"},disabled:e.isLoading},{default:f(()=>[(g(!0),y(O,null,Q(e.options,h=>(g(),U(i,{key:h.value,label:h.label,value:h.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"]),m(r,{modelValue:e.prompt,"onUpdate:modelValue":t[1]||(t[1]=h=>e.prompt=h),placeholder:"输入你的问题...",style:{height:"40px",border:"none !important",flex:"1","margin-left":"10px","margin-right":"10px"},onKeyup:M(e.onSubmit,["enter"]),disabled:e.isLoading},null,8,["modelValue","onKeyup","disabled"]),m(p,{link:"",onClick:e.onSubmit,style:{width:"40px"},loading:e.isLoading,disabled:!e.prompt.trim()||e.isLoading},{default:f(()=>[e.isLoading?F("",!0):(g(),y("img",z))]),_:1},8,["onClick","loading","disabled"])])]),_:1})]),_:1})]),_:1})])}const P=E(H,[["render",J],["__scopeId","data-v-b44c4780"]]),X=b({name:"ChatView",components:{ChatContainer:B,ChatInput:P,IndexAction:N},data(){return{prompt:"",isLoading:!1,currentQuestion:"",currentAnswer:"",conversations:[],options:[],selectedValue:"",docUrls:[],docLinks:[],rawBuffer:"",thinkBuffer:""}},async created(){this.getAgentData()},methods:{getAgentData(){const e={type:0,keyword:"",username:""};G.getAgent(e).then(t=>{t.code===200&&(this.options=t.data.map(n=>({value:n.id,label:n.name})))})},async startChat({prompt:e,agentId:t}){var n;this.isLoading=!0,this.currentQuestion=e,this.currentAnswer="",this.prompt="",this.docUrls=[],this.docLinks=[],this.rawBuffer="",this.thinkBuffer="";try{const o=await fetch("/rag/api/agent/stream?stream=true",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:this.currentQuestion,agentId:t})});if(!o.ok)throw new Error("Network response was not ok");const u=(n=o.body)==null?void 0:n.getReader(),l=new TextDecoder("utf-8");if(u){let i="";for(;;){const{value:s,done:r}=await u.read();if(r)break;const p=l.decode(s,{stream:!0});i+=p;const c=i.split(`
`);i=c.pop()||"";for(const d of c)if(d.startsWith(":"),d.startsWith('data:"')){const a=d.substring(6,d.length-1);if(a.startsWith("docurl:")){const w=a.substring(7).split("-");if(w.length>=3){const V=w[0],A=w[1],D=w.slice(2).join("-").replace(/\\n/g,"").trim();this.docLinks.push({id:V,fileName:D,docId:A}),await this.updateDocumentsDisplay()}this.docUrls.push(a);continue}if(a.startsWith("think:")){const h=a.substring(6);this.thinkBuffer+=h,await this.updateDocumentsDisplay();continue}this.rawBuffer+=a,await this.updateDocumentsDisplay()}}if(i.startsWith('data:"')){const s=i.substring(6,i.length-1);if(s.startsWith("docurl:")){const p=s.substring(7).split("-");if(p.length>=3){const c=p[0],d=p[1],a=p.slice(2).join("-").replace(/\\n/g,"").trim();this.docLinks.push({id:c,fileName:a,docId:d}),await this.updateDocumentsDisplay()}this.docUrls.push(s)}else if(s.startsWith("think:")){const r=s.substring(6);this.thinkBuffer+=r,await this.updateDocumentsDisplay()}else this.rawBuffer+=s,await this.updateDocumentsDisplay()}await this.updateDocumentsDisplay(!0),this.conversations.push({question:this.currentQuestion,answer:this.currentAnswer,docLinks:[]}),this.currentQuestion="",this.currentAnswer="",this.docLinks=[],this.rawBuffer=""}}catch(o){console.error("Error:",o),L.error("请求失败，请重试"),this.currentAnswer="请求失败，请重试"}finally{this.isLoading=!1}},setupDocumentListeners(){document.querySelectorAll(".reference-docs-header, .think-process-header").forEach(e=>{e.getAttribute("data-has-click")||(e.setAttribute("data-has-click","true"),e.addEventListener("click",function(){const t=this.nextElementSibling,n=this.querySelector(".custom-arrow");t.style.display==="none"?(t.style.display="flex",n&&n.classList.remove("arrow-right"),n&&n.classList.add("arrow-down")):(t.style.display="none",n&&n.classList.remove("arrow-down"),n&&n.classList.add("arrow-right"))}))})},async updateDocumentsDisplay(e=!1){let t=await _(this.rawBuffer.replace(/\\"/g,'"').replace(/\\n/g,`
`)),n="";if(this.docLinks.length>0){const o=document.createElement("div");o.className="reference-docs-container",o.id="docs-container";const u=document.createElement("div");u.className="reference-docs-header";const l=document.createElement("span");l.className="reference-docs-title",l.textContent="参考文档 ";const i=document.createElement("span");i.className="custom-arrow arrow-down",l.appendChild(i),u.appendChild(l);const s=document.createElement("div");s.className="reference-docs-content",this.docLinks.forEach((r,p)=>{const c=document.createElement("div");c.className="doc-link-item";const d=document.createElement("span");d.textContent=`文档 ${p+1}： `;const a=document.createElement("a");a.href=`${window.location.origin}/#/knowledge/docs?id=${r.id}&docId=${r.docId}`,a.target="_blank",a.textContent=r.fileName,c.appendChild(d),c.appendChild(a),s.appendChild(c)}),o.appendChild(u),o.appendChild(s),n+=o.outerHTML}if(this.thinkBuffer.trim()){const o=document.createElement("div");o.className="think-process-container",o.id="think-container";const u=document.createElement("div");u.className="think-process-header";const l=document.createElement("span");l.className="think-process-title",l.textContent="思考和行动过程 ";const i=document.createElement("span");i.className="custom-arrow arrow-down",l.appendChild(i),u.appendChild(l);const s=document.createElement("div");s.className="think-process-content",s.style.display="flex";const r=await _(this.thinkBuffer.replace(/\\"/g,'"').replace(/\\n/g,`
`));s.innerHTML=r,o.appendChild(u),o.appendChild(s),n+=o.outerHTML}this.currentAnswer=n+t,e||(await K(),this.setupDocumentListeners())}},mounted(){this.$nextTick(()=>{this.setupDocumentListeners();const e=new MutationObserver(()=>{this.setupDocumentListeners()}),t=document.querySelector(".total-css");t&&e.observe(t,{childList:!0,subtree:!0})})}}),Z={class:"content-home-page"};function ee(e,t,n,o,u,l){const i=k("indexAction"),s=S,r=v,p=k("ChatContainer"),c=k("ChatInput");return g(),y("div",Z,[m(p,{conversations:e.conversations,currentQuestion:e.currentQuestion,currentAnswer:e.currentAnswer},{empty:f(()=>[m(r,{style:{"min-height":"200px"}},{default:f(()=>[m(s,null,{default:f(()=>[m(i)]),_:1})]),_:1})]),_:1},8,["conversations","currentQuestion","currentAnswer"]),m(c,{modelValue:e.prompt,"onUpdate:modelValue":t[0]||(t[0]=d=>e.prompt=d),isLoading:e.isLoading,options:e.options,onSubmit:e.startChat},null,8,["modelValue","isLoading","options","onSubmit"])])}const pe=E(X,[["render",ee],["__scopeId","data-v-ec055eba"]]);export{pe as default};
