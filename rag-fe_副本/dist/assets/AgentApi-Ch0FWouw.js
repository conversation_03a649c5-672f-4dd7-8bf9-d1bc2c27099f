var _=Object.defineProperty;var p=(e,t,R)=>t in e?_(e,t,{enumerable:!0,configurable:!0,writable:!0,value:R}):e[t]=R;var a=(e,t,R)=>p(e,typeof t!="symbol"?t+"":t,R);import{an as n}from"./index-BQ_QpmzA.js";const U="/rag/api";var g=(e=>(e.QUERY_URL=U+"/agent/query",e.QUERY_PAGING_URL=U+"/agent/query/paging",e.SAVE_URL=U+"/agent/add",e.UPDATE_URL=U+"/agent/update",e.QUERY_BY_ID_URL=U+"/agent/detail",e))(g||{});class A{constructor(){a(this,"getAgent",t=>n.post(g.QUERY_URL,t));a(this,"getAgentPaging",t=>n.post(g.QUERY_PAGING_URL,t));a(this,"saveAgent",t=>n.post(g.SAVE_URL,t));a(this,"getAgentById",t=>n.get(`${g.QUERY_BY_ID_URL}?id=${t}`));a(this,"updateAgent",t=>n.put(g.UPDATE_URL,t))}}const s=new A;export{s as a};
