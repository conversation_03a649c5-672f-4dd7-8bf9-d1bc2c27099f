import{aP as s,a0 as l,a3 as u}from"./index-BQ_QpmzA.js";const i=s("message",()=>{const t=l(0);let r=null;const a=async()=>{try{const e=await u.getUnreadMessages();return t.value=(e==null?void 0:e.code)===200?e.data:0,t.value}catch(e){return console.error("获取未读消息数失败",e),t.value=0,0}},o=(e=3e5)=>{n(),a(),r=window.setInterval(a,e)},n=()=>{r&&(clearInterval(r),r=null)};return{unreadCount:t,fetchUnreadCount:a,startPolling:o,stopPolling:n,resetUnreadCount:()=>{t.value=0}}});export{i as u};
