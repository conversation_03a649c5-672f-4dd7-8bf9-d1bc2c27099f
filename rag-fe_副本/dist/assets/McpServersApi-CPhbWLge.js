var R=Object.defineProperty;var _=(e,r,L)=>r in e?R(e,r,{enumerable:!0,configurable:!0,writable:!0,value:L}):e[r]=L;var U=(e,r,L)=>_(e,typeof r!="symbol"?r+"":r,L);import{an as s}from"./index-BQ_QpmzA.js";const t="/rag/api";var p=(e=>(e.ADD_URL=t+"/mcpserver/add",e.DELETE_URL=t+"/mcpserver/delete",e.UPDATE_URL=t+"/mcpserver/update",e.PAGING_URL=t+"/mcpserver/paging",e.CURRENT_LIST_URL=t+"/mcpserver/current/list",e.GROUP_LIST_URL=t+"/mcpserver/get/list/in/group",e))(p||{});class c{constructor(){U(this,"getListByGroup",r=>s.get(`${p.GROUP_LIST_URL}?groupId=${r}`))}create(r){return s.post(p.ADD_URL,r)}remove(r){return s.post(p.DELETE_URL,r)}edit(r){return s.put(p.UPDATE_URL,r)}getServers(r){return s.post(p.PAGING_URL,r)}}const a=new c;export{a as m};
