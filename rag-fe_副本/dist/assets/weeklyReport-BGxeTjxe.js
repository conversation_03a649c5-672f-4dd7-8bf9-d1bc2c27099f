/* empty css                  *//* empty css                   *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                     *//* empty css               */import{an as R,h as q,a0 as f,aQ as Q,aR as x,aS as B,aT as I,aU as O,aV as L,k as V,q as D,a5 as A,d as a,a6 as F,y as i,a as l,w as m,Z as Y,a7 as z,g as y,o as C,x as $,b as K,B as N,aB as P,aW as H,a9 as U,_ as Z}from"./index-BQ_QpmzA.js";const j={getWeeklyReportData(h){return R.post("rag/api/report/week",h)}},G={class:"weekly-report-container"},J={class:"summary-section"},X={class:"summary-header"},tt={class:"summary-title"},et={class:"update-time"},at={class:"refresh-action"},st={class:"summary-cards"},ot={class:"summary-card"},rt={class:"summary-number user-color"},nt={class:"summary-card"},lt={class:"summary-number kb-color"},ct={class:"summary-card"},ut={class:"summary-number agent-color"},dt={class:"summary-card"},it={class:"summary-number doc-color"},mt={class:"table-section"},pt={class:"table-container"},_t={class:"metric-title"},ft={class:"metric-number"},kt={class:"metric-number"},gt={class:"metric-number"},yt=q({__name:"weeklyReport",setup(h){const k=f(!1),p=f({userCount:0,kbCount:0,agentCount:0,docCount:0}),b=e=>{const t=e.getFullYear(),o=String(e.getMonth()+1).padStart(2,"0"),s=String(e.getDate()).padStart(2,"0"),r=String(e.getHours()).padStart(2,"0"),u=String(e.getMinutes()).padStart(2,"0"),d=String(e.getSeconds()).padStart(2,"0");return`${t}-${o}-${s} ${r}:${u}:${d}`},W=()=>{const e=new Date,t=e.getFullYear(),o=e.getMonth(),s=Math.floor(o/3)+1,r=(s-1)*3,u=new Date(t,r,1,0,0,0);return{quarter:s,year:t,firstDayOfQuarter:b(u),currentDate:b(e)}},_=W();console.log("季度信息:",_),f(`${_.year}年Q${_.quarter}`),f(`${_.firstDayOfQuarter.split(" ")[0]} 至 ${_.currentDate.split(" ")[0]}`);const g=f([{title:"用户数",icon:Q,color:"#1890ff",data:{preQ:0,curWeek:0,lastWeek:0}},{title:"知识库数",icon:x,color:"#52c41a",data:{preQ:0,curWeek:0,lastWeek:0}},{title:"文档数",icon:B,color:"#722ed1",data:{preQ:0,curWeek:0,lastWeek:0}},{title:"智能体数",icon:Q,color:"#fa8c16",data:{preQ:0,curWeek:0,lastWeek:0}},{title:"任务数",icon:I,color:"#eb2f96",data:{preQ:0,curWeek:0,lastWeek:0}},{title:"输入Token数",icon:O,color:"#13c2c2",data:{preQ:0,curWeek:0,lastWeek:0}},{title:"输出Token数",icon:L,color:"#f5222d",data:{preQ:0,curWeek:0,lastWeek:0}}]),c=e=>e.toLocaleString(),S=()=>{const e=new Date,t=e.getFullYear(),o=String(e.getMonth()+1).padStart(2,"0"),s=String(e.getDate()).padStart(2,"0");return`${t}年${o}月${s}日`},T=async()=>{const e=W();try{return(await j.getWeeklyReportData({firstDayOfQuarter:e.firstDayOfQuarter,currentDate:e.currentDate})).data}catch(t){throw console.error("API调用失败:",t),t}},M=e=>{var o,s,r,u;["userNum","kbNum","docNum","agentNum","taskNum","inputTokenNum","outputTokenNum"].forEach((d,n)=>{if(e[d]&&g.value[n]){const v=e[d];g.value[n].data={preQ:v[`${d}PreQ`],curWeek:v[`${d}CurWeek`],lastWeek:v[`${d}LastWeek`]}}}),p.value={userCount:((o=e.userNum)==null?void 0:o.userNumTotal)||0,kbCount:((s=e.kbNum)==null?void 0:s.kbNumTotal)||0,agentCount:((r=e.agentNum)==null?void 0:r.agentNumTotal)||0,docCount:((u=e.docNum)==null?void 0:u.docNumTotal)||0}},E=async()=>{try{let e=`| 指标项 | 上个季度 | QTD上周 | QTD本周 |
`;e+=`|-------|---------|---------|----------|
`,g.value.forEach(t=>{const o=t.title,s=c(t.data.preQ),r=c(t.data.lastWeek),u=c(t.data.curWeek);e+=`| ${o} | ${s} | ${r} | ${u} |
`}),await navigator.clipboard.writeText(e),y.success("表格已复制到剪贴板")}catch(e){console.error("复制失败:",e),y.error("复制失败，请稍后重试")}},w=async(e=!1)=>{k.value=!0;try{const t=await T();M(t),e&&y.success("数据刷新成功")}catch(t){console.error("刷新数据失败:",t),y.error("数据刷新失败，请稍后重试")}finally{k.value=!1}};return V(()=>{w(!1)}),(e,t)=>{const o=K,s=Y,r=U,u=z,d=F;return C(),D("div",G,[A((C(),D("div",J,[a("div",X,[a("div",tt,[t[1]||(t[1]=a("h2",null,"数据概览",-1)),a("div",et," 数据统计截止到："+i(S()),1)]),a("div",at,[l(s,{type:"default",onClick:E,size:"default"},{default:m(()=>[l(o,null,{default:m(()=>[l(N(P))]),_:1}),t[2]||(t[2]=$(" 复制表格 "))]),_:1}),l(s,{type:"primary",loading:k.value,onClick:t[0]||(t[0]=n=>w(!0)),size:"default"},{default:m(()=>[l(o,null,{default:m(()=>[l(N(H))]),_:1}),t[3]||(t[3]=$(" 刷新数据 "))]),_:1},8,["loading"])])]),a("div",st,[a("div",ot,[t[4]||(t[4]=a("div",{class:"card-header"},[a("span",{class:"card-title"},"用户总数")],-1)),a("div",rt,i(c(p.value.userCount)),1)]),a("div",nt,[t[5]||(t[5]=a("div",{class:"card-header"},[a("span",{class:"card-title"},"知识库总数")],-1)),a("div",lt,i(c(p.value.kbCount)),1)]),a("div",ct,[t[6]||(t[6]=a("div",{class:"card-header"},[a("span",{class:"card-title"},"智能体总数")],-1)),a("div",ut,i(c(p.value.agentCount)),1)]),a("div",dt,[t[7]||(t[7]=a("div",{class:"card-header"},[a("span",{class:"card-title"},"文档总数")],-1)),a("div",it,i(c(p.value.docCount)),1)])])])),[[d,k.value]]),a("div",mt,[a("div",pt,[l(u,{data:g.value,style:{width:"100%"},class:"report-table"},{default:m(()=>[l(r,{prop:"title",label:"指标项",width:"200",align:"left"},{default:m(n=>[a("span",_t,i(n.row.title),1)]),_:1}),l(r,{prop:"data.preQ",label:"上个Q基线",align:"center"},{default:m(n=>[a("span",ft,i(c(n.row.data.preQ)),1)]),_:1}),l(r,{prop:"data.lastWeek",label:"QTD上周",align:"center"},{default:m(n=>[a("span",kt,i(c(n.row.data.lastWeek)),1)]),_:1}),l(r,{prop:"data.curWeek",label:"QTD本周",align:"center"},{default:m(n=>[a("span",gt,i(c(n.row.data.curWeek)),1)]),_:1})]),_:1},8,["data"])])])])}}}),Ct=Z(yt,[["__scopeId","data-v-d91b4492"]]);export{Ct as default};
