/* empty css                  *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                    *//* empty css               *//* empty css                */import"./el-tooltip-l0sNRNKZ.js";import{h as E,ao as y,ap as k,k as S,aq as P,_ as b,q as f,o as m,a as i,w as c,F as D,t as x,m as F,c as w,n as U,U as V,d as a,f as B,y as h,ar as T,X as I,r as C,b as q,x as M,Z as j,ag as H,af as L,a8 as N}from"./index-BQ_QpmzA.js";import{a as R}from"./AgentApi-Ch0FWouw.js";const X=E({name:"AgentCards",components:{UserFilled:k,View:y},props:{detail:{type:Array,default:()=>[]}},emits:["update-page-size"],setup(e,{emit:s}){const r=()=>{const t=window.innerWidth;let n;t<768?n=1:t<992?n=2:t<1200?n=3:t<1600?n=4:n=6;const u=window.innerHeight,g=Math.max(3,Math.floor((u-200)/220));let l=n*g;return l=Math.floor(l/4)*4,l<4&&(l=4),s("update-page-size",l),l},d=()=>{r()};return S(()=>{r(),window.addEventListener("resize",d)}),P(()=>{window.removeEventListener("resize",d)}),{calcItemsPerPage:r}},methods:{jumpDetails(e){this.$router.push({path:"/apps/update",query:{id:e.id}})},isTextEllipsis(e){return e&&e.length>15}}}),J={class:"card-layout"},W={class:"card-image"},Z=["src"],G={class:"card-content"},K={class:"card-title"},O={class:"app-info"},Q={class:"app-name"},Y={class:"id-info-container"},$={class:"id-item"},ee={class:"id-value"},te={class:"id-item"},ae={class:"id-value"},se={class:"card-footer"},ne={class:"card-description"};function oe(e,s,r,d,t,n){const u=B,_=V,g=U,l=F;return m(),f("div",null,[i(l,{gutter:10,style:{width:"100%",margin:"0 auto"}},{default:c(()=>[(m(!0),f(D,null,x(e.detail,o=>(m(),w(g,{key:o.id,xs:24,sm:12,md:8,lg:6,xl:6,class:"card-col"},{default:c(()=>[i(_,{class:"agent-card","body-style":{padding:"16px"},shadow:"hover",onClick:v=>e.jumpDetails(o)},{default:c(()=>[a("div",J,[a("div",W,[a("img",{src:o.url?o.url:"https://agi-dev-platform-web.cdn.bcebos.com/ai_apaas/dist/img/education_bad9115a.png",alt:"agent-image",class:"square-image"},null,8,Z)]),a("div",G,[a("div",K,[a("div",O,[i(u,{content:o.name,placement:"top","show-after":200,"popper-class":"custom-tooltip",disabled:!e.isTextEllipsis(o.name)},{default:c(()=>[a("div",Q,h(o.name),1)]),_:2},1032,["content","disabled"])])]),a("div",Y,[a("div",$,[s[0]||(s[0]=a("span",{class:"id-label"},"Agent ID：",-1)),a("span",ee,h(o.id||"未知"),1)]),a("div",te,[s[1]||(s[1]=a("span",{class:"id-label"},"工作组 ID：",-1)),a("span",ae,h(o.groupId||"未知"),1)])])])]),a("div",se,[a("div",ne,[i(u,{content:o.description,placement:"top","show-after":200,"popper-class":"custom-tooltip",disabled:!e.isTextEllipsis(o.description)},{default:c(()=>[a("div",null,h(o.description),1)]),_:2},1032,["content","disabled"])])])]),_:2},1032,["onClick"])]),_:2},1024))),128))]),_:1})])}const ie=b(X,[["render",oe],["__scopeId","data-v-4447432b"]]),le={components:{AgentCards:ie,Search:T},created(){this.getAgentData(null)},data(){return{keyword:"",tabActive:"mine",agents:[],currentPage:1,pageSize:12,pageSizes:[12,24,36],total:0}},methods:{handleXinJianClick(){this.$router.push("/apps/update")},handleUpdatePageSize(e){this.pageSize=e,this.pageSizes=[e,e*2,e*3],this.currentPage=1,this.getAgentData(null)},getAgentData(e){var r;(r=e==null?void 0:e.props)!=null&&r.name&&(this.tabActive=e.props.name);const s={type:this.tabActive==="all"?0:1,page:this.currentPage,size:this.pageSize,keyword:this.keyword};R.getAgentPaging(s).then(d=>{d.code===200&&(this.agents=[],this.total=d.data.total||0,(d.data.items||[]).forEach(n=>{this.agents.push(n)}))})},handleCurrentChange(e){this.currentPage=e,this.getAgentData(null)},handleSizeChange(e){this.pageSize=e,this.currentPage=1,this.getAgentData(null)}}},re={style:{margin:"5px",background:"#fff",padding:"10px"}},de={class:"search-row"},ce={class:"agent-list-container"},pe={class:"pagination-container"};function ue(e,s,r,d,t,n){const u=I,_=C("Search"),g=q,l=j,o=H,v=L,z=C("agent-cards"),A=N;return m(),f("div",re,[a("div",de,[i(u,{style:{width:"250px"},modelValue:t.keyword,"onUpdate:modelValue":s[0]||(s[0]=p=>t.keyword=p),placeholder:"请输入关键词",clearable:""},null,8,["modelValue"]),i(g,{class:"search-icon",onClick:n.getAgentData},{default:c(()=>[i(_)]),_:1},8,["onClick"]),i(l,{type:"primary",class:"add-btn",onclick:n.handleXinJianClick},{default:c(()=>s[4]||(s[4]=[M(" 新建 ")])),_:1},8,["onclick"])]),i(v,{modelValue:t.tabActive,"onUpdate:modelValue":s[1]||(s[1]=p=>t.tabActive=p),class:"agent-tabs",onTabClick:n.getAgentData},{default:c(()=>[i(o,{label:"我发布的",name:"mine",lazy:!0}),i(o,{label:"全部",name:"all",lazy:!0})]),_:1},8,["modelValue","onTabClick"]),a("div",ce,[(m(),w(z,{key:t.tabActive,"tab-name":t.tabActive,detail:t.agents,onUpdatePageSize:n.handleUpdatePageSize},null,8,["tab-name","detail","onUpdatePageSize"]))]),a("div",pe,[i(A,{"current-page":t.currentPage,"onUpdate:currentPage":s[2]||(s[2]=p=>t.currentPage=p),"page-size":t.pageSize,"onUpdate:pageSize":s[3]||(s[3]=p=>t.pageSize=p),"page-sizes":t.pageSizes,layout:"total, sizes, prev, pager, next, jumper",total:t.total,onSizeChange:n.handleSizeChange,onCurrentChange:n.handleCurrentChange},null,8,["current-page","page-size","page-sizes","total","onSizeChange","onCurrentChange"])])])}const Ee=b(le,[["render",ue]]);export{Ee as default};
