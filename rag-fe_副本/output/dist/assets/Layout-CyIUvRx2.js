/* empty css                  *//* empty css               *//* empty css                  */import"./el-tooltip-l0sNRNKZ.js";/* empty css                     */import{_ as k,r as y,c as d,o,w as e,a as t,E as T,b as F,d as u,e as $,f as Y,g as B,h as U,u as ee,s as D,i as te,j as ne,k as oe,l as se,m as ae,n as le,p as ce,q as g,F as w,t as v,v as re,x as M,y as C,z as ue,A as de,B as h,C as ie,D as pe,G as E,H as me,I as _e,J as ge,K as Ae,L as b,M as fe,N as he,O as Ce,P as Ee,Q as Ie,R as ke,S as Qe}from"./index-LoRUQG_-.js";import{u as ye}from"./messageStore-B5ILOdNH.js";import"./GroupApi-C0GuTlOK.js";const Be={class:"tooltip-content"},Me={class:"instruction"},Se={class:"instruction"},G="baidu://message/?gid=11088845",be={__name:"GroupChat",setup(s){const a=p=>{navigator.clipboard.writeText(p).then(()=>{B.success(`已复制群号：${p}，请手动打开如流加入群聊`)}).catch(()=>{B.error(`复制失败，请手动记录群号：${p}`)})};return(p,n)=>{const m=y("ChatDotRound"),_=F,r=T,i=Y;return o(),d(i,{placement:"bottom-end",effect:"light",class:"group-tooltip"},{content:e(()=>[u("div",Be,[n[5]||(n[5]=u("div",{class:"group-number"},"如流用户群：11088845",-1)),u("div",Me,[n[1]||(n[1]=u("span",{class:"instruction-text"},"已加入用户：点击",-1)),t(r,{type:"primary",class:"jump-link",href:G,target:"_blank"},{default:e(()=>[t(_,{size:20},{default:e(()=>[t(m)]),_:1})]),_:1}),n[2]||(n[2]=u("span",{class:"instruction-text"},"直接跳转",-1))]),u("div",Se,[n[3]||(n[3]=u("span",{class:"instruction-text"},"新用户：点击",-1)),u("span",{class:"copy-link",onClick:n[0]||(n[0]=$(A=>a("11088845"),["stop"]))}," 复制群号 "),n[4]||(n[4]=u("span",{class:"instruction-text"},"后手动加入",-1))])])]),default:e(()=>[t(r,{class:"group-icon",type:"primary",href:G,target:"_blank"},{default:e(()=>[t(_,{size:20},{default:e(()=>[t(m)]),_:1})]),_:1})]),_:1})}}},we=k(be,[["__scopeId","data-v-33ada3b1"]]),ve={class:"notification-wrapper"},Fe={key:0,class:"unread-count"},Ue=U({__name:"IHeader",setup(s){const a=ye(),p=ee(),{unreadCount:n}=D(a),{imageUrl:m,username:_}=D(p),r=ne(),i=te(()=>r.currentRoute.value.matched.filter(c=>c.meta.title)),A=c=>{c?(a.fetchUnreadCount(),a.stopPolling()):a.startPolling()},I=()=>{r.push("/center/user").catch(c=>{B.warning("跳转失败，请稍后重试")})},l=()=>{a.fetchUnreadCount(),r.push("/team/group?id=3").catch(c=>{B.warning("跳转失败，请稍后重试")})};return oe(()=>{a.fetchUnreadCount(),a.startPolling()}),se(()=>{a.stopPolling()}),(c,f)=>{const Q=re,O=ce,K=le,P=y("Message"),J=F,q=de,S=pe,Z=ie,x=ue,N=y("Reading"),z=T,j=Y,X=me,W=ae;return o(),d(W,null,{default:e(()=>[t(K,{span:14},{default:e(()=>[t(O,{separator:"/",class:"breadcrumb"},{default:e(()=>[(o(!0),g(w,null,v(i.value,R=>(o(),d(Q,{key:R.path},{default:e(()=>[M(C(R.meta.title),1)]),_:2},1024))),128))]),_:1})]),_:1}),t(K,{span:10,class:"user"},{default:e(()=>[t(x,{trigger:"hover",placement:"bottom-end",onVisibleChange:A},{dropdown:e(()=>[t(Z,{class:"custom-dropdown"},{default:e(()=>[t(S,{onClick:l,class:"full-width-item"},{default:e(()=>[f[0]||(f[0]=u("span",{class:"item-content"},"消息处理列表",-1)),h(n)>0?(o(),g("span",Fe,"("+C(h(n))+"未读)",1)):E("",!0)]),_:1})]),_:1})]),default:e(()=>[u("span",ve,[t(q,{value:h(n),hidden:h(n)<=0,class:"item notification-badge",offset:"[30, 5]"},{default:e(()=>[t(J,null,{default:e(()=>[t(P,{class:"icon-color"})]),_:1})]),_:1},8,["value","hidden"])])]),_:1}),t(we),t(j,{content:"查看用户手册",placement:"bottom-end",effect:"light",class:"item",trigger:"hover"},{default:e(()=>[t(z,{style:{"margin-right":"10px"},type:"primary",href:"https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/5eKOIcLZMe/I9qN8S71fAChD1",target:"_blank"},{default:e(()=>[t(J,{size:20},{default:e(()=>[t(N)]),_:1})]),_:1})]),_:1}),t(x,{trigger:"hover",placement:"bottom-end"},{dropdown:e(()=>[t(Z,{class:"custom-dropdown"},{default:e(()=>[t(S,null,{default:e(()=>[M(C(h(_))+"（当前用户）",1)]),_:1}),t(S,{onClick:I,class:"full-width-item"},{default:e(()=>f[1]||(f[1]=[u("span",{class:"item-content"},"个人中心",-1)])),_:1})]),_:1})]),default:e(()=>[t(X,{class:"user-icon",icon:c.UserFilled,src:h(m),size:35},null,8,["icon","src"])]),_:1})]),_:1})]),_:1})}}}),H=k(Ue,[["__scopeId","data-v-bcda5f71"]]),Ke={data(){return{activeIndex:"/",routes:_e}},mounted(){this.activeIndex=localStorage.getItem("currentPath")||"/"},methods:{handleSelect(s){localStorage.setItem("currentPath",s)},isShow(s){return s.meta.showChildren?s.meta.hidden!==!0:!1}}},Je={class:"menu"};function Ze(s,a,p,n,m,_){const r=F,i=Ae,A=fe,I=ge;return o(),g("div",Je,[t(I,{"text-color":"#CFD3DC","background-color":"000c17",onSelect:_.handleSelect,router:"","default-active":m.activeIndex,"unique-opened":!1},{default:e(()=>[(o(!0),g(w,null,v(m.routes,l=>(o(),g("div",{key:l},[l.redirect?(o(),d(i,{key:0,index:l.path},{title:e(()=>[t(r,null,{default:e(()=>[(o(),d(b(l.meta.icon)))]),_:2},1024),u("span",null,C(l.meta.title),1)]),_:2},1032,["index"])):E("",!0),_.isShow(l)?(o(),d(A,{key:1,index:l.path,router:""},{title:e(()=>[t(r,null,{default:e(()=>[(o(),d(b(l.meta.icon)))]),_:2},1024),M(" "+C(l.meta.title),1)]),default:e(()=>[(o(!0),g(w,null,v(l.children,c=>(o(),g("div",{key:c},[c.meta.hidden?E("",!0):(o(),d(i,{key:0,index:l.path+"/"+c.path},{title:e(()=>[t(r,null,{default:e(()=>[(o(),d(b(c.meta.icon)))]),_:2},1024),u("span",null,C(c.meta.title),1)]),_:2},1032,["index"]))]))),128))]),_:2},1032,["index"])):E("",!0)]))),128))]),_:1},8,["onSelect","default-active"])])}const V=k(Ke,[["render",Ze],["__scopeId","data-v-2dbb32d9"]]),xe=U({data(){return{imagePath:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMgAAADICAYAAACtWK6eAAAAAXNSR0IArs4c6QAADbRJREFUeF7tnX92G7cOhSm/hcVZirfx+tLUL2234aVEXVisHkoaSxrPD4AEyEvy+p/2nJAz4AU+AuRwRofAPypABVYVOFAbKkAF1hUgIIwOKrChAAFheFABAsIYoAJpCjCDpOnGXoMoQEAGcTSHmaYAAUnTjb0GUYCADOJoDjNNAQKSpht7DaIAARnE0RxmmgIEJE03817/+/H2fPhP+B4vfDiFf/7/28sf5jfhBdUKEBC1ZLYdPsA4hef7Kx9CeCUktlqnXI2ApKhm1Of3v97+OIVL1lj7IyhGYidehoAkCpfTbS1rEJQcVX36EhAfXRevmgLGvOx6fw/HP7+9HAuaPfStCEgh93/7++1nmK0zUm/NsitVOX0/AqLXTNVDss5QXfCuMUFJVU7ej4DItVK1zC2nNDcjKBq1dG0JiE6v3dYlwZivT7gtvOsedQMCopZsvYNnOSU1k9lEqpSsHQGR6bTZCgGMuYEExcCx8VSDzWXGvEqtckqs9iEceWxFrNZiQwKSoB88GLMxMZskOPnahYAotUMsp6RDIChSpW7tCIhQs3PWeAo/hc2hmxEUuXsIyI5WrZVTUtdHSGJbbg1vK0ZAVvTpFQzudkmnkEs7ArKgV8vrDJ37H2ptvn+yIB4BuRNllKyxBRHXJ4/qEJAQAsH4FBSvPFbPEuusgOUx9NTyBrUfs8nAaxDIdcYhXF6EMnpvxAq8kUEZrsRCLafugxAS3suOznAL+WEAaQGM+YyPCMpokAwBCGKghUM4nn6FV8n75Yj2jwJK14BAHg9RgHGfURAhuT5I67rs6hKQFssp6YIaEpSOj9V3BQgqGLGc+vHfl69SCCTtEEHpsezqBhDEgNGsMyRQzNvECeHpKTzvfZ0x5do5fXoCpXlAILNG4jojNSghJ4dOtoWbBQQSjMpBgQhK68fqmwMEFQzvckqTXVBBafHdk6YAQXQ8EhiftoUP4QuPrWimls9tmwAENWuc3sNXyYO+PBfl9YacVCqXohpFoQFBBaPFXRpEUKKO6MfqYQFBdChqOaWZERF1RZ5w4ABBdGAPYPDYimYaubWFAYTlVJoDc3pBTkZg65PqgKCC4XE8JCeYPfsigoJSdlUFBNExvZVTUrB4bGVZqSqA9HQMXRqArbSDnLQqll1FAUEtp1DSORJEkKBUOFZfBBBUMEYtpzQgIoJSckJzBwRRYIKhQSSE6MPYY8Rj9W6AoGaNFo6H6MK3XGvIyc55fWIOCCoYJdNyuZCtcydEULyOrZgDAvelwsIvL9UJ2Tp3hQPF4dXmfgEhGEWogYKEgMh8znJKppNlKwhQCMi2SwmGZcinXasqKARkxWksp9Ki2bFXFVAIyMyjBMMxxPMvXRwSAvJwTr/rT17mhyfOFYqBQkDOT4XMv1KIE0p9W+IOikNstLPNy3KqC3pcj60MCcgAYHwETfxMz/VP+tMIrVLjkk1GA6T3bdvdIBlkcjA7BDkaID9+ezEvAVFm3F047g11cDyKDpMdKj3WjHfQyTwALc9i9QhITiD0fhI5R5szMwQEbd7T2ZMdANcg4Ppk/YGx9e+wMIPoYjyptQkYn56R9v+Ls2rdmEGS4rNqJ7WTNdZyEf+oFgHRRE/dtq5gMJssO5eA1A166d1LwvFh0wDZJI51U1sCIg3ROu2qgMFsclOAgNQJ/L27wr2HP2o2ISB7oVr+3xGyxvpzs/53uh7KLgJSHoC1O57BAPyJs0/2jpRNDuELn4MAMIKcNUbPJtbhwQeFCkVbBONheINkE4VLd5sSkF2JLg3c4TiE44Mpp/AsNE3drPdT0mpBNjoQkB01vcCIQRpvvffb4W47ZMwmIo4IyIZMHnDkzN4usDjs/Igir5FGBGTBUWhgzE30sK/3o/SpPBKQmXLmwec0QzObpIa8rh8BueplDobzZ/knN1u+oDZdk9nkBhEB8dqhcsocS/OfB9web+fp5m6M1uaAWG6Jer9y6xJYUYCCcHhmkstQxjiusoajCyBWkHgC4gZH/Kmy9/D1z28vj881CkyI3/56O7ncZuAtYTdAJkflBKIHIDn2SIKv5ozb89gk2nu0cQckJ5tYAuKy6zP3SIXSam6CWxaZbjRYNikCSGo2sQLEe2ZF2v0pNdaamdIjUxRfg6zdUOPAXEA097IQPddeCxviNdyzyEDZpGgGuQ8ASfDmBJzk+lYBeb4OQHmVmqlzdeg5m1QDRLI2SQWkOBwVd66WgrvG+HuFpCog0blbi+cUQGoERxxHiq25M/dW/2Jl1p0RPUJSHZCtskAbdGfYnsJPz8Bbu7bWVm8bawCCOFHk6gwDyFLJpQ26aoAArT+mgPA4oyUJtpQsEm21fpdcYqukDRQg82yiBaRWUCAt0JsE5HoCIAUuSZDntIEEZMome2/bzQdOQG6KtKTFfTmIBgksICnU11qgM4PcvJUS4EvrpZTrpMTMXh8CsqeQ8N+15aDwssnNai3SUw5qrtmKAElXgMRoqhUYBOTCcooOez6rCUp3gNQqs2o6cZ5mau3mpWQP6aRWS9/uAFnaLk6uUxQdazlwycQaC/Sc8e9lkPsxpkKocOVD0y4BiSMscrx9pnpKeZHquK1+moDLvr/B8XetvTkwasfbLSCTECVLrpKOW3N0yfLKarxaQM5jNwBTAkv3gJTOJrWzSInyygqMKUCTALl2trZlDs0QgJTMJt4O25r1SmRLj/HlAHJJJn4flhgKkFLZpEYW8S6tPIMwF5Bp0vCwcThAJjFdA6rC4UXP0soj8O4zoRUgHicahgXko/79++1ncPipAe+gegiwxsdAQCTbBRXbuG0JF8gkLuuOQzgeTuEf7WHRVBcSkFTlCvfzCrbTr/Dq8SE5j7KqZOaz2MWaPdU7Wr9XMnyJNWfQK5tYBp4HyJb2aec1ZhCtYgDtPYIw9+GWi03O26QSVxIQiUqAbbyyieZJcIQitj+F8N1aoppZg7tY1t6seD3XLeHbJn6xH/H0frimdRUziFYx0PYei+LSQ0XJGsNmkDjbeuzYlA6ktfu5ll2egyy8dasZylAZJM6yJffQNY6wbOu1YLa08Va9+Z1VsrB3OECmJ9OI6dzCodM10LNJK/oPC8i0GHx/D8eeyy7EbNIKHDFGhgaklTSfm1lQsklLYEyaE5C76GvRgRp4imwJrxjUqrYEZObQVh2pAaXklnDrehKQzmY8KSjuZVeh97Kl401tR0A2lIuzHxfx+tBqPWsM+6AwtbToyeFL4W6VTXrUiRlEMUH2GAD3w8/ZEu5VGwKiAGR6dlLqbTalaSbNtdmkVzC4zZsZTr0HhmRLuHcNcjLqp/ByeMXZ/I3C1DXIFku9B8lHRrmKEM+yxf9lFlXOsKMCMkLZpQyFZptry0vVQEcGZBKq92yiCojGGntUFw8SEJCbHASlHTpM1xnbtTj+V03cZ4mZQAQFFxTXcmpp2Mwgy8FASLAgKQ7Grf5mBhl5twsLg2VripVTzCDp4cCMkq5das+qYDCDJLgN+CMFCaOB7VKtnGIGsYkJZhMbHedXgQKjpQwShXt6Cs8eXwLMcTVByVHvsS9EOTUfjtO7MeZHTSa7IUUE+A6tXZiWv5Lk7Fhxq5zAuCUl5xEhgjLCS1qWboUspwpNdm4ZZO4gVFB6PhCYCwkqGLlfydfoUgyQaNQZkkP44vGTZ5pBfy5fsb88mDO21L6IE1pJMIqVWEsOghS/UMpODdhS/VCzxuk9fK3x8cGiGYRlV6kw198HFYzau49VAUHe8artGH2Ip/cofcBUZKnz7pTIhktVgfHHsqu8HyA1BwGj6hpkKxQgndbZ+oTllHwygskgXJ/InZbakmDolYMFJA6Fx1b0Dl3rAZmZwcqpJe2gAUFexEfbWljIj3g8xG5aAVqkSwaFOgsi/uQcyylJRO23aSKDcH2y78ipBSoYNZ6Cy1Vbb9kkIHE4MZvE//JY/c25qBn29Cu81ngKPjQgXJ/c3A+ZNRpYgEsAajaDtFJ2ef72CSQYjWxeSOC4bsRIm7bRDrHM8Njt4vGQMvHYTQa5l6vnY/WIE0CrC3AJYl0C0uP6BLWcqnUMXRLcFm26BgQZFGnZhQqG1H6LIK15jSEAaRUUllM10bjceyhApucnaM9Oro54nd6PJxj1wZgsGA4Q5GwSF7tn+07hGSdE2jhz5qXXsIBAg+Llbe11HX5OQGtC7fbDAxIdgHqsvlpwdPIU3EI/AnKnImTtb+Fl6TUIxielCMhC8IwIyijbttK5YvhFukSoIUBh1tgMBWaQHVJQj9VLAN/x/LHlY+jZ4xdegIAIheopm/R+PEToUlEzAiKS6daoZVC4zlA6e8Qn6XqJlns0BQrXGcluZwZJlg73a/UfQyIYGd69dCUg2RJeQQnhu8GlzC7BcspGSgJio+P5KghlF8EwdCgziK2Y09WqgMJyysWZzCAushbMJgTDyYNcg7gKWyKbsJzydyEziL/G9usTHkMv5DXuYhUTOt4o+1g9y6mi/uI2b3G5LzdUL+IJRiVPMYNUE14KCtcZVV3EB4V15d/IKMwaCK4hIBBemMquQ/gS7eExdBSvsMTC8QQtgVSA27yQbqFRKAoQEBRP0A5IBQgIpFtoFIoCBATFE7QDUgECAukWGoWiAAFB8QTtgFSAgEC6hUahKEBAUDxBOyAVICCQbqFRKAoQEBRP0A5IBf4FjsR+UJmw4lcAAAAASUVORK5CYII=",import.meta.url).href}},methods:{fullscreen:function(){this.$emit("set-fullscreen",!1)}}}),Re={class:"logo"};function De(s,a,p,n,m,_){const r=he,i=Ce;return o(),g("div",Re,[t(r,{class:"logo-img",src:s.imagePath,onClick:s.fullscreen},null,8,["src","onClick"]),t(i,{class:"logo-text"},{default:e(()=>a[0]||(a[0]=[M("QE-RAG")])),_:1})])}const L=k(xe,[["render",De],["__scopeId","data-v-2c2c170f"]]),Ge=U({name:"Layout",components:{IHeader:H,IMenu:V,ILogo:L},data(){return{fullscreen:!0}},mounted(){window.addEventListener("keydown",this.cancelFullscreen)},setup(){},methods:{setFullscreen(s){this.fullscreen=s},cancelFullscreen(s){(s.key==="Escape"||s.keyCode===27)&&(this.fullscreen=!0)}}}),Te={class:"common-layout"};function Ye(s,a,p,n,m,_){const r=L,i=V,A=Ie,I=H,l=ke,c=y("router-view"),f=Qe,Q=Ee;return o(),g("div",Te,[t(Q,null,{default:e(()=>[s.fullscreen?(o(),d(A,{key:0,class:"alide"},{default:e(()=>[t(r,{onSetFullscreen:s.setFullscreen},null,8,["onSetFullscreen"]),t(i)]),_:1})):E("",!0),t(Q,null,{default:e(()=>[s.fullscreen?(o(),d(l,{key:0,class:"header"},{default:e(()=>[t(I)]),_:1})):E("",!0),t(f,{class:"main"},{default:e(()=>[t(c)]),_:1})]),_:1})]),_:1})])}const je=k(Ge,[["render",Ye],["__scopeId","data-v-68344712"]]);export{je as default};
