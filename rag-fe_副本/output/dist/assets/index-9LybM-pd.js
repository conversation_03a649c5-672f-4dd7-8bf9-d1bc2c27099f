/* empty css                  *//* empty css                    */import{_ as X,i as re,$ as v,a0 as Y,k as ee,c as S,o as g,w as a,q as F,F as M,t as O,V as ne,d as T,y as N,U as se,h as H,u as ae,s as ke,a9 as _e,a as e,a1 as ue,a2 as ie,W as de,aq as le,x as C,B as q,Y as Z,g as G,Z as Ae,aL as Ve,a3 as me,T as Ce,a4 as pe,a5 as ce,a7 as ve,a6 as ge,G as J,e as Ue,az as ze,aM as Se,ad as xe,b as De,r as fe,ae as Ee,j as Te}from"./index-LoRUQG_-.js";/* empty css                   *//* empty css                      *//* empty css                  *//* empty css                     *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                *//* empty css                     */import{g as L}from"./GroupApi-C0GuTlOK.js";/* empty css                  *//* empty css                   */import{u as $e}from"./UserSelect.vue_vue_type_style_index_0_scoped_3500df00_lang-CqcJvsUu.js";import{u as Ie}from"./messageStore-B5ILOdNH.js";const Ge={style:{float:"right",color:"#8492a6","font-size":"13px"}},Ne={__name:"UserSelect",props:{modelValue:{type:[String,Array],default:()=>[]},mode:{type:String,default:"multiple",validator:V=>["multiple","single"].includes(V)},placeholder:{type:String,default:""}},emits:["update:modelValue","change"],setup(V,{expose:x,emit:U}){const u=V,s=U,f=re(()=>u.placeholder||(u.mode==="multiple"?"指定知识库管理员，输入用户名搜索":"请选择管理员")),y=v(u.mode==="multiple"?[]:""),i=v([]),o=v(!1),_=v(null);let p=null;const b=async l=>{try{return(await $e.getUser({name:l})).data||[]}catch(n){return console.error("搜索用户失败:",n),[]}},c=()=>{const l=u.mode==="multiple"?[...y.value]:y.value;s("update:modelValue",l),s("change",l)},D=async()=>{const l=u.mode==="multiple"?Array.isArray(u.modelValue)?u.modelValue:[]:u.modelValue?[u.modelValue]:[];if(l.length>0){o.value=!0;try{const n=await Promise.all(l.map(E=>b(E)));i.value=n.flat().filter(E=>E&&E.name)}catch(n){console.error("初始化用户数据失败:",n)}finally{o.value=!1}}},I=()=>{console.log("Select focused, current options:",{userOptions:i.value,selected:y.value})},d=async l=>{const n=l.trim();if(n===""){i.value=[];return}o.value=!0,clearTimeout(p),p=setTimeout(async()=>{try{const $=(await b(n)).map(r=>({name:String(r.name||""),departmentName:String(r.departmentName||"未知")})).filter(r=>r.name);i.value=$}catch{i.value=[]}finally{o.value=!1}},500)},m=l=>{l&&i.value.length===0&&y.value&&D()};return Y(()=>u.modelValue,l=>{u.mode==="multiple"?y.value=Array.isArray(l)?l:[]:y.value=typeof l=="string"?l:""},{immediate:!0}),ee(()=>{D()}),x({setUserOptions:l=>{i.value=Array.isArray(l)?l:[]}}),(l,n)=>{const E=ne,$=se;return g(),S($,{modelValue:y.value,"onUpdate:modelValue":n[0]||(n[0]=r=>y.value=r),multiple:V.mode==="multiple",filterable:"",remote:"","reserve-keyword":"",placeholder:f.value,"remote-method":d,loading:o.value,ref_key:"selectRef",ref:_,onChange:c,onVisibleChange:m,onFocus:I},{default:a(()=>[(g(!0),F(M,null,O(i.value,r=>(g(),S(E,{key:`${r.name}-${r.departmentName}`,label:r.name,value:r.name},{default:a(()=>[T("span",null,N(r.name),1),T("span",Ge,N(r.departmentName||"未知"),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","multiple","placeholder","loading"])}}},oe=X(Ne,[["__scopeId","data-v-3500df00"]]),be=H({__name:"GroupFormDialog",props:{visible:{type:Boolean},formData:{},isEdit:{type:Boolean}},emits:["update:visible","submit-success"],setup(V,{emit:x}){const U=ae(),{username:u}=ke(U),s=V,f=x,y=v(),i=v(!1),o=v({...s.formData});Y(()=>s.formData,l=>o.value={...l},{deep:!0}),Y(()=>s.visible,l=>{if(l){const n={...s.formData};(!n.adminArray||n.adminArray.length===0)&&(n.adminArray=[u.value]),o.value=n,Ae(()=>{o.value=n})}},{immediate:!0});const _={name:[{required:!0,message:"请输入名称",trigger:"blur"}],business:[{required:!0,message:"请输入事业群名称",trigger:"blur"}],managerArray:[{required:!0,message:"请填写经理，输入用户名搜索",trigger:"blur"}]},p=re(()=>s.isEdit?"编辑群组":"新建群组");function b(){f("update:visible",!1)}function c(l){o.value.managerArray=l}function D(l){o.value.memberArray=l}function I(l){o.value.adminArray=l}async function d(){var z;await((z=y.value)==null?void 0:z.validate()),i.value=!0;const{id:l,name:n,business:E,manager:$,createUser:r,managerArray:t,memberArray:h,adminArray:P}=o.value,B={name:n,business:E,manager:t,createUser:r},R={member:(h==null?void 0:h.join(";"))||"",admin:P.join(";")};try{let A;if(s.isEdit){const j={workGroup:{id:l,...B},...R};A=await L.editGroup(j)}else{const j={workGroup:{...B},...R};A=await L.createGroup(j)}(A==null?void 0:A.code)===200&&(G.success((A==null?void 0:A.message)||(s.isEdit?"更新成功":"创建成功")),f("submit-success"),b())}catch(A){console.error(A),G.error((A==null?void 0:A.message)||"提交失败")}finally{i.value=!1}}const m=v([]),w=v(!1);return ee(async()=>{try{const l=await L.getBuList();m.value=l.data||[]}catch(l){console.error("获取事业群列表失败",l)}}),(l,n)=>{const E=de,$=ie,r=le,t=ne,h=se,P=ue,B=Z,R=_e;return g(),S(R,{"model-value":l.visible,"onUpdate:modelValue":n[5]||(n[5]=z=>f("update:visible",z)),title:p.value,width:"50%"},{footer:a(()=>[e(B,{onClick:b},{default:a(()=>n[6]||(n[6]=[C("取消")])),_:1}),e(B,{type:"primary",onClick:d,loading:i.value},{default:a(()=>n[7]||(n[7]=[C("确认")])),_:1},8,["loading"])]),default:a(()=>[e(P,{model:o.value,"label-width":"100px",rules:_,ref_key:"formRef",ref:y},{default:a(()=>[e($,{label:"名称",prop:"name"},{default:a(()=>[e(E,{modelValue:o.value.name,"onUpdate:modelValue":n[0]||(n[0]=z=>o.value.name=z),placeholder:"请群组名称"},null,8,["modelValue"])]),_:1}),e($,{label:"创建者"},{default:a(()=>[e(r,null,{default:a(()=>[C(N(o.value.createUser),1)]),_:1})]),_:1}),e($,{label:"事业群",prop:"business"},{default:a(()=>[e(h,{modelValue:o.value.business,"onUpdate:modelValue":n[1]||(n[1]=z=>o.value.business=z),filterable:"",remote:"","reserve-keyword":"",placeholder:"请输入事业群名称",loading:w.value},{default:a(()=>[(g(!0),F(M,null,O(m.value,z=>(g(),S(t,{key:z,label:z,value:z},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1}),e($,{label:"经理"},{default:a(()=>[e(oe,{modelValue:o.value.managerArray,"onUpdate:modelValue":n[2]||(n[2]=z=>o.value.managerArray=z),mode:"single",placeholder:"请填写经理，输入用户名搜索","default-selected":o.value.managerArray,onChange:c},null,8,["modelValue","default-selected"])]),_:1}),e($,{label:"成员"},{default:a(()=>[e(oe,{modelValue:o.value.memberArray,"onUpdate:modelValue":n[3]||(n[3]=z=>o.value.memberArray=z),"default-selected":o.value.memberArray,placeholder:"请填写成员，输入用户名搜索",onChange:D},null,8,["modelValue","default-selected"])]),_:1}),e($,{label:"管理员"},{default:a(()=>[e(oe,{modelValue:o.value.adminArray,"onUpdate:modelValue":n[4]||(n[4]=z=>o.value.adminArray=z),"default-selected":o.value.adminArray||[q(u)],placeholder:"请填写管理员，输入用户名搜索",onChange:I},null,8,["modelValue","default-selected"])]),_:1})]),_:1},8,["model"])]),_:1},8,["model-value","title"])}}}),Pe=H({__name:"ApplyGroupDialog",props:{visible:Boolean,groupId:Number,groupName:String,admins:Array},emits:["update:visible","submit-success"],setup(V,{emit:x}){const U=V,u=x,s=v({workGroupId:U.groupId,role:0,auditor:"",reason:""}),f=v(!1),y=async()=>{try{f.value=!0,await L.submitApplication({workGroupId:U.groupId,...s.value}),G.success("申请提交成功"),u("update:visible",!1),u("submit-success")}catch{G.error("提交申请失败")}finally{f.value=!1}},i=_=>{console.log("子组件接收到弹窗状态更新:",_),u("update:visible",_)},o=()=>{u("update:visible",!1)};return Y(()=>U.visible,_=>{console.log("子组件 visible 状态:",_),_&&(s.value={workGroupId:U.groupId,role:1,auditor:U.admins[0]||"",reason:"申请加入"})}),(_,p)=>{const b=ne,c=se,D=ie,I=de,d=ue,m=Z,w=_e;return g(),S(w,{"model-value":V.visible,title:`申请加入 ${V.groupName}`,width:"500px","onUpdate:visible":i},{footer:a(()=>[e(m,{onClick:o},{default:a(()=>p[3]||(p[3]=[C("取消")])),_:1}),e(m,{type:"primary",onClick:y,loading:f.value},{default:a(()=>p[4]||(p[4]=[C("提交申请")])),_:1},8,["loading"])]),default:a(()=>[e(d,{model:s.value,"label-width":"100px"},{default:a(()=>[e(D,{label:"申请角色",prop:"role",required:""},{default:a(()=>[e(c,{modelValue:s.value.role,"onUpdate:modelValue":p[0]||(p[0]=l=>s.value.role=l),placeholder:"请选择角色"},{default:a(()=>[e(b,{label:"成员",value:0}),e(b,{label:"管理员",value:1})]),_:1},8,["modelValue"])]),_:1}),e(D,{label:"审批人",prop:"auditor",required:""},{default:a(()=>[e(c,{modelValue:s.value.auditor,"onUpdate:modelValue":p[1]||(p[1]=l=>s.value.auditor=l),placeholder:"请选择审批人",filterable:""},{default:a(()=>[(g(!0),F(M,null,O(V.admins,l=>(g(),S(b,{key:l,label:l,value:l},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(D,{label:"申请理由",prop:"reason"},{default:a(()=>[e(I,{modelValue:s.value.reason,"onUpdate:modelValue":p[2]||(p[2]=l=>s.value.reason=l),type:"textarea",rows:3,placeholder:"请输入申请理由"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["model-value","title"])}}});function W(V){return(V==null?void 0:V.split(";").filter(Boolean))||[]}function K(V){return Ve(V).format("YYYY-MM-DD HH:mm:ss")}const Be={class:"member-tags"},qe={class:"member-tags"},Fe={class:"pagination-container"},Me=H({__name:"AllGroupTable",setup(V){const x=ae(),U=v([]),u=v(!1),s=v(!1),f=v(!1),y=v(I()),i=v(!1),o=v(0),_=v(""),p=v([]),b=v({currentPage:1,size:10,total:0}),c=v({name:"",manager:"",business:"",page:1,size:10}),D=r=>{o.value=r.id,_.value=r.name,p.value=W(r.admin),i.value=!0};function I(){return{id:0,name:"",createUser:x.username||"",business:"",manager:"",member:"",admin:"",managerArray:[],memberArray:[],adminArray:[],createAt:"",updateAt:""}}re(()=>U.value.filter(r=>(c.value.business===""||r.business&&r.business.includes(c.value.business))&&(c.value.manager===""||r.manager&&r.manager.includes(c.value.manager))&&(c.value.createUser===""||r.createUser&&r.createUser.includes(c.value.createUser))));const d=()=>{G.success("筛选条件已应用"),console.log(c.value,178),l(c.value)},m=r=>{b.value.size=r,l()},w=r=>{b.value.currentPage=r,l()};ee(()=>{l()});async function l(r={}){u.value=!0;try{const t={...c.value,...r,page:b.value.currentPage,size:b.value.size},h=await L.getGroups(t);U.value=h.data.items||[],b.value.total=h.data.total||0}catch{G.error("获取数据失败")}finally{u.value=!1}}function n(r){var h;const t=x.username;return(h=r.admin)==null?void 0:h.split(";").includes(t)}function E(){f.value=!1,y.value=I(),s.value=!0}function $(r){var t,h;if(!n(r)){G.warning("您没有权限编辑该群组");return}f.value=!0,y.value={...r,managerArray:r.manager,memberArray:((t=r.member)==null?void 0:t.split(";").filter(Boolean))||[],adminArray:((h=r.admin)==null?void 0:h.split(";").filter(Boolean))||[]},s.value=!0}return(r,t)=>{const h=Z,P=de,B=ie,R=ue,z=Ce,A=ve,j=le,ye=ce,we=ge,he=pe;return g(),F(M,null,[e(h,{type:"primary",onClick:E,style:{margin:"16px"}},{default:a(()=>t[7]||(t[7]=[C("新增群组")])),_:1}),e(z,null,{default:a(()=>[e(R,{inline:!0,model:c.value,class:"filter-form"},{default:a(()=>[e(B,{label:"工作组名称"},{default:a(()=>[e(P,{modelValue:c.value.name,"onUpdate:modelValue":t[0]||(t[0]=k=>c.value.name=k),placeholder:"输入工作组名称",clearable:""},null,8,["modelValue"])]),_:1}),e(B,{label:"经理"},{default:a(()=>[e(P,{modelValue:c.value.manager,"onUpdate:modelValue":t[1]||(t[1]=k=>c.value.manager=k),placeholder:"输入经理名称",clearable:""},null,8,["modelValue"])]),_:1}),e(B,{label:"事业群名称"},{default:a(()=>[e(P,{modelValue:c.value.business,"onUpdate:modelValue":t[2]||(t[2]=k=>c.value.business=k),placeholder:"请输入事业群名称",clearable:""},null,8,["modelValue"])]),_:1}),e(B,null,{default:a(()=>[e(h,{type:"primary",onClick:d},{default:a(()=>t[8]||(t[8]=[C("查询")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),me((g(),S(ye,{data:U.value,style:{width:"100%"}},{default:a(()=>[e(A,{property:"name",label:"工作组名称","show-overflow-tooltip":""}),e(A,{property:"admin",label:"管理员",width:"200","show-overflow-tooltip":""},{default:a(k=>[T("div",Be,[(g(!0),F(M,null,O(q(W)(k.row.admin),(Q,te)=>(g(),S(j,{key:te,class:"member-tag",size:"small"},{default:a(()=>[C(N(Q),1)]),_:2},1024))),128))])]),_:1}),e(A,{property:"member",label:"成员",width:"200","show-overflow-tooltip":""},{default:a(k=>[T("div",qe,[(g(!0),F(M,null,O(q(W)(k.row.member),(Q,te)=>(g(),S(j,{key:te,class:"member-tag",size:"small"},{default:a(()=>[C(N(Q),1)]),_:2},1024))),128))])]),_:1}),e(A,{property:"createUser",label:"创建者","show-overflow-tooltip":""}),e(A,{property:"business",label:"事业群","show-overflow-tooltip":""}),e(A,{property:"manager",label:"经理","show-overflow-tooltip":""}),e(A,{property:"createAt",label:"创建时间","show-overflow-tooltip":""},{default:a(k=>[C(N(q(K)(k.row.createAt)),1)]),_:1}),e(A,{property:"updateAt",label:"更新时间","show-overflow-tooltip":""},{default:a(k=>[C(N(q(K)(k.row.updateAt)),1)]),_:1}),e(A,{label:"操作",fixed:"right",width:"100"},{default:a(k=>[n(k.row)?(g(),S(h,{key:0,size:"small",type:"primary",onClick:Q=>$(k.row)},{default:a(()=>t[9]||(t[9]=[C(" 编辑 ")])),_:2},1032,["onClick"])):(g(),S(h,{key:1,size:"small",type:"success",onClick:Q=>D(k.row)},{default:a(()=>t[10]||(t[10]=[C(" 申请 ")])),_:2},1032,["onClick"]))]),_:1})]),_:1},8,["data"])),[[he,u.value]]),T("div",Fe,[e(we,{"current-page":b.value.currentPage,"onUpdate:currentPage":t[3]||(t[3]=k=>b.value.currentPage=k),"page-size":b.value.size,"onUpdate:pageSize":t[4]||(t[4]=k=>b.value.size=k),total:b.value.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:m,onCurrentChange:w},null,8,["current-page","page-size","total"])]),e(be,{visible:s.value,"onUpdate:visible":t[5]||(t[5]=k=>s.value=k),formData:y.value,isEdit:f.value,onSubmitSuccess:l},null,8,["visible","formData","isEdit"]),e(Pe,{visible:i.value,groupId:o.value,"group-name":_.value,admins:p.value,"onUpdate:visible":t[6]||(t[6]=k=>i.value=k),onSubmitSuccess:l},null,8,["visible","groupId","group-name","admins"])],64)}}}),Le=X(Me,[["__scopeId","data-v-512c2d11"]]),Oe={class:"member-tags"},Re={class:"member-tags"},je={class:"token-display"},Ye={class:"token-text"},He={class:"pagination-container"},Qe=H({__name:"groupTable",setup(V){const x=ae(),U=v([]),u=v(!1),s=v(!1),f=v(!1),y=v(o()),i=v({currentPage:1,pageSize:10,total:0});function o(){return{id:0,name:"",createUser:x.username||"",business:"",manager:"",member:"",admin:"",managerArray:[],memberArray:[],adminArray:[],createAt:"",updateAt:""}}async function _(){u.value=!0;try{const d={page:i.value.currentPage,pageSize:i.value.pageSize},m=await L.getGroup(d);U.value=m.data.items||[],i.value.total=m.data.total||0}catch{G.error("获取数据失败")}finally{u.value=!1}}const p=d=>{i.value.pageSize=d,i.value.currentPage=1,_()},b=d=>{i.value.currentPage=d,_()};function c(d){var w;const m=x.username;return(w=d.admin)==null?void 0:w.split(";").includes(m)}function D(d){var m,w;if(!c(d)){G.warning("您没有权限编辑该群组");return}f.value=!0,y.value={...d,managerArray:d.manager,memberArray:((m=d.member)==null?void 0:m.split(";").filter(Boolean))||[],adminArray:((w=d.admin)==null?void 0:w.split(";").filter(Boolean))||[]},s.value=!0}const I=d=>{navigator.clipboard.writeText(d).then(()=>{G.success("Token已复制到剪贴板")}).catch(m=>{console.error("复制失败:",m),G.error("复制失败，请手动复制")})};return _(),(d,m)=>{const w=ve,l=le,n=Z,E=ce,$=ge,r=pe;return g(),F(M,null,[me((g(),S(E,{data:U.value,style:{width:"100%"}},{default:a(()=>[e(w,{property:"id",label:"工作组ID","show-overflow-tooltip":""}),e(w,{property:"name",label:"工作组名称","show-overflow-tooltip":""}),e(w,{property:"admin",label:"管理员",width:"200","show-overflow-tooltip":""},{default:a(t=>[T("div",Oe,[(g(!0),F(M,null,O(q(W)(t.row.admin),(h,P)=>(g(),S(l,{key:P,class:"member-tag",size:"small"},{default:a(()=>[C(N(h),1)]),_:2},1024))),128))])]),_:1}),e(w,{property:"member",label:"成员",width:"200","show-overflow-tooltip":""},{default:a(t=>[T("div",Re,[(g(!0),F(M,null,O(q(W)(t.row.member),(h,P)=>(g(),S(l,{key:P,class:"member-tag",size:"small"},{default:a(()=>[C(N(h),1)]),_:2},1024))),128))])]),_:1}),e(w,{property:"createUser",label:"创建者","show-overflow-tooltip":""}),e(w,{property:"business",label:"事业群","show-overflow-tooltip":""}),e(w,{property:"manager",label:"经理","show-overflow-tooltip":""}),e(w,{property:"token",label:"Token",width:"100","show-overflow-tooltip":""},{default:a(t=>[T("div",je,[T("span",Ye,N(t.row.token?t.row.token.slice(0,8)+"...":""),1),t.row.token?(g(),S(n,{key:0,size:"small",type:"text",onClick:Ue(h=>I(t.row.token),["stop"]),icon:q(ze),title:"复制Token",class:"copy-btn"},null,8,["onClick","icon"])):J("",!0)])]),_:1}),e(w,{property:"createAt",label:"创建时间","show-overflow-tooltip":""},{default:a(t=>[C(N(q(K)(t.row.createAt)),1)]),_:1}),e(w,{property:"updateAt",label:"更新时间","show-overflow-tooltip":""},{default:a(t=>[C(N(q(K)(t.row.updateAt)),1)]),_:1}),e(w,{label:"操作",fixed:"right",width:"100"},{default:a(t=>[e(n,{size:"small",type:"primary",onClick:h=>D(t.row),disabled:!c(t.row)},{default:a(()=>m[3]||(m[3]=[C(" 编辑 ")])),_:2},1032,["onClick","disabled"])]),_:1})]),_:1},8,["data"])),[[r,u.value]]),T("div",He,[e($,{"current-page":i.value.currentPage,"onUpdate:currentPage":m[0]||(m[0]=t=>i.value.currentPage=t),"page-size":i.value.pageSize,"onUpdate:pageSize":m[1]||(m[1]=t=>i.value.pageSize=t),total:i.value.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",background:"",onSizeChange:p,onCurrentChange:b},null,8,["current-page","page-size","total"])]),e(be,{visible:s.value,"onUpdate:visible":m[2]||(m[2]=t=>s.value=t),formData:y.value,isEdit:f.value,onSubmitSuccess:_},null,8,["visible","formData","isEdit"])],64)}}}),We=X(Qe,[["__scopeId","data-v-93efa914"]]),Ze=H({__name:"tableList",setup(V){const x=Ie(),U=ae(),{username:u}=U,s=v([]),f=v(!1);async function y(){f.value=!0;try{const o=await L.checkList();s.value=o.data}catch{G.error("获取数据失败")}finally{f.value=!1}}async function i(o,_){try{const{id:p,workGroupId:b,role:c,userName:D,auditor:I,reason:d}=o,m={id:p,workGroupId:b,role:c,userName:D,auditor:I,reason:d};(_===0?await L.applyYes(m):await L.applyNo(m)).code===200&&(G.success("提交成功"),await x.fetchUnreadCount()),y()}catch{G.error("提交失败")}}return y(),(o,_)=>{const p=ve,b=le,c=Z,D=ce,I=pe;return me((g(),S(D,{data:s.value,style:{width:"100%"}},{default:a(()=>[e(p,{property:"workGroupId",label:"组ID","show-overflow-tooltip":""}),e(p,{property:"workGroupName",label:"组名称","show-overflow-tooltip":""}),e(p,{property:"role",label:"申请角色","show-overflow-tooltip":""},{default:a(d=>[e(b,{size:"small",type:+d.row.role==1?"primary":"success"},{default:a(()=>[C(N(+d.row.role==1?"管理员":"成员"),1)]),_:2},1032,["type"])]),_:1}),e(p,{property:"reason",label:"申请原因","show-overflow-tooltip":""}),e(p,{property:"userName",label:"申请人","show-overflow-tooltip":""}),e(p,{label:"操作",fixed:"right",width:"200"},{default:a(d=>[e(c,{size:"small",type:"primary",onClick:m=>i(d.row,0)},{default:a(()=>_[0]||(_[0]=[C("同意")])),_:2},1032,["onClick"]),e(c,{size:"small",type:"dangour",onClick:m=>i(d.row,1)},{default:a(()=>_[1]||(_[1]=[C("拒绝")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[I,f.value]])}}}),Je={class:"tab-label"},Ke={class:"tab-label"},Xe={class:"tab-label"},ea=H({__name:"index",setup(V){const x=Se(),U=Te(),u=v("1");return ee(()=>{x.query.id&&(u.value=x.query.id.toString(),console.log("初始化activeName:",u.value))}),Y(u,s=>{console.log("标签页变化:",s),U.replace({path:x.path,query:{...x.query,id:s}})}),Y(()=>x.query.id,s=>{s&&s!==u.value&&(console.log("路由参数变化:",s),u.value=s.toString())},{immediate:!0}),(s,f)=>{const y=fe("User"),i=De,o=Ee,_=fe("Coin"),p=xe;return g(),S(p,{modelValue:u.value,"onUpdate:modelValue":f[0]||(f[0]=b=>u.value=b),class:"group-tabs"},{default:a(()=>[e(o,{name:"1"},{label:a(()=>[T("div",Je,[e(i,{class:"tab-icon"},{default:a(()=>[e(y)]),_:1}),f[1]||(f[1]=T("span",{class:"tab-text"},"我加入的",-1))])]),default:a(()=>[u.value==="1"?(g(),S(We,{key:0})):J("",!0)]),_:1}),e(o,{name:"2"},{label:a(()=>[T("div",Ke,[e(i,{class:"tab-icon"},{default:a(()=>[e(_)]),_:1}),f[2]||(f[2]=T("span",{class:"tab-text"},"全部团队",-1))])]),default:a(()=>[u.value==="2"?(g(),S(Le,{key:0})):J("",!0)]),_:1}),e(o,{name:"3"},{label:a(()=>[T("div",Xe,[e(i,{class:"tab-icon"},{default:a(()=>[e(_)]),_:1}),f[3]||(f[3]=T("span",{class:"tab-text"},"待审批列表",-1))])]),default:a(()=>[u.value==="3"?(g(),S(Ze,{key:0})):J("",!0)]),_:1})]),_:1},8,["modelValue"])}}}),ga=X(ea,[["__scopeId","data-v-ae1b5805"]]);export{ga as default};
