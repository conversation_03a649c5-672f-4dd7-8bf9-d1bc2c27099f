import{aN as s,$ as l}from"./index-LoRUQG_-.js";import{g as u}from"./GroupApi-C0GuTlOK.js";const g=s("message",()=>{const t=l(0);let r=null;const n=async()=>{try{const e=await u.getUnreadMessages();return t.value=(e==null?void 0:e.code)===200?e.data:0,t.value}catch(e){return console.error("获取未读消息数失败",e),t.value=0,0}},a=(e=3e5)=>{o(),n(),r=window.setInterval(n,e)},o=()=>{r&&(clearInterval(r),r=null)};return{unreadCount:t,fetchUnreadCount:n,startPolling:a,stopPolling:o,resetUnreadCount:()=>{t.value=0}}});export{g as u};
