/* empty css                  *//* empty css                    *//* empty css                     *//* empty css                  *//* empty css                     */import{g as S}from"./GroupApi-C0GuTlOK.js";import{h as E,$ as g,i as R,a0 as D,k as z,q as C,a as e,a1 as T,w as s,g as $,o as V,a2 as B,W as G,U as J,F as j,t as O,Y as L,x as I,c as q,V as Q,_ as A,a3 as Y,G as M,a4 as H,a5 as K,a6 as X,a7 as Z,y as N,a8 as x,a9 as ee,ac as W,ad as ae,ae as le}from"./index-LoRUQG_-.js";/* empty css                   *//* empty css                      *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                   *//* empty css                  *//* empty css                   */import{m as U}from"./McpServersApi-C6_QmHgg.js";const oe={class:"query-container"},te=E({__name:"McpServerQueryForm",props:{query:{},tabActive:{}},emits:["search","create"],setup(P){const b=P,m=g([]),f=g(!1),_=g([]),p=g(!1),h=R(()=>b.tabActive==="all"?_.value:m.value);D(()=>b.tabActive,l=>{l==="all"&&o()},{immediate:!0});const k=async()=>{f.value=!0;try{const l=await S.getJoinedList();m.value=(l==null?void 0:l.data)||[]}catch(l){console.error("获取工作组列表错误:",l),$.error("获取工作组列表失败")}finally{f.value=!1}},o=async()=>{if(b.tabActive==="all"){p.value=!0;try{const l=await S.getAllGroupsList();l!=null&&l.data&&(_.value=l.data)}catch(l){console.error("获取所有工作组列表错误:",l),$.error("获取所有工作组列表失败")}finally{p.value=!1}}};return z(()=>{k(),b.tabActive==="all"&&o()}),(l,t)=>{const c=G,a=B,n=Q,y=J,v=L,i=T;return V(),C("div",oe,[e(i,{class:"query-form",inline:""},{default:s(()=>[e(a,{label:"server名称:",class:"form-item-wide"},{default:s(()=>[e(c,{modelValue:l.query.name,"onUpdate:modelValue":t[0]||(t[0]=r=>l.query.name=r),placeholder:"根据server名称模糊查询",clearable:"",class:"wide-input"},null,8,["modelValue"])]),_:1}),e(a,{label:"工作组:",class:"form-item-wide"},{default:s(()=>[e(y,{modelValue:l.query.groupId,"onUpdate:modelValue":t[1]||(t[1]=r=>l.query.groupId=r),placeholder:"请选择工作组",clearable:"","loading-text":"加载中...",loading:l.tabActive==="all"?p.value:f.value,class:"wide-input"},{default:s(()=>[(V(!0),C(j,null,O(h.value,r=>(V(),q(n,{key:r.id,label:`${r.id} - ${r.name} (${r.business})`,value:r.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1}),e(a,null,{default:s(()=>[e(v,{type:"primary",onClick:t[2]||(t[2]=r=>l.$emit("search"))},{default:s(()=>t[4]||(t[4]=[I("查询")])),_:1})]),_:1}),e(a,{class:"create-btn"},{default:s(()=>[e(v,{type:"primary",onClick:t[3]||(t[3]=r=>l.$emit("create"))},{default:s(()=>t[5]||(t[5]=[I("新建")])),_:1})]),_:1})]),_:1})])}}}),re=A(te,[["__scopeId","data-v-2adc62a9"]]),ne={class:"table-container"},se=E({__name:"McpServerTable",props:{tableData:{},loading:{type:Boolean},paging:{}},emits:["edit","delete","update:page","update:size"],setup(P){const b=g([]),m=g(!1),f=g([]),_=async()=>{m.value=!0;try{const o=await S.getAllGroupsList();o!=null&&o.data&&(b.value=o.data)}catch(o){console.error("获取所有工作组列表错误:",o),$.error("获取所有工作组列表失败")}finally{m.value=!1}},p=async()=>{try{const o=await S.getJoinedList();o!=null&&o.data&&(f.value=o.data)}catch(o){console.error("获取已加入工作组列表错误:",o)}};z(()=>{_(),p()});const h=o=>{if(!o)return!1;const l=Number(o);return f.value.some(t=>t.id===l)},k=o=>{if(!o)return"-";const l=Number(o),t=b.value.find(c=>c.id===l);return t?`${t.name} (${t.business})`:`${o}`};return(o,l)=>{const t=Z,c=L,a=x,n=K,y=X,v=H;return V(),C("div",ne,[Y((V(),q(n,{data:o.tableData,stripe:"","table-layout":"auto",style:{width:"100%"}},{default:s(()=>[e(t,{prop:"id",label:"ID",width:"80",align:"center"}),e(t,{prop:"name",label:"server名称",width:"100","show-overflow-tooltip":""}),e(t,{label:"工作组",width:"120",align:"center","show-overflow-tooltip":""},{default:s(({row:i})=>[I(N(k(i.groupId)),1)]),_:1}),e(t,{prop:"type",label:"服务类型",width:"80",align:"center"},{default:s(({row:i})=>[I(N(i.type===0?"api":"local"),1)]),_:1}),e(t,{prop:"description",label:"功能描述",width:"150","show-overflow-tooltip":""}),e(t,{prop:"command",label:"执行命令",width:"100","show-overflow-tooltip":""}),e(t,{prop:"url",label:"接口地址",width:"100","show-overflow-tooltip":""}),e(t,{prop:"env",label:"配置信息",width:"100",align:"center","show-overflow-tooltip":""}),e(t,{prop:"queryPath",label:"查询路径","show-overflow-tooltip":""}),e(t,{prop:"executePath",label:"执行路径",width:"100","show-overflow-tooltip":""}),e(t,{prop:"createAt",label:"创建时间",width:"180",align:"center"}),e(t,{prop:"updateAt",label:"更新时间",width:"180",align:"center"}),e(t,{label:"操作",width:"160",align:"center",fixed:"right"},{default:s(({row:i})=>[h(i.groupId)?(V(),q(c,{key:0,type:"primary",size:"small",onClick:r=>o.$emit("edit",i),link:""},{default:s(()=>l[2]||(l[2]=[I("修改")])),_:2},1032,["onClick"])):M("",!0),h(i.groupId)?(V(),q(a,{key:1,title:"确认要删除该条记录吗?",onConfirm:r=>o.$emit("delete",i)},{reference:s(()=>[e(c,{type:"danger",size:"small",link:""},{default:s(()=>l[3]||(l[3]=[I("删除")])),_:1})]),_:2},1032,["onConfirm"])):M("",!0)]),_:1})]),_:1},8,["data"])),[[v,o.loading]]),o.paging.total>0?(V(),q(y,{key:0,"current-page":o.paging.page,"page-size":o.paging.size,total:o.paging.total,background:!0,layout:"prev, pager, next, jumper, total","onUpdate:currentPage":l[0]||(l[0]=i=>o.$emit("update:page",i)),"onUpdate:pageSize":l[1]||(l[1]=i=>o.$emit("update:size",i))},null,8,["current-page","page-size","total"])):M("",!0)])}}}),ue=A(se,[["__scopeId","data-v-f23d4b16"]]),de=E({__name:"McpServerForm",props:{modelValue:{type:Boolean},title:{},server:{}},emits:["update:modelValue","save"],setup(P,{emit:b}){const m=P,f=b,_=g(),p=g({...m.server,type:Number(m.server.type),groupId:Number(m.server.groupId)});D(()=>m.server,a=>{p.value={...a,type:Number(a.type),groupId:Number(a.groupId)}},{deep:!0});const h=g([]),k=g(!1),o=async()=>{k.value=!0;try{const a=await S.getJoinedList();h.value=(a==null?void 0:a.data)||[]}catch(a){console.error("获取工作组列表错误:",a),$.error("获取工作组列表失败")}finally{k.value=!1}};z(()=>{o()});const l={name:[{required:!0,max:50,message:"请输入服务器名称（不超过50个字）",trigger:"blur"}],type:[{required:!0,type:"number",message:"请选择服务器类型",trigger:"change"}],command:[],url:[],env:[{required:!0,message:"请输入环境",trigger:"blur"}],queryPath:[{required:!0,message:"请输入查询路径",trigger:"blur"}],executePath:[{required:!0,message:"请输入执行路径",trigger:"blur"}],groupId:[{required:!0,type:"number",message:"请选择工作组",trigger:"change"}]},t=async()=>{try{await _.value.validate();const a={...p.value,type:Number(p.value.type),groupId:Number(p.value.groupId)};f("save",a),f("update:modelValue",!1)}catch{$.error("请检查表单填写是否正确")}},c=()=>{var a;(a=_.value)==null||a.resetFields(),f("update:modelValue",!1)};return(a,n)=>{const y=G,v=B,i=Q,r=J,d=T,w=L,F=ee;return V(),q(F,{"model-value":a.modelValue,title:a.title,width:"600",onClose:n[9]||(n[9]=u=>a.$emit("update:modelValue",!1)),"onUpdate:modelValue":n[10]||(n[10]=u=>a.$emit("update:modelValue",u))},{footer:s(()=>[e(w,{onClick:c},{default:s(()=>n[11]||(n[11]=[I("取消")])),_:1}),e(w,{type:"primary",onClick:t},{default:s(()=>n[12]||(n[12]=[I("保存")])),_:1})]),default:s(()=>[e(d,{ref_key:"formRef",ref:_,model:a.server,rules:l,"label-width":"140px"},{default:s(()=>[e(v,{label:"server名称:",prop:"name"},{default:s(()=>[e(y,{modelValue:a.server.name,"onUpdate:modelValue":n[0]||(n[0]=u=>a.server.name=u),placeholder:"请输入server名称",clearable:""},null,8,["modelValue"])]),_:1}),e(v,{label:"服务器类型:",prop:"type"},{default:s(()=>[e(r,{modelValue:a.server.type,"onUpdate:modelValue":n[1]||(n[1]=u=>a.server.type=u),modelModifiers:{number:!0},placeholder:"请选择服务器类型",clearable:""},{default:s(()=>[e(i,{label:"api",value:0}),e(i,{label:"local",value:1})]),_:1},8,["modelValue"])]),_:1}),e(v,{label:"功能描述:",prop:"description"},{default:s(()=>[e(y,{modelValue:a.server.description,"onUpdate:modelValue":n[2]||(n[2]=u=>a.server.description=u),placeholder:"请输入功能描述",type:"textarea",rows:2},null,8,["modelValue"])]),_:1}),e(v,{label:"命令:",prop:"command"},{default:s(()=>[e(y,{modelValue:a.server.command,"onUpdate:modelValue":n[3]||(n[3]=u=>a.server.command=u),placeholder:"请输入命令",clearable:""},null,8,["modelValue"])]),_:1}),e(v,{label:"接口地址:",prop:"url"},{default:s(()=>[e(y,{modelValue:a.server.url,"onUpdate:modelValue":n[4]||(n[4]=u=>a.server.url=u),placeholder:"请输入接口地址",clearable:""},null,8,["modelValue"])]),_:1}),e(v,{label:"配置信息:",prop:"env"},{default:s(()=>[e(y,{modelValue:a.server.env,"onUpdate:modelValue":n[5]||(n[5]=u=>a.server.env=u),placeholder:"请输入配置信息",clearable:""},null,8,["modelValue"])]),_:1}),e(v,{label:"查询路径:",prop:"queryPath"},{default:s(()=>[e(y,{modelValue:a.server.queryPath,"onUpdate:modelValue":n[6]||(n[6]=u=>a.server.queryPath=u),placeholder:"请输入查询路径",clearable:""},null,8,["modelValue"])]),_:1}),e(v,{label:"执行路径:",prop:"executePath"},{default:s(()=>[e(y,{modelValue:a.server.executePath,"onUpdate:modelValue":n[7]||(n[7]=u=>a.server.executePath=u),placeholder:"请输入执行路径",clearable:""},null,8,["modelValue"])]),_:1}),e(v,{label:"工作组:",prop:"groupId"},{default:s(()=>[e(r,{modelValue:a.server.groupId,"onUpdate:modelValue":n[8]||(n[8]=u=>a.server.groupId=u),modelModifiers:{number:!0},placeholder:"请选择工作组",clearable:"","loading-text":"加载中...",loading:k.value},{default:s(()=>[(V(!0),C(j,null,O(h.value,u=>(V(),q(i,{key:u.id,label:`${u.id} - ${u.name} (${u.business})`,value:u.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["model-value","title"])}}}),ie=A(de,[["__scopeId","data-v-998a6832"]]),pe={class:"app-workspace"},me=E({__name:"index",setup(P){const b=g(!1),m=g(!1),f=g(""),_=g("mine"),p=W({name:"",groupId:"",page:"1",size:"10",type:1}),h=W({page:1,size:10,total:0,items:[]}),k=g({id:"",name:"",type:"",command:"",url:"",env:"",queryPath:"",executePath:"",groupId:"",description:""});z(()=>{c()});const o=r=>{p.type=r.props.name==="all"?0:1,p.page="1",c()},l=r=>{p.page=r.toString(),c()},t=r=>{p.size=r.toString(),p.page="1",c()},c=async(r=p)=>{b.value=!0;try{const d=await U.getServers(r);(d==null?void 0:d.code)===200&&Object.assign(h,d.data)}catch(d){console.error("Failed to fetch servers:",d)}finally{b.value=!1}},a=()=>{p.page="1",c()},n=()=>{f.value="新建服务器",k.value={id:"",name:"",type:"",command:"",url:"",env:"",queryPath:"",executePath:"",groupId:"",description:""},m.value=!0},y=r=>{f.value="编辑服务器",k.value={...r},m.value=!0},v=async r=>{try{const w=await(r.id?U.edit:U.create)(r);(w==null?void 0:w.code)===200&&(c(),m.value=!1,$.success((w==null?void 0:w.message)||"操作成功"))}catch(d){console.error("Failed to save server:",d),$.error("操作失败，请检查数据")}},i=async r=>{try{const d=await U.remove({id:r.id,groupId:r.groupId});(d==null?void 0:d.code)===200&&(c(),$.success("删除成功"))}catch(d){console.error("Failed to delete server:",d),$.error("删除失败")}};return(r,d)=>{const w=le,F=ae;return V(),C("div",pe,[e(F,{modelValue:_.value,"onUpdate:modelValue":d[0]||(d[0]=u=>_.value=u),class:"server-tabs",onTabClick:o},{default:s(()=>[e(w,{label:"我的",name:"mine",lazy:!0}),e(w,{label:"全部",name:"all",lazy:!0})]),_:1},8,["modelValue"]),e(re,{query:p,"tab-active":_.value,onSearch:a,onCreate:n},null,8,["query","tab-active"]),e(ue,{"table-data":h.items,loading:b.value,paging:h,onEdit:y,onDelete:i,"onUpdate:page":l,"onUpdate:size":t},null,8,["table-data","loading","paging"]),e(ie,{modelValue:m.value,"onUpdate:modelValue":d[1]||(d[1]=u=>m.value=u),title:f.value,server:k.value,onSave:v},null,8,["modelValue","title","server"])])}}}),Se=A(me,[["__scopeId","data-v-f14996f2"]]);export{Se as default};
