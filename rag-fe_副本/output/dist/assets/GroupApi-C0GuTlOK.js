import{al as p}from"./index-LoRUQG_-.js";const e="/rag/api";var o=(t=>(t.QUERY_ALL_GROUP=e+"/workgroup/all/query",t.QUERY_GROUP_JOIN=e+"/workgroup/join/query",t.UPDATE_GROUP_INFO=e+"/workgroup/update",t.UPDATE_GROUP_INFO_USER=e+"/workgroup/update",t.DETAIL_GROUP=e+"/workgroup/query",t.ADD_GROUP=e+"/workgroup/add",t.JOIN_GROUP=e+"/workgroup/apply/join",t.LIST_GROUP=e+"/audit/message/query",t.APPROVE_GROUP=e+"/workgroup/apply/approve",t.REJECTY_GROUP=e+"/workgroup/apply/reject",t.MEASSAGE_TIPS=e+"/audit/message/num/query",t.BU_LIST=e+"/bu/query",t.QUERY_JOINED_LIST=e+"/workgroup/query/joined/list",t.QUERY_ALL_GROUP_LIST=e+"/workgroup/query/all/list",t))(o||{});class u{getGroup(r={}){return p.post(o.QUERY_GROUP_JOIN,r)}getGroups(r={}){return p.post(o.QUERY_ALL_GROUP,r)}editGroupUser(r){return p.put(o.UPDATE_GROUP_INFO,r)}editGroup(r){return p.post(o.UPDATE_GROUP_INFO,r)}createGroup(r){return p.post(o.ADD_GROUP,r)}submitApplication(r){return p.post(o.JOIN_GROUP,r)}checkList(r){return p.get(o.LIST_GROUP,r)}applyYes(r){return p.post(o.APPROVE_GROUP,r)}applyNo(r){return p.post(o.REJECTY_GROUP,r)}getUnreadMessages(){return p.get(o.MEASSAGE_TIPS)}getBuList(){return p.get(o.BU_LIST)}getJoinedList(){return p.post(o.QUERY_JOINED_LIST)}getAllGroupsList(){return p.post(o.QUERY_ALL_GROUP_LIST)}}const U=new u;export{U as g};
