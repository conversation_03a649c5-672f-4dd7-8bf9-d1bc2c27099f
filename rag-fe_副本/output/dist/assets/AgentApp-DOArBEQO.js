/* empty css                  *//* empty css                *//* empty css                     *//* empty css                     *//* empty css                  *//* empty css                  *//* empty css                   *//* empty css               */import{_ as Xe,c as _n,o as ne,w as S,a as m,m as Wn,n as Gn,d as D,G as ot,Y as ut,x as be,g as $e,q as de,aj as Gh,r as he,b as to,W as Rr,aq as Gs,U as Vs,F as Dr,t as Lr,T as st,V as $s,ar as Pr,$ as tn,a0 as Dn,as as Or,at as Vh,au as $h,av as qh,aw as Kh,ax as Hh,i as eo,ay as Er,a9 as qs,Z as Ks,az as kh,aA as zh,a5 as ro,a7 as io,aB as Zh,aC as Yh,y as Pn,f as Xh,h as Hs,aD as Jh,an as ks,X as Qh,ap as jh,am as e_,a1 as n_,a2 as t_,aE as r_,aF as i_}from"./index-LoRUQG_-.js";import{a as no}from"./AgentApi-CkSBsfcP.js";/* empty css                    */import{g as Ns}from"./GroupApi-C0GuTlOK.js";/* empty css                        */import"./el-tooltip-l0sNRNKZ.js";import{_ as o_,C as u_,a as s_}from"./play-BrM_F_ME.js";import{r as a_}from"./renderMarkdown-C0qkyt2C.js";import{g as l_,m as f_}from"./ModelApi-CqIWtrMp.js";import{m as c_}from"./McpServersApi-C6_QmHgg.js";/* empty css                  */const d_={props:{isLoadingFaBu:Boolean,isLoadingUpdate:Boolean},methods:{saveAgent(){this.$emit("save")},updateAgent(){this.$emit("update")}}};function p_(f,c,s,x,y,I){const P=Gn,U=ut,$=Wn;return ne(),_n($,{class:"upper-row"},{default:S(()=>[m(P,{span:24},{default:S(()=>[m($,{class:"header-content"},{default:S(()=>[m(P,{span:3,class:"header-title"},{default:S(()=>c[0]||(c[0]=[D("span",null,"我的Agent应用",-1)])),_:1}),m(P,{span:17}),m(P,{span:3,class:"header-actions"},{default:S(()=>[s.isLoadingFaBu?(ne(),_n(U,{key:0,type:"primary",class:"publish-btn",onClick:I.saveAgent},{default:S(()=>c[1]||(c[1]=[be(" 发布 ")])),_:1},8,["onClick"])):ot("",!0),s.isLoadingUpdate?(ne(),_n(U,{key:1,type:"primary",class:"publish-btn",onClick:I.updateAgent},{default:S(()=>c[2]||(c[2]=[be(" 更新 ")])),_:1},8,["onClick"])):ot("",!0)]),_:1}),m(P,{span:1})]),_:1})]),_:1})]),_:1})}const g_=Xe(d_,[["render",p_],["__scopeId","data-v-11f1cd0e"]]),h_={props:{baseURL:String,imageUrl:String,agentName:String,description:String,groupId:Number},emits:["update:imageUrl","update:agentName","update:description","update:groupId"],data(){return{workgroups:[],allWorkgroups:[],workgroupsLoading:!1,allWorkgroupsLoading:!1,selectedGroupId:null}},watch:{groupId:{immediate:!0,handler(f){f&&(this.selectedGroupId=typeof f=="string"?parseInt(f,10):f,console.log("Updated selectedGroupId from prop:",this.selectedGroupId))}}},computed:{computedImageUrl:{get(){return this.imageUrl},set(f){this.$emit("update:imageUrl",f)}},computedAgentName:{get(){return this.agentName},set(f){this.$emit("update:agentName",f)}},computedDescription:{get(){return this.description},set(f){this.$emit("update:description",f)}},displayWorkgroups(){if(!this.selectedGroupId)return this.workgroups;if(this.workgroups.map(x=>x.id).includes(this.selectedGroupId))return this.workgroups;const s=this.allWorkgroups.find(x=>x.id===this.selectedGroupId);return s?[...this.workgroups,s]:this.workgroups}},methods:{handleAvatarSuccess(f){this.computedImageUrl=f.data},beforeAvatarUpload(f){return f.type!=="image/jpeg"&&f.type!=="image/png"?(this.$message.error("头像图片必须是JPG或PNG格式!"),!1):!0},async fetchWorkgroups(){this.workgroupsLoading=!0;try{const f=await Ns.getJoinedList();this.workgroups=(f==null?void 0:f.data)||[],await this.fetchAllWorkgroups()}catch(f){console.error("获取工作组列表错误:",f),$e.error("获取工作组列表失败")}finally{this.workgroupsLoading=!1}},async fetchAllWorkgroups(){this.allWorkgroupsLoading=!0;try{const f=await Ns.getAllGroupsList();this.allWorkgroups=(f==null?void 0:f.data)||[]}catch(f){console.error("获取所有工作组列表错误:",f)}finally{this.allWorkgroupsLoading=!1}},handleGroupChange(f){this.$emit("update:groupId",f)}},mounted(){this.fetchWorkgroups(),this.groupId&&(this.selectedGroupId=typeof this.groupId=="string"?parseInt(this.groupId,10):this.groupId)}},__={class:"basic-settings"},v_={class:"settings-content"},m_=["src"];function w_(f,c,s,x,y,I){const P=he("Plus"),U=to,$=Gh,z=Gn,E=Rr,Q=Wn,K=Gs,G=$s,T=Vs,k=st;return ne(),de("div",__,[m(k,{style:{padding:"10px"}},{header:S(()=>c[3]||(c[3]=[D("div",{class:"section-header"},[D("span",{class:"group-title"},"基本信息")],-1)])),default:S(()=>[m(Q,null,{default:S(()=>[m(z,null,{default:S(()=>[D("div",v_,[m(Q,null,{default:S(()=>[m(z,{span:8},{default:S(()=>[m($,{class:"avatar-uploader",action:s.baseURL,"show-file-list":!1,"on-success":I.handleAvatarSuccess,"before-upload":I.beforeAvatarUpload},{default:S(()=>[s.imageUrl?(ne(),de("img",{key:0,src:s.imageUrl,class:"avatar"},null,8,m_)):(ne(),_n(U,{key:1,class:"avatar-uploader-icon"},{default:S(()=>[m(P)]),_:1}))]),_:1},8,["action","on-success","before-upload"])]),_:1}),m(z,{span:16},{default:S(()=>[D("div",null,[m(E,{modelValue:I.computedAgentName,"onUpdate:modelValue":c[0]||(c[0]=j=>I.computedAgentName=j),placeholder:"我的应用名"},null,8,["modelValue"]),m(E,{modelValue:I.computedDescription,"onUpdate:modelValue":c[1]||(c[1]=j=>I.computedDescription=j),rows:2,class:"description-input",type:"textarea",placeholder:"应用描述"},null,8,["modelValue"])])]),_:1})]),_:1}),m(K,{type:"success",class:"image-tip"},{default:S(()=>c[4]||(c[4]=[be("图片的长宽比为360*140为最优，其他的会被拉伸哦")])),_:1}),m(Q,{class:"owner-row"},{default:S(()=>[m(z,{span:8},{default:S(()=>c[5]||(c[5]=[D("span",{class:"label"},"工作组",-1)])),_:1}),m(z,{span:16},{default:S(()=>[m(T,{modelValue:y.selectedGroupId,"onUpdate:modelValue":c[2]||(c[2]=j=>y.selectedGroupId=j),placeholder:"请选择工作组",clearable:"","loading-text":"加载中...",loading:y.workgroupsLoading,onChange:I.handleGroupChange,style:{width:"100%"}},{default:S(()=>[(ne(!0),de(Dr,null,Lr(I.displayWorkgroups,j=>(ne(),_n(G,{key:j.id,label:`${j.id} - ${j.name} (${j.business})`,value:j.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading","onChange"])]),_:1})]),_:1})])]),_:1})]),_:1})]),_:1})])}const S_=Xe(h_,[["render",w_],["__scopeId","data-v-c3b2e948"]]);var Ot={exports:{}};/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */var y_=Ot.exports,Ws;function C_(){return Ws||(Ws=1,function(f,c){(function(){var s,x="4.17.21",y=200,I="Unsupported core-js use. Try https://npms.io/search?q=ponyfill.",P="Expected a function",U="Invalid `variable` option passed into `_.template`",$="__lodash_hash_undefined__",z=500,E="__lodash_placeholder__",Q=1,K=2,G=4,T=1,k=2,j=1,Je=2,rn=4,Pe=8,on=16,Z=32,vn=64,qe=128,Vn=256,at=512,Et=30,_e="...",Ln=800,zs=16,oo=1,Zs=2,Ys=3,Tt=1/0,$n=9007199254740991,Xs=17976931348623157e292,Ut=NaN,Qe=**********,Js=Qe-1,Qs=Qe>>>1,js=[["ary",qe],["bind",j],["bindKey",Je],["curry",Pe],["curryRight",on],["flip",at],["partial",Z],["partialRight",vn],["rearg",Vn]],qn="[object Arguments]",Mt="[object Array]",ea="[object AsyncFunction]",lt="[object Boolean]",ft="[object Date]",na="[object DOMException]",Bt="[object Error]",Ft="[object Function]",uo="[object GeneratorFunction]",Ke="[object Map]",ct="[object Number]",ta="[object Null]",un="[object Object]",so="[object Promise]",ra="[object Proxy]",dt="[object RegExp]",He="[object Set]",pt="[object String]",Nt="[object Symbol]",ia="[object Undefined]",gt="[object WeakMap]",oa="[object WeakSet]",ht="[object ArrayBuffer]",Kn="[object DataView]",Tr="[object Float32Array]",Ur="[object Float64Array]",Mr="[object Int8Array]",Br="[object Int16Array]",Fr="[object Int32Array]",Nr="[object Uint8Array]",Wr="[object Uint8ClampedArray]",Gr="[object Uint16Array]",Vr="[object Uint32Array]",ua=/\b__p \+= '';/g,sa=/\b(__p \+=) '' \+/g,aa=/(__e\(.*?\)|\b__t\)) \+\n'';/g,ao=/&(?:amp|lt|gt|quot|#39);/g,lo=/[&<>"']/g,la=RegExp(ao.source),fa=RegExp(lo.source),ca=/<%-([\s\S]+?)%>/g,da=/<%([\s\S]+?)%>/g,fo=/<%=([\s\S]+?)%>/g,pa=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,ga=/^\w*$/,ha=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,$r=/[\\^$.*+?()[\]{}|]/g,_a=RegExp($r.source),qr=/^\s+/,va=/\s/,ma=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,wa=/\{\n\/\* \[wrapped with (.+)\] \*/,Sa=/,? & /,ya=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,Ca=/[()=,{}\[\]\/\s]/,xa=/\\(\\)?/g,Aa=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,co=/\w*$/,Ia=/^[-+]0x[0-9a-f]+$/i,ba=/^0b[01]+$/i,Pa=/^\[object .+?Constructor\]$/,Da=/^0o[0-7]+$/i,La=/^(?:0|[1-9]\d*)$/,Ra=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,Wt=/($^)/,Oa=/['\n\r\u2028\u2029\\]/g,Gt="\\ud800-\\udfff",Ea="\\u0300-\\u036f",Ta="\\ufe20-\\ufe2f",Ua="\\u20d0-\\u20ff",po=Ea+Ta+Ua,go="\\u2700-\\u27bf",ho="a-z\\xdf-\\xf6\\xf8-\\xff",Ma="\\xac\\xb1\\xd7\\xf7",Ba="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",Fa="\\u2000-\\u206f",Na=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",_o="A-Z\\xc0-\\xd6\\xd8-\\xde",vo="\\ufe0e\\ufe0f",mo=Ma+Ba+Fa+Na,Kr="['’]",Wa="["+Gt+"]",wo="["+mo+"]",Vt="["+po+"]",So="\\d+",Ga="["+go+"]",yo="["+ho+"]",Co="[^"+Gt+mo+So+go+ho+_o+"]",Hr="\\ud83c[\\udffb-\\udfff]",Va="(?:"+Vt+"|"+Hr+")",xo="[^"+Gt+"]",kr="(?:\\ud83c[\\udde6-\\uddff]){2}",zr="[\\ud800-\\udbff][\\udc00-\\udfff]",Hn="["+_o+"]",Ao="\\u200d",Io="(?:"+yo+"|"+Co+")",$a="(?:"+Hn+"|"+Co+")",bo="(?:"+Kr+"(?:d|ll|m|re|s|t|ve))?",Po="(?:"+Kr+"(?:D|LL|M|RE|S|T|VE))?",Do=Va+"?",Lo="["+vo+"]?",qa="(?:"+Ao+"(?:"+[xo,kr,zr].join("|")+")"+Lo+Do+")*",Ka="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",Ha="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",Ro=Lo+Do+qa,ka="(?:"+[Ga,kr,zr].join("|")+")"+Ro,za="(?:"+[xo+Vt+"?",Vt,kr,zr,Wa].join("|")+")",Za=RegExp(Kr,"g"),Ya=RegExp(Vt,"g"),Zr=RegExp(Hr+"(?="+Hr+")|"+za+Ro,"g"),Xa=RegExp([Hn+"?"+yo+"+"+bo+"(?="+[wo,Hn,"$"].join("|")+")",$a+"+"+Po+"(?="+[wo,Hn+Io,"$"].join("|")+")",Hn+"?"+Io+"+"+bo,Hn+"+"+Po,Ha,Ka,So,ka].join("|"),"g"),Ja=RegExp("["+Ao+Gt+po+vo+"]"),Qa=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,ja=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],el=-1,re={};re[Tr]=re[Ur]=re[Mr]=re[Br]=re[Fr]=re[Nr]=re[Wr]=re[Gr]=re[Vr]=!0,re[qn]=re[Mt]=re[ht]=re[lt]=re[Kn]=re[ft]=re[Bt]=re[Ft]=re[Ke]=re[ct]=re[un]=re[dt]=re[He]=re[pt]=re[gt]=!1;var te={};te[qn]=te[Mt]=te[ht]=te[Kn]=te[lt]=te[ft]=te[Tr]=te[Ur]=te[Mr]=te[Br]=te[Fr]=te[Ke]=te[ct]=te[un]=te[dt]=te[He]=te[pt]=te[Nt]=te[Nr]=te[Wr]=te[Gr]=te[Vr]=!0,te[Bt]=te[Ft]=te[gt]=!1;var nl={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"},tl={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},rl={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"},il={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},ol=parseFloat,ul=parseInt,Oo=typeof Pr=="object"&&Pr&&Pr.Object===Object&&Pr,sl=typeof self=="object"&&self&&self.Object===Object&&self,pe=Oo||sl||Function("return this")(),Yr=c&&!c.nodeType&&c,Rn=Yr&&!0&&f&&!f.nodeType&&f,Eo=Rn&&Rn.exports===Yr,Xr=Eo&&Oo.process,Ue=function(){try{var d=Rn&&Rn.require&&Rn.require("util").types;return d||Xr&&Xr.binding&&Xr.binding("util")}catch{}}(),To=Ue&&Ue.isArrayBuffer,Uo=Ue&&Ue.isDate,Mo=Ue&&Ue.isMap,Bo=Ue&&Ue.isRegExp,Fo=Ue&&Ue.isSet,No=Ue&&Ue.isTypedArray;function De(d,h,g){switch(g.length){case 0:return d.call(h);case 1:return d.call(h,g[0]);case 2:return d.call(h,g[0],g[1]);case 3:return d.call(h,g[0],g[1],g[2])}return d.apply(h,g)}function al(d,h,g,A){for(var M=-1,Y=d==null?0:d.length;++M<Y;){var le=d[M];h(A,le,g(le),d)}return A}function Me(d,h){for(var g=-1,A=d==null?0:d.length;++g<A&&h(d[g],g,d)!==!1;);return d}function ll(d,h){for(var g=d==null?0:d.length;g--&&h(d[g],g,d)!==!1;);return d}function Wo(d,h){for(var g=-1,A=d==null?0:d.length;++g<A;)if(!h(d[g],g,d))return!1;return!0}function mn(d,h){for(var g=-1,A=d==null?0:d.length,M=0,Y=[];++g<A;){var le=d[g];h(le,g,d)&&(Y[M++]=le)}return Y}function $t(d,h){var g=d==null?0:d.length;return!!g&&kn(d,h,0)>-1}function Jr(d,h,g){for(var A=-1,M=d==null?0:d.length;++A<M;)if(g(h,d[A]))return!0;return!1}function ie(d,h){for(var g=-1,A=d==null?0:d.length,M=Array(A);++g<A;)M[g]=h(d[g],g,d);return M}function wn(d,h){for(var g=-1,A=h.length,M=d.length;++g<A;)d[M+g]=h[g];return d}function Qr(d,h,g,A){var M=-1,Y=d==null?0:d.length;for(A&&Y&&(g=d[++M]);++M<Y;)g=h(g,d[M],M,d);return g}function fl(d,h,g,A){var M=d==null?0:d.length;for(A&&M&&(g=d[--M]);M--;)g=h(g,d[M],M,d);return g}function jr(d,h){for(var g=-1,A=d==null?0:d.length;++g<A;)if(h(d[g],g,d))return!0;return!1}var cl=ei("length");function dl(d){return d.split("")}function pl(d){return d.match(ya)||[]}function Go(d,h,g){var A;return g(d,function(M,Y,le){if(h(M,Y,le))return A=Y,!1}),A}function qt(d,h,g,A){for(var M=d.length,Y=g+(A?1:-1);A?Y--:++Y<M;)if(h(d[Y],Y,d))return Y;return-1}function kn(d,h,g){return h===h?Il(d,h,g):qt(d,Vo,g)}function gl(d,h,g,A){for(var M=g-1,Y=d.length;++M<Y;)if(A(d[M],h))return M;return-1}function Vo(d){return d!==d}function $o(d,h){var g=d==null?0:d.length;return g?ti(d,h)/g:Ut}function ei(d){return function(h){return h==null?s:h[d]}}function ni(d){return function(h){return d==null?s:d[h]}}function qo(d,h,g,A,M){return M(d,function(Y,le,ee){g=A?(A=!1,Y):h(g,Y,le,ee)}),g}function hl(d,h){var g=d.length;for(d.sort(h);g--;)d[g]=d[g].value;return d}function ti(d,h){for(var g,A=-1,M=d.length;++A<M;){var Y=h(d[A]);Y!==s&&(g=g===s?Y:g+Y)}return g}function ri(d,h){for(var g=-1,A=Array(d);++g<d;)A[g]=h(g);return A}function _l(d,h){return ie(h,function(g){return[g,d[g]]})}function Ko(d){return d&&d.slice(0,Zo(d)+1).replace(qr,"")}function Le(d){return function(h){return d(h)}}function ii(d,h){return ie(h,function(g){return d[g]})}function _t(d,h){return d.has(h)}function Ho(d,h){for(var g=-1,A=d.length;++g<A&&kn(h,d[g],0)>-1;);return g}function ko(d,h){for(var g=d.length;g--&&kn(h,d[g],0)>-1;);return g}function vl(d,h){for(var g=d.length,A=0;g--;)d[g]===h&&++A;return A}var ml=ni(nl),wl=ni(tl);function Sl(d){return"\\"+il[d]}function yl(d,h){return d==null?s:d[h]}function zn(d){return Ja.test(d)}function Cl(d){return Qa.test(d)}function xl(d){for(var h,g=[];!(h=d.next()).done;)g.push(h.value);return g}function oi(d){var h=-1,g=Array(d.size);return d.forEach(function(A,M){g[++h]=[M,A]}),g}function zo(d,h){return function(g){return d(h(g))}}function Sn(d,h){for(var g=-1,A=d.length,M=0,Y=[];++g<A;){var le=d[g];(le===h||le===E)&&(d[g]=E,Y[M++]=g)}return Y}function Kt(d){var h=-1,g=Array(d.size);return d.forEach(function(A){g[++h]=A}),g}function Al(d){var h=-1,g=Array(d.size);return d.forEach(function(A){g[++h]=[A,A]}),g}function Il(d,h,g){for(var A=g-1,M=d.length;++A<M;)if(d[A]===h)return A;return-1}function bl(d,h,g){for(var A=g+1;A--;)if(d[A]===h)return A;return A}function Zn(d){return zn(d)?Dl(d):cl(d)}function ke(d){return zn(d)?Ll(d):dl(d)}function Zo(d){for(var h=d.length;h--&&va.test(d.charAt(h)););return h}var Pl=ni(rl);function Dl(d){for(var h=Zr.lastIndex=0;Zr.test(d);)++h;return h}function Ll(d){return d.match(Zr)||[]}function Rl(d){return d.match(Xa)||[]}var Ol=function d(h){h=h==null?pe:Yn.defaults(pe.Object(),h,Yn.pick(pe,ja));var g=h.Array,A=h.Date,M=h.Error,Y=h.Function,le=h.Math,ee=h.Object,ui=h.RegExp,El=h.String,Be=h.TypeError,Ht=g.prototype,Tl=Y.prototype,Xn=ee.prototype,kt=h["__core-js_shared__"],zt=Tl.toString,J=Xn.hasOwnProperty,Ul=0,Yo=function(){var e=/[^.]+$/.exec(kt&&kt.keys&&kt.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}(),Zt=Xn.toString,Ml=zt.call(ee),Bl=pe._,Fl=ui("^"+zt.call(J).replace($r,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Yt=Eo?h.Buffer:s,yn=h.Symbol,Xt=h.Uint8Array,Xo=Yt?Yt.allocUnsafe:s,Jt=zo(ee.getPrototypeOf,ee),Jo=ee.create,Qo=Xn.propertyIsEnumerable,Qt=Ht.splice,jo=yn?yn.isConcatSpreadable:s,vt=yn?yn.iterator:s,On=yn?yn.toStringTag:s,jt=function(){try{var e=Bn(ee,"defineProperty");return e({},"",{}),e}catch{}}(),Nl=h.clearTimeout!==pe.clearTimeout&&h.clearTimeout,Wl=A&&A.now!==pe.Date.now&&A.now,Gl=h.setTimeout!==pe.setTimeout&&h.setTimeout,er=le.ceil,nr=le.floor,si=ee.getOwnPropertySymbols,Vl=Yt?Yt.isBuffer:s,eu=h.isFinite,$l=Ht.join,ql=zo(ee.keys,ee),fe=le.max,ve=le.min,Kl=A.now,Hl=h.parseInt,nu=le.random,kl=Ht.reverse,ai=Bn(h,"DataView"),mt=Bn(h,"Map"),li=Bn(h,"Promise"),Jn=Bn(h,"Set"),wt=Bn(h,"WeakMap"),St=Bn(ee,"create"),tr=wt&&new wt,Qn={},zl=Fn(ai),Zl=Fn(mt),Yl=Fn(li),Xl=Fn(Jn),Jl=Fn(wt),rr=yn?yn.prototype:s,yt=rr?rr.valueOf:s,tu=rr?rr.toString:s;function o(e){if(ue(e)&&!B(e)&&!(e instanceof q)){if(e instanceof Fe)return e;if(J.call(e,"__wrapped__"))return rs(e)}return new Fe(e)}var jn=function(){function e(){}return function(n){if(!oe(n))return{};if(Jo)return Jo(n);e.prototype=n;var t=new e;return e.prototype=s,t}}();function ir(){}function Fe(e,n){this.__wrapped__=e,this.__actions__=[],this.__chain__=!!n,this.__index__=0,this.__values__=s}o.templateSettings={escape:ca,evaluate:da,interpolate:fo,variable:"",imports:{_:o}},o.prototype=ir.prototype,o.prototype.constructor=o,Fe.prototype=jn(ir.prototype),Fe.prototype.constructor=Fe;function q(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=Qe,this.__views__=[]}function Ql(){var e=new q(this.__wrapped__);return e.__actions__=Ce(this.__actions__),e.__dir__=this.__dir__,e.__filtered__=this.__filtered__,e.__iteratees__=Ce(this.__iteratees__),e.__takeCount__=this.__takeCount__,e.__views__=Ce(this.__views__),e}function jl(){if(this.__filtered__){var e=new q(this);e.__dir__=-1,e.__filtered__=!0}else e=this.clone(),e.__dir__*=-1;return e}function ef(){var e=this.__wrapped__.value(),n=this.__dir__,t=B(e),r=n<0,i=t?e.length:0,u=pc(0,i,this.__views__),a=u.start,l=u.end,p=l-a,_=r?l:a-1,v=this.__iteratees__,w=v.length,C=0,b=ve(p,this.__takeCount__);if(!t||!r&&i==p&&b==p)return bu(e,this.__actions__);var R=[];e:for(;p--&&C<b;){_+=n;for(var N=-1,O=e[_];++N<w;){var V=v[N],H=V.iteratee,Ee=V.type,ye=H(O);if(Ee==Zs)O=ye;else if(!ye){if(Ee==oo)continue e;break e}}R[C++]=O}return R}q.prototype=jn(ir.prototype),q.prototype.constructor=q;function En(e){var n=-1,t=e==null?0:e.length;for(this.clear();++n<t;){var r=e[n];this.set(r[0],r[1])}}function nf(){this.__data__=St?St(null):{},this.size=0}function tf(e){var n=this.has(e)&&delete this.__data__[e];return this.size-=n?1:0,n}function rf(e){var n=this.__data__;if(St){var t=n[e];return t===$?s:t}return J.call(n,e)?n[e]:s}function of(e){var n=this.__data__;return St?n[e]!==s:J.call(n,e)}function uf(e,n){var t=this.__data__;return this.size+=this.has(e)?0:1,t[e]=St&&n===s?$:n,this}En.prototype.clear=nf,En.prototype.delete=tf,En.prototype.get=rf,En.prototype.has=of,En.prototype.set=uf;function sn(e){var n=-1,t=e==null?0:e.length;for(this.clear();++n<t;){var r=e[n];this.set(r[0],r[1])}}function sf(){this.__data__=[],this.size=0}function af(e){var n=this.__data__,t=or(n,e);if(t<0)return!1;var r=n.length-1;return t==r?n.pop():Qt.call(n,t,1),--this.size,!0}function lf(e){var n=this.__data__,t=or(n,e);return t<0?s:n[t][1]}function ff(e){return or(this.__data__,e)>-1}function cf(e,n){var t=this.__data__,r=or(t,e);return r<0?(++this.size,t.push([e,n])):t[r][1]=n,this}sn.prototype.clear=sf,sn.prototype.delete=af,sn.prototype.get=lf,sn.prototype.has=ff,sn.prototype.set=cf;function an(e){var n=-1,t=e==null?0:e.length;for(this.clear();++n<t;){var r=e[n];this.set(r[0],r[1])}}function df(){this.size=0,this.__data__={hash:new En,map:new(mt||sn),string:new En}}function pf(e){var n=vr(this,e).delete(e);return this.size-=n?1:0,n}function gf(e){return vr(this,e).get(e)}function hf(e){return vr(this,e).has(e)}function _f(e,n){var t=vr(this,e),r=t.size;return t.set(e,n),this.size+=t.size==r?0:1,this}an.prototype.clear=df,an.prototype.delete=pf,an.prototype.get=gf,an.prototype.has=hf,an.prototype.set=_f;function Tn(e){var n=-1,t=e==null?0:e.length;for(this.__data__=new an;++n<t;)this.add(e[n])}function vf(e){return this.__data__.set(e,$),this}function mf(e){return this.__data__.has(e)}Tn.prototype.add=Tn.prototype.push=vf,Tn.prototype.has=mf;function ze(e){var n=this.__data__=new sn(e);this.size=n.size}function wf(){this.__data__=new sn,this.size=0}function Sf(e){var n=this.__data__,t=n.delete(e);return this.size=n.size,t}function yf(e){return this.__data__.get(e)}function Cf(e){return this.__data__.has(e)}function xf(e,n){var t=this.__data__;if(t instanceof sn){var r=t.__data__;if(!mt||r.length<y-1)return r.push([e,n]),this.size=++t.size,this;t=this.__data__=new an(r)}return t.set(e,n),this.size=t.size,this}ze.prototype.clear=wf,ze.prototype.delete=Sf,ze.prototype.get=yf,ze.prototype.has=Cf,ze.prototype.set=xf;function ru(e,n){var t=B(e),r=!t&&Nn(e),i=!t&&!r&&bn(e),u=!t&&!r&&!i&&rt(e),a=t||r||i||u,l=a?ri(e.length,El):[],p=l.length;for(var _ in e)(n||J.call(e,_))&&!(a&&(_=="length"||i&&(_=="offset"||_=="parent")||u&&(_=="buffer"||_=="byteLength"||_=="byteOffset")||dn(_,p)))&&l.push(_);return l}function iu(e){var n=e.length;return n?e[Si(0,n-1)]:s}function Af(e,n){return mr(Ce(e),Un(n,0,e.length))}function If(e){return mr(Ce(e))}function fi(e,n,t){(t!==s&&!Ze(e[n],t)||t===s&&!(n in e))&&ln(e,n,t)}function Ct(e,n,t){var r=e[n];(!(J.call(e,n)&&Ze(r,t))||t===s&&!(n in e))&&ln(e,n,t)}function or(e,n){for(var t=e.length;t--;)if(Ze(e[t][0],n))return t;return-1}function bf(e,n,t,r){return Cn(e,function(i,u,a){n(r,i,t(i),a)}),r}function ou(e,n){return e&&en(n,ce(n),e)}function Pf(e,n){return e&&en(n,Ae(n),e)}function ln(e,n,t){n=="__proto__"&&jt?jt(e,n,{configurable:!0,enumerable:!0,value:t,writable:!0}):e[n]=t}function ci(e,n){for(var t=-1,r=n.length,i=g(r),u=e==null;++t<r;)i[t]=u?s:Hi(e,n[t]);return i}function Un(e,n,t){return e===e&&(t!==s&&(e=e<=t?e:t),n!==s&&(e=e>=n?e:n)),e}function Ne(e,n,t,r,i,u){var a,l=n&Q,p=n&K,_=n&G;if(t&&(a=i?t(e,r,i,u):t(e)),a!==s)return a;if(!oe(e))return e;var v=B(e);if(v){if(a=hc(e),!l)return Ce(e,a)}else{var w=me(e),C=w==Ft||w==uo;if(bn(e))return Lu(e,l);if(w==un||w==qn||C&&!i){if(a=p||C?{}:Zu(e),!l)return p?ic(e,Pf(a,e)):rc(e,ou(a,e))}else{if(!te[w])return i?e:{};a=_c(e,w,l)}}u||(u=new ze);var b=u.get(e);if(b)return b;u.set(e,a),xs(e)?e.forEach(function(O){a.add(Ne(O,n,t,O,e,u))}):ys(e)&&e.forEach(function(O,V){a.set(V,Ne(O,n,t,V,e,u))});var R=_?p?Oi:Ri:p?Ae:ce,N=v?s:R(e);return Me(N||e,function(O,V){N&&(V=O,O=e[V]),Ct(a,V,Ne(O,n,t,V,e,u))}),a}function Df(e){var n=ce(e);return function(t){return uu(t,e,n)}}function uu(e,n,t){var r=t.length;if(e==null)return!r;for(e=ee(e);r--;){var i=t[r],u=n[i],a=e[i];if(a===s&&!(i in e)||!u(a))return!1}return!0}function su(e,n,t){if(typeof e!="function")throw new Be(P);return Lt(function(){e.apply(s,t)},n)}function xt(e,n,t,r){var i=-1,u=$t,a=!0,l=e.length,p=[],_=n.length;if(!l)return p;t&&(n=ie(n,Le(t))),r?(u=Jr,a=!1):n.length>=y&&(u=_t,a=!1,n=new Tn(n));e:for(;++i<l;){var v=e[i],w=t==null?v:t(v);if(v=r||v!==0?v:0,a&&w===w){for(var C=_;C--;)if(n[C]===w)continue e;p.push(v)}else u(n,w,r)||p.push(v)}return p}var Cn=Uu(je),au=Uu(pi,!0);function Lf(e,n){var t=!0;return Cn(e,function(r,i,u){return t=!!n(r,i,u),t}),t}function ur(e,n,t){for(var r=-1,i=e.length;++r<i;){var u=e[r],a=n(u);if(a!=null&&(l===s?a===a&&!Oe(a):t(a,l)))var l=a,p=u}return p}function Rf(e,n,t,r){var i=e.length;for(t=F(t),t<0&&(t=-t>i?0:i+t),r=r===s||r>i?i:F(r),r<0&&(r+=i),r=t>r?0:Is(r);t<r;)e[t++]=n;return e}function lu(e,n){var t=[];return Cn(e,function(r,i,u){n(r,i,u)&&t.push(r)}),t}function ge(e,n,t,r,i){var u=-1,a=e.length;for(t||(t=mc),i||(i=[]);++u<a;){var l=e[u];n>0&&t(l)?n>1?ge(l,n-1,t,r,i):wn(i,l):r||(i[i.length]=l)}return i}var di=Mu(),fu=Mu(!0);function je(e,n){return e&&di(e,n,ce)}function pi(e,n){return e&&fu(e,n,ce)}function sr(e,n){return mn(n,function(t){return pn(e[t])})}function Mn(e,n){n=An(n,e);for(var t=0,r=n.length;e!=null&&t<r;)e=e[nn(n[t++])];return t&&t==r?e:s}function cu(e,n,t){var r=n(e);return B(e)?r:wn(r,t(e))}function we(e){return e==null?e===s?ia:ta:On&&On in ee(e)?dc(e):Ic(e)}function gi(e,n){return e>n}function Of(e,n){return e!=null&&J.call(e,n)}function Ef(e,n){return e!=null&&n in ee(e)}function Tf(e,n,t){return e>=ve(n,t)&&e<fe(n,t)}function hi(e,n,t){for(var r=t?Jr:$t,i=e[0].length,u=e.length,a=u,l=g(u),p=1/0,_=[];a--;){var v=e[a];a&&n&&(v=ie(v,Le(n))),p=ve(v.length,p),l[a]=!t&&(n||i>=120&&v.length>=120)?new Tn(a&&v):s}v=e[0];var w=-1,C=l[0];e:for(;++w<i&&_.length<p;){var b=v[w],R=n?n(b):b;if(b=t||b!==0?b:0,!(C?_t(C,R):r(_,R,t))){for(a=u;--a;){var N=l[a];if(!(N?_t(N,R):r(e[a],R,t)))continue e}C&&C.push(R),_.push(b)}}return _}function Uf(e,n,t,r){return je(e,function(i,u,a){n(r,t(i),u,a)}),r}function At(e,n,t){n=An(n,e),e=Qu(e,n);var r=e==null?e:e[nn(Ge(n))];return r==null?s:De(r,e,t)}function du(e){return ue(e)&&we(e)==qn}function Mf(e){return ue(e)&&we(e)==ht}function Bf(e){return ue(e)&&we(e)==ft}function It(e,n,t,r,i){return e===n?!0:e==null||n==null||!ue(e)&&!ue(n)?e!==e&&n!==n:Ff(e,n,t,r,It,i)}function Ff(e,n,t,r,i,u){var a=B(e),l=B(n),p=a?Mt:me(e),_=l?Mt:me(n);p=p==qn?un:p,_=_==qn?un:_;var v=p==un,w=_==un,C=p==_;if(C&&bn(e)){if(!bn(n))return!1;a=!0,v=!1}if(C&&!v)return u||(u=new ze),a||rt(e)?Hu(e,n,t,r,i,u):fc(e,n,p,t,r,i,u);if(!(t&T)){var b=v&&J.call(e,"__wrapped__"),R=w&&J.call(n,"__wrapped__");if(b||R){var N=b?e.value():e,O=R?n.value():n;return u||(u=new ze),i(N,O,t,r,u)}}return C?(u||(u=new ze),cc(e,n,t,r,i,u)):!1}function Nf(e){return ue(e)&&me(e)==Ke}function _i(e,n,t,r){var i=t.length,u=i,a=!r;if(e==null)return!u;for(e=ee(e);i--;){var l=t[i];if(a&&l[2]?l[1]!==e[l[0]]:!(l[0]in e))return!1}for(;++i<u;){l=t[i];var p=l[0],_=e[p],v=l[1];if(a&&l[2]){if(_===s&&!(p in e))return!1}else{var w=new ze;if(r)var C=r(_,v,p,e,n,w);if(!(C===s?It(v,_,T|k,r,w):C))return!1}}return!0}function pu(e){if(!oe(e)||Sc(e))return!1;var n=pn(e)?Fl:Pa;return n.test(Fn(e))}function Wf(e){return ue(e)&&we(e)==dt}function Gf(e){return ue(e)&&me(e)==He}function Vf(e){return ue(e)&&Ar(e.length)&&!!re[we(e)]}function gu(e){return typeof e=="function"?e:e==null?Ie:typeof e=="object"?B(e)?vu(e[0],e[1]):_u(e):Bs(e)}function vi(e){if(!Dt(e))return ql(e);var n=[];for(var t in ee(e))J.call(e,t)&&t!="constructor"&&n.push(t);return n}function $f(e){if(!oe(e))return Ac(e);var n=Dt(e),t=[];for(var r in e)r=="constructor"&&(n||!J.call(e,r))||t.push(r);return t}function mi(e,n){return e<n}function hu(e,n){var t=-1,r=xe(e)?g(e.length):[];return Cn(e,function(i,u,a){r[++t]=n(i,u,a)}),r}function _u(e){var n=Ti(e);return n.length==1&&n[0][2]?Xu(n[0][0],n[0][1]):function(t){return t===e||_i(t,e,n)}}function vu(e,n){return Mi(e)&&Yu(n)?Xu(nn(e),n):function(t){var r=Hi(t,e);return r===s&&r===n?ki(t,e):It(n,r,T|k)}}function ar(e,n,t,r,i){e!==n&&di(n,function(u,a){if(i||(i=new ze),oe(u))qf(e,n,a,t,ar,r,i);else{var l=r?r(Fi(e,a),u,a+"",e,n,i):s;l===s&&(l=u),fi(e,a,l)}},Ae)}function qf(e,n,t,r,i,u,a){var l=Fi(e,t),p=Fi(n,t),_=a.get(p);if(_){fi(e,t,_);return}var v=u?u(l,p,t+"",e,n,a):s,w=v===s;if(w){var C=B(p),b=!C&&bn(p),R=!C&&!b&&rt(p);v=p,C||b||R?B(l)?v=l:se(l)?v=Ce(l):b?(w=!1,v=Lu(p,!0)):R?(w=!1,v=Ru(p,!0)):v=[]:Rt(p)||Nn(p)?(v=l,Nn(l)?v=bs(l):(!oe(l)||pn(l))&&(v=Zu(p))):w=!1}w&&(a.set(p,v),i(v,p,r,u,a),a.delete(p)),fi(e,t,v)}function mu(e,n){var t=e.length;if(t)return n+=n<0?t:0,dn(n,t)?e[n]:s}function wu(e,n,t){n.length?n=ie(n,function(u){return B(u)?function(a){return Mn(a,u.length===1?u[0]:u)}:u}):n=[Ie];var r=-1;n=ie(n,Le(L()));var i=hu(e,function(u,a,l){var p=ie(n,function(_){return _(u)});return{criteria:p,index:++r,value:u}});return hl(i,function(u,a){return tc(u,a,t)})}function Kf(e,n){return Su(e,n,function(t,r){return ki(e,r)})}function Su(e,n,t){for(var r=-1,i=n.length,u={};++r<i;){var a=n[r],l=Mn(e,a);t(l,a)&&bt(u,An(a,e),l)}return u}function Hf(e){return function(n){return Mn(n,e)}}function wi(e,n,t,r){var i=r?gl:kn,u=-1,a=n.length,l=e;for(e===n&&(n=Ce(n)),t&&(l=ie(e,Le(t)));++u<a;)for(var p=0,_=n[u],v=t?t(_):_;(p=i(l,v,p,r))>-1;)l!==e&&Qt.call(l,p,1),Qt.call(e,p,1);return e}function yu(e,n){for(var t=e?n.length:0,r=t-1;t--;){var i=n[t];if(t==r||i!==u){var u=i;dn(i)?Qt.call(e,i,1):xi(e,i)}}return e}function Si(e,n){return e+nr(nu()*(n-e+1))}function kf(e,n,t,r){for(var i=-1,u=fe(er((n-e)/(t||1)),0),a=g(u);u--;)a[r?u:++i]=e,e+=t;return a}function yi(e,n){var t="";if(!e||n<1||n>$n)return t;do n%2&&(t+=e),n=nr(n/2),n&&(e+=e);while(n);return t}function W(e,n){return Ni(Ju(e,n,Ie),e+"")}function zf(e){return iu(it(e))}function Zf(e,n){var t=it(e);return mr(t,Un(n,0,t.length))}function bt(e,n,t,r){if(!oe(e))return e;n=An(n,e);for(var i=-1,u=n.length,a=u-1,l=e;l!=null&&++i<u;){var p=nn(n[i]),_=t;if(p==="__proto__"||p==="constructor"||p==="prototype")return e;if(i!=a){var v=l[p];_=r?r(v,p,l):s,_===s&&(_=oe(v)?v:dn(n[i+1])?[]:{})}Ct(l,p,_),l=l[p]}return e}var Cu=tr?function(e,n){return tr.set(e,n),e}:Ie,Yf=jt?function(e,n){return jt(e,"toString",{configurable:!0,enumerable:!1,value:Zi(n),writable:!0})}:Ie;function Xf(e){return mr(it(e))}function We(e,n,t){var r=-1,i=e.length;n<0&&(n=-n>i?0:i+n),t=t>i?i:t,t<0&&(t+=i),i=n>t?0:t-n>>>0,n>>>=0;for(var u=g(i);++r<i;)u[r]=e[r+n];return u}function Jf(e,n){var t;return Cn(e,function(r,i,u){return t=n(r,i,u),!t}),!!t}function lr(e,n,t){var r=0,i=e==null?r:e.length;if(typeof n=="number"&&n===n&&i<=Qs){for(;r<i;){var u=r+i>>>1,a=e[u];a!==null&&!Oe(a)&&(t?a<=n:a<n)?r=u+1:i=u}return i}return Ci(e,n,Ie,t)}function Ci(e,n,t,r){var i=0,u=e==null?0:e.length;if(u===0)return 0;n=t(n);for(var a=n!==n,l=n===null,p=Oe(n),_=n===s;i<u;){var v=nr((i+u)/2),w=t(e[v]),C=w!==s,b=w===null,R=w===w,N=Oe(w);if(a)var O=r||R;else _?O=R&&(r||C):l?O=R&&C&&(r||!b):p?O=R&&C&&!b&&(r||!N):b||N?O=!1:O=r?w<=n:w<n;O?i=v+1:u=v}return ve(u,Js)}function xu(e,n){for(var t=-1,r=e.length,i=0,u=[];++t<r;){var a=e[t],l=n?n(a):a;if(!t||!Ze(l,p)){var p=l;u[i++]=a===0?0:a}}return u}function Au(e){return typeof e=="number"?e:Oe(e)?Ut:+e}function Re(e){if(typeof e=="string")return e;if(B(e))return ie(e,Re)+"";if(Oe(e))return tu?tu.call(e):"";var n=e+"";return n=="0"&&1/e==-1/0?"-0":n}function xn(e,n,t){var r=-1,i=$t,u=e.length,a=!0,l=[],p=l;if(t)a=!1,i=Jr;else if(u>=y){var _=n?null:ac(e);if(_)return Kt(_);a=!1,i=_t,p=new Tn}else p=n?[]:l;e:for(;++r<u;){var v=e[r],w=n?n(v):v;if(v=t||v!==0?v:0,a&&w===w){for(var C=p.length;C--;)if(p[C]===w)continue e;n&&p.push(w),l.push(v)}else i(p,w,t)||(p!==l&&p.push(w),l.push(v))}return l}function xi(e,n){return n=An(n,e),e=Qu(e,n),e==null||delete e[nn(Ge(n))]}function Iu(e,n,t,r){return bt(e,n,t(Mn(e,n)),r)}function fr(e,n,t,r){for(var i=e.length,u=r?i:-1;(r?u--:++u<i)&&n(e[u],u,e););return t?We(e,r?0:u,r?u+1:i):We(e,r?u+1:0,r?i:u)}function bu(e,n){var t=e;return t instanceof q&&(t=t.value()),Qr(n,function(r,i){return i.func.apply(i.thisArg,wn([r],i.args))},t)}function Ai(e,n,t){var r=e.length;if(r<2)return r?xn(e[0]):[];for(var i=-1,u=g(r);++i<r;)for(var a=e[i],l=-1;++l<r;)l!=i&&(u[i]=xt(u[i]||a,e[l],n,t));return xn(ge(u,1),n,t)}function Pu(e,n,t){for(var r=-1,i=e.length,u=n.length,a={};++r<i;){var l=r<u?n[r]:s;t(a,e[r],l)}return a}function Ii(e){return se(e)?e:[]}function bi(e){return typeof e=="function"?e:Ie}function An(e,n){return B(e)?e:Mi(e,n)?[e]:ts(X(e))}var Qf=W;function In(e,n,t){var r=e.length;return t=t===s?r:t,!n&&t>=r?e:We(e,n,t)}var Du=Nl||function(e){return pe.clearTimeout(e)};function Lu(e,n){if(n)return e.slice();var t=e.length,r=Xo?Xo(t):new e.constructor(t);return e.copy(r),r}function Pi(e){var n=new e.constructor(e.byteLength);return new Xt(n).set(new Xt(e)),n}function jf(e,n){var t=n?Pi(e.buffer):e.buffer;return new e.constructor(t,e.byteOffset,e.byteLength)}function ec(e){var n=new e.constructor(e.source,co.exec(e));return n.lastIndex=e.lastIndex,n}function nc(e){return yt?ee(yt.call(e)):{}}function Ru(e,n){var t=n?Pi(e.buffer):e.buffer;return new e.constructor(t,e.byteOffset,e.length)}function Ou(e,n){if(e!==n){var t=e!==s,r=e===null,i=e===e,u=Oe(e),a=n!==s,l=n===null,p=n===n,_=Oe(n);if(!l&&!_&&!u&&e>n||u&&a&&p&&!l&&!_||r&&a&&p||!t&&p||!i)return 1;if(!r&&!u&&!_&&e<n||_&&t&&i&&!r&&!u||l&&t&&i||!a&&i||!p)return-1}return 0}function tc(e,n,t){for(var r=-1,i=e.criteria,u=n.criteria,a=i.length,l=t.length;++r<a;){var p=Ou(i[r],u[r]);if(p){if(r>=l)return p;var _=t[r];return p*(_=="desc"?-1:1)}}return e.index-n.index}function Eu(e,n,t,r){for(var i=-1,u=e.length,a=t.length,l=-1,p=n.length,_=fe(u-a,0),v=g(p+_),w=!r;++l<p;)v[l]=n[l];for(;++i<a;)(w||i<u)&&(v[t[i]]=e[i]);for(;_--;)v[l++]=e[i++];return v}function Tu(e,n,t,r){for(var i=-1,u=e.length,a=-1,l=t.length,p=-1,_=n.length,v=fe(u-l,0),w=g(v+_),C=!r;++i<v;)w[i]=e[i];for(var b=i;++p<_;)w[b+p]=n[p];for(;++a<l;)(C||i<u)&&(w[b+t[a]]=e[i++]);return w}function Ce(e,n){var t=-1,r=e.length;for(n||(n=g(r));++t<r;)n[t]=e[t];return n}function en(e,n,t,r){var i=!t;t||(t={});for(var u=-1,a=n.length;++u<a;){var l=n[u],p=r?r(t[l],e[l],l,t,e):s;p===s&&(p=e[l]),i?ln(t,l,p):Ct(t,l,p)}return t}function rc(e,n){return en(e,Ui(e),n)}function ic(e,n){return en(e,ku(e),n)}function cr(e,n){return function(t,r){var i=B(t)?al:bf,u=n?n():{};return i(t,e,L(r,2),u)}}function et(e){return W(function(n,t){var r=-1,i=t.length,u=i>1?t[i-1]:s,a=i>2?t[2]:s;for(u=e.length>3&&typeof u=="function"?(i--,u):s,a&&Se(t[0],t[1],a)&&(u=i<3?s:u,i=1),n=ee(n);++r<i;){var l=t[r];l&&e(n,l,r,u)}return n})}function Uu(e,n){return function(t,r){if(t==null)return t;if(!xe(t))return e(t,r);for(var i=t.length,u=n?i:-1,a=ee(t);(n?u--:++u<i)&&r(a[u],u,a)!==!1;);return t}}function Mu(e){return function(n,t,r){for(var i=-1,u=ee(n),a=r(n),l=a.length;l--;){var p=a[e?l:++i];if(t(u[p],p,u)===!1)break}return n}}function oc(e,n,t){var r=n&j,i=Pt(e);function u(){var a=this&&this!==pe&&this instanceof u?i:e;return a.apply(r?t:this,arguments)}return u}function Bu(e){return function(n){n=X(n);var t=zn(n)?ke(n):s,r=t?t[0]:n.charAt(0),i=t?In(t,1).join(""):n.slice(1);return r[e]()+i}}function nt(e){return function(n){return Qr(Us(Ts(n).replace(Za,"")),e,"")}}function Pt(e){return function(){var n=arguments;switch(n.length){case 0:return new e;case 1:return new e(n[0]);case 2:return new e(n[0],n[1]);case 3:return new e(n[0],n[1],n[2]);case 4:return new e(n[0],n[1],n[2],n[3]);case 5:return new e(n[0],n[1],n[2],n[3],n[4]);case 6:return new e(n[0],n[1],n[2],n[3],n[4],n[5]);case 7:return new e(n[0],n[1],n[2],n[3],n[4],n[5],n[6])}var t=jn(e.prototype),r=e.apply(t,n);return oe(r)?r:t}}function uc(e,n,t){var r=Pt(e);function i(){for(var u=arguments.length,a=g(u),l=u,p=tt(i);l--;)a[l]=arguments[l];var _=u<3&&a[0]!==p&&a[u-1]!==p?[]:Sn(a,p);if(u-=_.length,u<t)return Vu(e,n,dr,i.placeholder,s,a,_,s,s,t-u);var v=this&&this!==pe&&this instanceof i?r:e;return De(v,this,a)}return i}function Fu(e){return function(n,t,r){var i=ee(n);if(!xe(n)){var u=L(t,3);n=ce(n),t=function(l){return u(i[l],l,i)}}var a=e(n,t,r);return a>-1?i[u?n[a]:a]:s}}function Nu(e){return cn(function(n){var t=n.length,r=t,i=Fe.prototype.thru;for(e&&n.reverse();r--;){var u=n[r];if(typeof u!="function")throw new Be(P);if(i&&!a&&_r(u)=="wrapper")var a=new Fe([],!0)}for(r=a?r:t;++r<t;){u=n[r];var l=_r(u),p=l=="wrapper"?Ei(u):s;p&&Bi(p[0])&&p[1]==(qe|Pe|Z|Vn)&&!p[4].length&&p[9]==1?a=a[_r(p[0])].apply(a,p[3]):a=u.length==1&&Bi(u)?a[l]():a.thru(u)}return function(){var _=arguments,v=_[0];if(a&&_.length==1&&B(v))return a.plant(v).value();for(var w=0,C=t?n[w].apply(this,_):v;++w<t;)C=n[w].call(this,C);return C}})}function dr(e,n,t,r,i,u,a,l,p,_){var v=n&qe,w=n&j,C=n&Je,b=n&(Pe|on),R=n&at,N=C?s:Pt(e);function O(){for(var V=arguments.length,H=g(V),Ee=V;Ee--;)H[Ee]=arguments[Ee];if(b)var ye=tt(O),Te=vl(H,ye);if(r&&(H=Eu(H,r,i,b)),u&&(H=Tu(H,u,a,b)),V-=Te,b&&V<_){var ae=Sn(H,ye);return Vu(e,n,dr,O.placeholder,t,H,ae,l,p,_-V)}var Ye=w?t:this,hn=C?Ye[e]:e;return V=H.length,l?H=bc(H,l):R&&V>1&&H.reverse(),v&&p<V&&(H.length=p),this&&this!==pe&&this instanceof O&&(hn=N||Pt(hn)),hn.apply(Ye,H)}return O}function Wu(e,n){return function(t,r){return Uf(t,e,n(r),{})}}function pr(e,n){return function(t,r){var i;if(t===s&&r===s)return n;if(t!==s&&(i=t),r!==s){if(i===s)return r;typeof t=="string"||typeof r=="string"?(t=Re(t),r=Re(r)):(t=Au(t),r=Au(r)),i=e(t,r)}return i}}function Di(e){return cn(function(n){return n=ie(n,Le(L())),W(function(t){var r=this;return e(n,function(i){return De(i,r,t)})})})}function gr(e,n){n=n===s?" ":Re(n);var t=n.length;if(t<2)return t?yi(n,e):n;var r=yi(n,er(e/Zn(n)));return zn(n)?In(ke(r),0,e).join(""):r.slice(0,e)}function sc(e,n,t,r){var i=n&j,u=Pt(e);function a(){for(var l=-1,p=arguments.length,_=-1,v=r.length,w=g(v+p),C=this&&this!==pe&&this instanceof a?u:e;++_<v;)w[_]=r[_];for(;p--;)w[_++]=arguments[++l];return De(C,i?t:this,w)}return a}function Gu(e){return function(n,t,r){return r&&typeof r!="number"&&Se(n,t,r)&&(t=r=s),n=gn(n),t===s?(t=n,n=0):t=gn(t),r=r===s?n<t?1:-1:gn(r),kf(n,t,r,e)}}function hr(e){return function(n,t){return typeof n=="string"&&typeof t=="string"||(n=Ve(n),t=Ve(t)),e(n,t)}}function Vu(e,n,t,r,i,u,a,l,p,_){var v=n&Pe,w=v?a:s,C=v?s:a,b=v?u:s,R=v?s:u;n|=v?Z:vn,n&=~(v?vn:Z),n&rn||(n&=-4);var N=[e,n,i,b,w,R,C,l,p,_],O=t.apply(s,N);return Bi(e)&&ju(O,N),O.placeholder=r,es(O,e,n)}function Li(e){var n=le[e];return function(t,r){if(t=Ve(t),r=r==null?0:ve(F(r),292),r&&eu(t)){var i=(X(t)+"e").split("e"),u=n(i[0]+"e"+(+i[1]+r));return i=(X(u)+"e").split("e"),+(i[0]+"e"+(+i[1]-r))}return n(t)}}var ac=Jn&&1/Kt(new Jn([,-0]))[1]==Tt?function(e){return new Jn(e)}:Ji;function $u(e){return function(n){var t=me(n);return t==Ke?oi(n):t==He?Al(n):_l(n,e(n))}}function fn(e,n,t,r,i,u,a,l){var p=n&Je;if(!p&&typeof e!="function")throw new Be(P);var _=r?r.length:0;if(_||(n&=-97,r=i=s),a=a===s?a:fe(F(a),0),l=l===s?l:F(l),_-=i?i.length:0,n&vn){var v=r,w=i;r=i=s}var C=p?s:Ei(e),b=[e,n,t,r,i,v,w,u,a,l];if(C&&xc(b,C),e=b[0],n=b[1],t=b[2],r=b[3],i=b[4],l=b[9]=b[9]===s?p?0:e.length:fe(b[9]-_,0),!l&&n&(Pe|on)&&(n&=-25),!n||n==j)var R=oc(e,n,t);else n==Pe||n==on?R=uc(e,n,l):(n==Z||n==(j|Z))&&!i.length?R=sc(e,n,t,r):R=dr.apply(s,b);var N=C?Cu:ju;return es(N(R,b),e,n)}function qu(e,n,t,r){return e===s||Ze(e,Xn[t])&&!J.call(r,t)?n:e}function Ku(e,n,t,r,i,u){return oe(e)&&oe(n)&&(u.set(n,e),ar(e,n,s,Ku,u),u.delete(n)),e}function lc(e){return Rt(e)?s:e}function Hu(e,n,t,r,i,u){var a=t&T,l=e.length,p=n.length;if(l!=p&&!(a&&p>l))return!1;var _=u.get(e),v=u.get(n);if(_&&v)return _==n&&v==e;var w=-1,C=!0,b=t&k?new Tn:s;for(u.set(e,n),u.set(n,e);++w<l;){var R=e[w],N=n[w];if(r)var O=a?r(N,R,w,n,e,u):r(R,N,w,e,n,u);if(O!==s){if(O)continue;C=!1;break}if(b){if(!jr(n,function(V,H){if(!_t(b,H)&&(R===V||i(R,V,t,r,u)))return b.push(H)})){C=!1;break}}else if(!(R===N||i(R,N,t,r,u))){C=!1;break}}return u.delete(e),u.delete(n),C}function fc(e,n,t,r,i,u,a){switch(t){case Kn:if(e.byteLength!=n.byteLength||e.byteOffset!=n.byteOffset)return!1;e=e.buffer,n=n.buffer;case ht:return!(e.byteLength!=n.byteLength||!u(new Xt(e),new Xt(n)));case lt:case ft:case ct:return Ze(+e,+n);case Bt:return e.name==n.name&&e.message==n.message;case dt:case pt:return e==n+"";case Ke:var l=oi;case He:var p=r&T;if(l||(l=Kt),e.size!=n.size&&!p)return!1;var _=a.get(e);if(_)return _==n;r|=k,a.set(e,n);var v=Hu(l(e),l(n),r,i,u,a);return a.delete(e),v;case Nt:if(yt)return yt.call(e)==yt.call(n)}return!1}function cc(e,n,t,r,i,u){var a=t&T,l=Ri(e),p=l.length,_=Ri(n),v=_.length;if(p!=v&&!a)return!1;for(var w=p;w--;){var C=l[w];if(!(a?C in n:J.call(n,C)))return!1}var b=u.get(e),R=u.get(n);if(b&&R)return b==n&&R==e;var N=!0;u.set(e,n),u.set(n,e);for(var O=a;++w<p;){C=l[w];var V=e[C],H=n[C];if(r)var Ee=a?r(H,V,C,n,e,u):r(V,H,C,e,n,u);if(!(Ee===s?V===H||i(V,H,t,r,u):Ee)){N=!1;break}O||(O=C=="constructor")}if(N&&!O){var ye=e.constructor,Te=n.constructor;ye!=Te&&"constructor"in e&&"constructor"in n&&!(typeof ye=="function"&&ye instanceof ye&&typeof Te=="function"&&Te instanceof Te)&&(N=!1)}return u.delete(e),u.delete(n),N}function cn(e){return Ni(Ju(e,s,us),e+"")}function Ri(e){return cu(e,ce,Ui)}function Oi(e){return cu(e,Ae,ku)}var Ei=tr?function(e){return tr.get(e)}:Ji;function _r(e){for(var n=e.name+"",t=Qn[n],r=J.call(Qn,n)?t.length:0;r--;){var i=t[r],u=i.func;if(u==null||u==e)return i.name}return n}function tt(e){var n=J.call(o,"placeholder")?o:e;return n.placeholder}function L(){var e=o.iteratee||Yi;return e=e===Yi?gu:e,arguments.length?e(arguments[0],arguments[1]):e}function vr(e,n){var t=e.__data__;return wc(n)?t[typeof n=="string"?"string":"hash"]:t.map}function Ti(e){for(var n=ce(e),t=n.length;t--;){var r=n[t],i=e[r];n[t]=[r,i,Yu(i)]}return n}function Bn(e,n){var t=yl(e,n);return pu(t)?t:s}function dc(e){var n=J.call(e,On),t=e[On];try{e[On]=s;var r=!0}catch{}var i=Zt.call(e);return r&&(n?e[On]=t:delete e[On]),i}var Ui=si?function(e){return e==null?[]:(e=ee(e),mn(si(e),function(n){return Qo.call(e,n)}))}:Qi,ku=si?function(e){for(var n=[];e;)wn(n,Ui(e)),e=Jt(e);return n}:Qi,me=we;(ai&&me(new ai(new ArrayBuffer(1)))!=Kn||mt&&me(new mt)!=Ke||li&&me(li.resolve())!=so||Jn&&me(new Jn)!=He||wt&&me(new wt)!=gt)&&(me=function(e){var n=we(e),t=n==un?e.constructor:s,r=t?Fn(t):"";if(r)switch(r){case zl:return Kn;case Zl:return Ke;case Yl:return so;case Xl:return He;case Jl:return gt}return n});function pc(e,n,t){for(var r=-1,i=t.length;++r<i;){var u=t[r],a=u.size;switch(u.type){case"drop":e+=a;break;case"dropRight":n-=a;break;case"take":n=ve(n,e+a);break;case"takeRight":e=fe(e,n-a);break}}return{start:e,end:n}}function gc(e){var n=e.match(wa);return n?n[1].split(Sa):[]}function zu(e,n,t){n=An(n,e);for(var r=-1,i=n.length,u=!1;++r<i;){var a=nn(n[r]);if(!(u=e!=null&&t(e,a)))break;e=e[a]}return u||++r!=i?u:(i=e==null?0:e.length,!!i&&Ar(i)&&dn(a,i)&&(B(e)||Nn(e)))}function hc(e){var n=e.length,t=new e.constructor(n);return n&&typeof e[0]=="string"&&J.call(e,"index")&&(t.index=e.index,t.input=e.input),t}function Zu(e){return typeof e.constructor=="function"&&!Dt(e)?jn(Jt(e)):{}}function _c(e,n,t){var r=e.constructor;switch(n){case ht:return Pi(e);case lt:case ft:return new r(+e);case Kn:return jf(e,t);case Tr:case Ur:case Mr:case Br:case Fr:case Nr:case Wr:case Gr:case Vr:return Ru(e,t);case Ke:return new r;case ct:case pt:return new r(e);case dt:return ec(e);case He:return new r;case Nt:return nc(e)}}function vc(e,n){var t=n.length;if(!t)return e;var r=t-1;return n[r]=(t>1?"& ":"")+n[r],n=n.join(t>2?", ":" "),e.replace(ma,`{
/* [wrapped with `+n+`] */
`)}function mc(e){return B(e)||Nn(e)||!!(jo&&e&&e[jo])}function dn(e,n){var t=typeof e;return n=n??$n,!!n&&(t=="number"||t!="symbol"&&La.test(e))&&e>-1&&e%1==0&&e<n}function Se(e,n,t){if(!oe(t))return!1;var r=typeof n;return(r=="number"?xe(t)&&dn(n,t.length):r=="string"&&n in t)?Ze(t[n],e):!1}function Mi(e,n){if(B(e))return!1;var t=typeof e;return t=="number"||t=="symbol"||t=="boolean"||e==null||Oe(e)?!0:ga.test(e)||!pa.test(e)||n!=null&&e in ee(n)}function wc(e){var n=typeof e;return n=="string"||n=="number"||n=="symbol"||n=="boolean"?e!=="__proto__":e===null}function Bi(e){var n=_r(e),t=o[n];if(typeof t!="function"||!(n in q.prototype))return!1;if(e===t)return!0;var r=Ei(t);return!!r&&e===r[0]}function Sc(e){return!!Yo&&Yo in e}var yc=kt?pn:ji;function Dt(e){var n=e&&e.constructor,t=typeof n=="function"&&n.prototype||Xn;return e===t}function Yu(e){return e===e&&!oe(e)}function Xu(e,n){return function(t){return t==null?!1:t[e]===n&&(n!==s||e in ee(t))}}function Cc(e){var n=Cr(e,function(r){return t.size===z&&t.clear(),r}),t=n.cache;return n}function xc(e,n){var t=e[1],r=n[1],i=t|r,u=i<(j|Je|qe),a=r==qe&&t==Pe||r==qe&&t==Vn&&e[7].length<=n[8]||r==(qe|Vn)&&n[7].length<=n[8]&&t==Pe;if(!(u||a))return e;r&j&&(e[2]=n[2],i|=t&j?0:rn);var l=n[3];if(l){var p=e[3];e[3]=p?Eu(p,l,n[4]):l,e[4]=p?Sn(e[3],E):n[4]}return l=n[5],l&&(p=e[5],e[5]=p?Tu(p,l,n[6]):l,e[6]=p?Sn(e[5],E):n[6]),l=n[7],l&&(e[7]=l),r&qe&&(e[8]=e[8]==null?n[8]:ve(e[8],n[8])),e[9]==null&&(e[9]=n[9]),e[0]=n[0],e[1]=i,e}function Ac(e){var n=[];if(e!=null)for(var t in ee(e))n.push(t);return n}function Ic(e){return Zt.call(e)}function Ju(e,n,t){return n=fe(n===s?e.length-1:n,0),function(){for(var r=arguments,i=-1,u=fe(r.length-n,0),a=g(u);++i<u;)a[i]=r[n+i];i=-1;for(var l=g(n+1);++i<n;)l[i]=r[i];return l[n]=t(a),De(e,this,l)}}function Qu(e,n){return n.length<2?e:Mn(e,We(n,0,-1))}function bc(e,n){for(var t=e.length,r=ve(n.length,t),i=Ce(e);r--;){var u=n[r];e[r]=dn(u,t)?i[u]:s}return e}function Fi(e,n){if(!(n==="constructor"&&typeof e[n]=="function")&&n!="__proto__")return e[n]}var ju=ns(Cu),Lt=Gl||function(e,n){return pe.setTimeout(e,n)},Ni=ns(Yf);function es(e,n,t){var r=n+"";return Ni(e,vc(r,Pc(gc(r),t)))}function ns(e){var n=0,t=0;return function(){var r=Kl(),i=zs-(r-t);if(t=r,i>0){if(++n>=Ln)return arguments[0]}else n=0;return e.apply(s,arguments)}}function mr(e,n){var t=-1,r=e.length,i=r-1;for(n=n===s?r:n;++t<n;){var u=Si(t,i),a=e[u];e[u]=e[t],e[t]=a}return e.length=n,e}var ts=Cc(function(e){var n=[];return e.charCodeAt(0)===46&&n.push(""),e.replace(ha,function(t,r,i,u){n.push(i?u.replace(xa,"$1"):r||t)}),n});function nn(e){if(typeof e=="string"||Oe(e))return e;var n=e+"";return n=="0"&&1/e==-1/0?"-0":n}function Fn(e){if(e!=null){try{return zt.call(e)}catch{}try{return e+""}catch{}}return""}function Pc(e,n){return Me(js,function(t){var r="_."+t[0];n&t[1]&&!$t(e,r)&&e.push(r)}),e.sort()}function rs(e){if(e instanceof q)return e.clone();var n=new Fe(e.__wrapped__,e.__chain__);return n.__actions__=Ce(e.__actions__),n.__index__=e.__index__,n.__values__=e.__values__,n}function Dc(e,n,t){(t?Se(e,n,t):n===s)?n=1:n=fe(F(n),0);var r=e==null?0:e.length;if(!r||n<1)return[];for(var i=0,u=0,a=g(er(r/n));i<r;)a[u++]=We(e,i,i+=n);return a}function Lc(e){for(var n=-1,t=e==null?0:e.length,r=0,i=[];++n<t;){var u=e[n];u&&(i[r++]=u)}return i}function Rc(){var e=arguments.length;if(!e)return[];for(var n=g(e-1),t=arguments[0],r=e;r--;)n[r-1]=arguments[r];return wn(B(t)?Ce(t):[t],ge(n,1))}var Oc=W(function(e,n){return se(e)?xt(e,ge(n,1,se,!0)):[]}),Ec=W(function(e,n){var t=Ge(n);return se(t)&&(t=s),se(e)?xt(e,ge(n,1,se,!0),L(t,2)):[]}),Tc=W(function(e,n){var t=Ge(n);return se(t)&&(t=s),se(e)?xt(e,ge(n,1,se,!0),s,t):[]});function Uc(e,n,t){var r=e==null?0:e.length;return r?(n=t||n===s?1:F(n),We(e,n<0?0:n,r)):[]}function Mc(e,n,t){var r=e==null?0:e.length;return r?(n=t||n===s?1:F(n),n=r-n,We(e,0,n<0?0:n)):[]}function Bc(e,n){return e&&e.length?fr(e,L(n,3),!0,!0):[]}function Fc(e,n){return e&&e.length?fr(e,L(n,3),!0):[]}function Nc(e,n,t,r){var i=e==null?0:e.length;return i?(t&&typeof t!="number"&&Se(e,n,t)&&(t=0,r=i),Rf(e,n,t,r)):[]}function is(e,n,t){var r=e==null?0:e.length;if(!r)return-1;var i=t==null?0:F(t);return i<0&&(i=fe(r+i,0)),qt(e,L(n,3),i)}function os(e,n,t){var r=e==null?0:e.length;if(!r)return-1;var i=r-1;return t!==s&&(i=F(t),i=t<0?fe(r+i,0):ve(i,r-1)),qt(e,L(n,3),i,!0)}function us(e){var n=e==null?0:e.length;return n?ge(e,1):[]}function Wc(e){var n=e==null?0:e.length;return n?ge(e,Tt):[]}function Gc(e,n){var t=e==null?0:e.length;return t?(n=n===s?1:F(n),ge(e,n)):[]}function Vc(e){for(var n=-1,t=e==null?0:e.length,r={};++n<t;){var i=e[n];r[i[0]]=i[1]}return r}function ss(e){return e&&e.length?e[0]:s}function $c(e,n,t){var r=e==null?0:e.length;if(!r)return-1;var i=t==null?0:F(t);return i<0&&(i=fe(r+i,0)),kn(e,n,i)}function qc(e){var n=e==null?0:e.length;return n?We(e,0,-1):[]}var Kc=W(function(e){var n=ie(e,Ii);return n.length&&n[0]===e[0]?hi(n):[]}),Hc=W(function(e){var n=Ge(e),t=ie(e,Ii);return n===Ge(t)?n=s:t.pop(),t.length&&t[0]===e[0]?hi(t,L(n,2)):[]}),kc=W(function(e){var n=Ge(e),t=ie(e,Ii);return n=typeof n=="function"?n:s,n&&t.pop(),t.length&&t[0]===e[0]?hi(t,s,n):[]});function zc(e,n){return e==null?"":$l.call(e,n)}function Ge(e){var n=e==null?0:e.length;return n?e[n-1]:s}function Zc(e,n,t){var r=e==null?0:e.length;if(!r)return-1;var i=r;return t!==s&&(i=F(t),i=i<0?fe(r+i,0):ve(i,r-1)),n===n?bl(e,n,i):qt(e,Vo,i,!0)}function Yc(e,n){return e&&e.length?mu(e,F(n)):s}var Xc=W(as);function as(e,n){return e&&e.length&&n&&n.length?wi(e,n):e}function Jc(e,n,t){return e&&e.length&&n&&n.length?wi(e,n,L(t,2)):e}function Qc(e,n,t){return e&&e.length&&n&&n.length?wi(e,n,s,t):e}var jc=cn(function(e,n){var t=e==null?0:e.length,r=ci(e,n);return yu(e,ie(n,function(i){return dn(i,t)?+i:i}).sort(Ou)),r});function ed(e,n){var t=[];if(!(e&&e.length))return t;var r=-1,i=[],u=e.length;for(n=L(n,3);++r<u;){var a=e[r];n(a,r,e)&&(t.push(a),i.push(r))}return yu(e,i),t}function Wi(e){return e==null?e:kl.call(e)}function nd(e,n,t){var r=e==null?0:e.length;return r?(t&&typeof t!="number"&&Se(e,n,t)?(n=0,t=r):(n=n==null?0:F(n),t=t===s?r:F(t)),We(e,n,t)):[]}function td(e,n){return lr(e,n)}function rd(e,n,t){return Ci(e,n,L(t,2))}function id(e,n){var t=e==null?0:e.length;if(t){var r=lr(e,n);if(r<t&&Ze(e[r],n))return r}return-1}function od(e,n){return lr(e,n,!0)}function ud(e,n,t){return Ci(e,n,L(t,2),!0)}function sd(e,n){var t=e==null?0:e.length;if(t){var r=lr(e,n,!0)-1;if(Ze(e[r],n))return r}return-1}function ad(e){return e&&e.length?xu(e):[]}function ld(e,n){return e&&e.length?xu(e,L(n,2)):[]}function fd(e){var n=e==null?0:e.length;return n?We(e,1,n):[]}function cd(e,n,t){return e&&e.length?(n=t||n===s?1:F(n),We(e,0,n<0?0:n)):[]}function dd(e,n,t){var r=e==null?0:e.length;return r?(n=t||n===s?1:F(n),n=r-n,We(e,n<0?0:n,r)):[]}function pd(e,n){return e&&e.length?fr(e,L(n,3),!1,!0):[]}function gd(e,n){return e&&e.length?fr(e,L(n,3)):[]}var hd=W(function(e){return xn(ge(e,1,se,!0))}),_d=W(function(e){var n=Ge(e);return se(n)&&(n=s),xn(ge(e,1,se,!0),L(n,2))}),vd=W(function(e){var n=Ge(e);return n=typeof n=="function"?n:s,xn(ge(e,1,se,!0),s,n)});function md(e){return e&&e.length?xn(e):[]}function wd(e,n){return e&&e.length?xn(e,L(n,2)):[]}function Sd(e,n){return n=typeof n=="function"?n:s,e&&e.length?xn(e,s,n):[]}function Gi(e){if(!(e&&e.length))return[];var n=0;return e=mn(e,function(t){if(se(t))return n=fe(t.length,n),!0}),ri(n,function(t){return ie(e,ei(t))})}function ls(e,n){if(!(e&&e.length))return[];var t=Gi(e);return n==null?t:ie(t,function(r){return De(n,s,r)})}var yd=W(function(e,n){return se(e)?xt(e,n):[]}),Cd=W(function(e){return Ai(mn(e,se))}),xd=W(function(e){var n=Ge(e);return se(n)&&(n=s),Ai(mn(e,se),L(n,2))}),Ad=W(function(e){var n=Ge(e);return n=typeof n=="function"?n:s,Ai(mn(e,se),s,n)}),Id=W(Gi);function bd(e,n){return Pu(e||[],n||[],Ct)}function Pd(e,n){return Pu(e||[],n||[],bt)}var Dd=W(function(e){var n=e.length,t=n>1?e[n-1]:s;return t=typeof t=="function"?(e.pop(),t):s,ls(e,t)});function fs(e){var n=o(e);return n.__chain__=!0,n}function Ld(e,n){return n(e),e}function wr(e,n){return n(e)}var Rd=cn(function(e){var n=e.length,t=n?e[0]:0,r=this.__wrapped__,i=function(u){return ci(u,e)};return n>1||this.__actions__.length||!(r instanceof q)||!dn(t)?this.thru(i):(r=r.slice(t,+t+(n?1:0)),r.__actions__.push({func:wr,args:[i],thisArg:s}),new Fe(r,this.__chain__).thru(function(u){return n&&!u.length&&u.push(s),u}))});function Od(){return fs(this)}function Ed(){return new Fe(this.value(),this.__chain__)}function Td(){this.__values__===s&&(this.__values__=As(this.value()));var e=this.__index__>=this.__values__.length,n=e?s:this.__values__[this.__index__++];return{done:e,value:n}}function Ud(){return this}function Md(e){for(var n,t=this;t instanceof ir;){var r=rs(t);r.__index__=0,r.__values__=s,n?i.__wrapped__=r:n=r;var i=r;t=t.__wrapped__}return i.__wrapped__=e,n}function Bd(){var e=this.__wrapped__;if(e instanceof q){var n=e;return this.__actions__.length&&(n=new q(this)),n=n.reverse(),n.__actions__.push({func:wr,args:[Wi],thisArg:s}),new Fe(n,this.__chain__)}return this.thru(Wi)}function Fd(){return bu(this.__wrapped__,this.__actions__)}var Nd=cr(function(e,n,t){J.call(e,t)?++e[t]:ln(e,t,1)});function Wd(e,n,t){var r=B(e)?Wo:Lf;return t&&Se(e,n,t)&&(n=s),r(e,L(n,3))}function Gd(e,n){var t=B(e)?mn:lu;return t(e,L(n,3))}var Vd=Fu(is),$d=Fu(os);function qd(e,n){return ge(Sr(e,n),1)}function Kd(e,n){return ge(Sr(e,n),Tt)}function Hd(e,n,t){return t=t===s?1:F(t),ge(Sr(e,n),t)}function cs(e,n){var t=B(e)?Me:Cn;return t(e,L(n,3))}function ds(e,n){var t=B(e)?ll:au;return t(e,L(n,3))}var kd=cr(function(e,n,t){J.call(e,t)?e[t].push(n):ln(e,t,[n])});function zd(e,n,t,r){e=xe(e)?e:it(e),t=t&&!r?F(t):0;var i=e.length;return t<0&&(t=fe(i+t,0)),Ir(e)?t<=i&&e.indexOf(n,t)>-1:!!i&&kn(e,n,t)>-1}var Zd=W(function(e,n,t){var r=-1,i=typeof n=="function",u=xe(e)?g(e.length):[];return Cn(e,function(a){u[++r]=i?De(n,a,t):At(a,n,t)}),u}),Yd=cr(function(e,n,t){ln(e,t,n)});function Sr(e,n){var t=B(e)?ie:hu;return t(e,L(n,3))}function Xd(e,n,t,r){return e==null?[]:(B(n)||(n=n==null?[]:[n]),t=r?s:t,B(t)||(t=t==null?[]:[t]),wu(e,n,t))}var Jd=cr(function(e,n,t){e[t?0:1].push(n)},function(){return[[],[]]});function Qd(e,n,t){var r=B(e)?Qr:qo,i=arguments.length<3;return r(e,L(n,4),t,i,Cn)}function jd(e,n,t){var r=B(e)?fl:qo,i=arguments.length<3;return r(e,L(n,4),t,i,au)}function ep(e,n){var t=B(e)?mn:lu;return t(e,xr(L(n,3)))}function np(e){var n=B(e)?iu:zf;return n(e)}function tp(e,n,t){(t?Se(e,n,t):n===s)?n=1:n=F(n);var r=B(e)?Af:Zf;return r(e,n)}function rp(e){var n=B(e)?If:Xf;return n(e)}function ip(e){if(e==null)return 0;if(xe(e))return Ir(e)?Zn(e):e.length;var n=me(e);return n==Ke||n==He?e.size:vi(e).length}function op(e,n,t){var r=B(e)?jr:Jf;return t&&Se(e,n,t)&&(n=s),r(e,L(n,3))}var up=W(function(e,n){if(e==null)return[];var t=n.length;return t>1&&Se(e,n[0],n[1])?n=[]:t>2&&Se(n[0],n[1],n[2])&&(n=[n[0]]),wu(e,ge(n,1),[])}),yr=Wl||function(){return pe.Date.now()};function sp(e,n){if(typeof n!="function")throw new Be(P);return e=F(e),function(){if(--e<1)return n.apply(this,arguments)}}function ps(e,n,t){return n=t?s:n,n=e&&n==null?e.length:n,fn(e,qe,s,s,s,s,n)}function gs(e,n){var t;if(typeof n!="function")throw new Be(P);return e=F(e),function(){return--e>0&&(t=n.apply(this,arguments)),e<=1&&(n=s),t}}var Vi=W(function(e,n,t){var r=j;if(t.length){var i=Sn(t,tt(Vi));r|=Z}return fn(e,r,n,t,i)}),hs=W(function(e,n,t){var r=j|Je;if(t.length){var i=Sn(t,tt(hs));r|=Z}return fn(n,r,e,t,i)});function _s(e,n,t){n=t?s:n;var r=fn(e,Pe,s,s,s,s,s,n);return r.placeholder=_s.placeholder,r}function vs(e,n,t){n=t?s:n;var r=fn(e,on,s,s,s,s,s,n);return r.placeholder=vs.placeholder,r}function ms(e,n,t){var r,i,u,a,l,p,_=0,v=!1,w=!1,C=!0;if(typeof e!="function")throw new Be(P);n=Ve(n)||0,oe(t)&&(v=!!t.leading,w="maxWait"in t,u=w?fe(Ve(t.maxWait)||0,n):u,C="trailing"in t?!!t.trailing:C);function b(ae){var Ye=r,hn=i;return r=i=s,_=ae,a=e.apply(hn,Ye),a}function R(ae){return _=ae,l=Lt(V,n),v?b(ae):a}function N(ae){var Ye=ae-p,hn=ae-_,Fs=n-Ye;return w?ve(Fs,u-hn):Fs}function O(ae){var Ye=ae-p,hn=ae-_;return p===s||Ye>=n||Ye<0||w&&hn>=u}function V(){var ae=yr();if(O(ae))return H(ae);l=Lt(V,N(ae))}function H(ae){return l=s,C&&r?b(ae):(r=i=s,a)}function Ee(){l!==s&&Du(l),_=0,r=p=i=l=s}function ye(){return l===s?a:H(yr())}function Te(){var ae=yr(),Ye=O(ae);if(r=arguments,i=this,p=ae,Ye){if(l===s)return R(p);if(w)return Du(l),l=Lt(V,n),b(p)}return l===s&&(l=Lt(V,n)),a}return Te.cancel=Ee,Te.flush=ye,Te}var ap=W(function(e,n){return su(e,1,n)}),lp=W(function(e,n,t){return su(e,Ve(n)||0,t)});function fp(e){return fn(e,at)}function Cr(e,n){if(typeof e!="function"||n!=null&&typeof n!="function")throw new Be(P);var t=function(){var r=arguments,i=n?n.apply(this,r):r[0],u=t.cache;if(u.has(i))return u.get(i);var a=e.apply(this,r);return t.cache=u.set(i,a)||u,a};return t.cache=new(Cr.Cache||an),t}Cr.Cache=an;function xr(e){if(typeof e!="function")throw new Be(P);return function(){var n=arguments;switch(n.length){case 0:return!e.call(this);case 1:return!e.call(this,n[0]);case 2:return!e.call(this,n[0],n[1]);case 3:return!e.call(this,n[0],n[1],n[2])}return!e.apply(this,n)}}function cp(e){return gs(2,e)}var dp=Qf(function(e,n){n=n.length==1&&B(n[0])?ie(n[0],Le(L())):ie(ge(n,1),Le(L()));var t=n.length;return W(function(r){for(var i=-1,u=ve(r.length,t);++i<u;)r[i]=n[i].call(this,r[i]);return De(e,this,r)})}),$i=W(function(e,n){var t=Sn(n,tt($i));return fn(e,Z,s,n,t)}),ws=W(function(e,n){var t=Sn(n,tt(ws));return fn(e,vn,s,n,t)}),pp=cn(function(e,n){return fn(e,Vn,s,s,s,n)});function gp(e,n){if(typeof e!="function")throw new Be(P);return n=n===s?n:F(n),W(e,n)}function hp(e,n){if(typeof e!="function")throw new Be(P);return n=n==null?0:fe(F(n),0),W(function(t){var r=t[n],i=In(t,0,n);return r&&wn(i,r),De(e,this,i)})}function _p(e,n,t){var r=!0,i=!0;if(typeof e!="function")throw new Be(P);return oe(t)&&(r="leading"in t?!!t.leading:r,i="trailing"in t?!!t.trailing:i),ms(e,n,{leading:r,maxWait:n,trailing:i})}function vp(e){return ps(e,1)}function mp(e,n){return $i(bi(n),e)}function wp(){if(!arguments.length)return[];var e=arguments[0];return B(e)?e:[e]}function Sp(e){return Ne(e,G)}function yp(e,n){return n=typeof n=="function"?n:s,Ne(e,G,n)}function Cp(e){return Ne(e,Q|G)}function xp(e,n){return n=typeof n=="function"?n:s,Ne(e,Q|G,n)}function Ap(e,n){return n==null||uu(e,n,ce(n))}function Ze(e,n){return e===n||e!==e&&n!==n}var Ip=hr(gi),bp=hr(function(e,n){return e>=n}),Nn=du(function(){return arguments}())?du:function(e){return ue(e)&&J.call(e,"callee")&&!Qo.call(e,"callee")},B=g.isArray,Pp=To?Le(To):Mf;function xe(e){return e!=null&&Ar(e.length)&&!pn(e)}function se(e){return ue(e)&&xe(e)}function Dp(e){return e===!0||e===!1||ue(e)&&we(e)==lt}var bn=Vl||ji,Lp=Uo?Le(Uo):Bf;function Rp(e){return ue(e)&&e.nodeType===1&&!Rt(e)}function Op(e){if(e==null)return!0;if(xe(e)&&(B(e)||typeof e=="string"||typeof e.splice=="function"||bn(e)||rt(e)||Nn(e)))return!e.length;var n=me(e);if(n==Ke||n==He)return!e.size;if(Dt(e))return!vi(e).length;for(var t in e)if(J.call(e,t))return!1;return!0}function Ep(e,n){return It(e,n)}function Tp(e,n,t){t=typeof t=="function"?t:s;var r=t?t(e,n):s;return r===s?It(e,n,s,t):!!r}function qi(e){if(!ue(e))return!1;var n=we(e);return n==Bt||n==na||typeof e.message=="string"&&typeof e.name=="string"&&!Rt(e)}function Up(e){return typeof e=="number"&&eu(e)}function pn(e){if(!oe(e))return!1;var n=we(e);return n==Ft||n==uo||n==ea||n==ra}function Ss(e){return typeof e=="number"&&e==F(e)}function Ar(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=$n}function oe(e){var n=typeof e;return e!=null&&(n=="object"||n=="function")}function ue(e){return e!=null&&typeof e=="object"}var ys=Mo?Le(Mo):Nf;function Mp(e,n){return e===n||_i(e,n,Ti(n))}function Bp(e,n,t){return t=typeof t=="function"?t:s,_i(e,n,Ti(n),t)}function Fp(e){return Cs(e)&&e!=+e}function Np(e){if(yc(e))throw new M(I);return pu(e)}function Wp(e){return e===null}function Gp(e){return e==null}function Cs(e){return typeof e=="number"||ue(e)&&we(e)==ct}function Rt(e){if(!ue(e)||we(e)!=un)return!1;var n=Jt(e);if(n===null)return!0;var t=J.call(n,"constructor")&&n.constructor;return typeof t=="function"&&t instanceof t&&zt.call(t)==Ml}var Ki=Bo?Le(Bo):Wf;function Vp(e){return Ss(e)&&e>=-9007199254740991&&e<=$n}var xs=Fo?Le(Fo):Gf;function Ir(e){return typeof e=="string"||!B(e)&&ue(e)&&we(e)==pt}function Oe(e){return typeof e=="symbol"||ue(e)&&we(e)==Nt}var rt=No?Le(No):Vf;function $p(e){return e===s}function qp(e){return ue(e)&&me(e)==gt}function Kp(e){return ue(e)&&we(e)==oa}var Hp=hr(mi),kp=hr(function(e,n){return e<=n});function As(e){if(!e)return[];if(xe(e))return Ir(e)?ke(e):Ce(e);if(vt&&e[vt])return xl(e[vt]());var n=me(e),t=n==Ke?oi:n==He?Kt:it;return t(e)}function gn(e){if(!e)return e===0?e:0;if(e=Ve(e),e===Tt||e===-1/0){var n=e<0?-1:1;return n*Xs}return e===e?e:0}function F(e){var n=gn(e),t=n%1;return n===n?t?n-t:n:0}function Is(e){return e?Un(F(e),0,Qe):0}function Ve(e){if(typeof e=="number")return e;if(Oe(e))return Ut;if(oe(e)){var n=typeof e.valueOf=="function"?e.valueOf():e;e=oe(n)?n+"":n}if(typeof e!="string")return e===0?e:+e;e=Ko(e);var t=ba.test(e);return t||Da.test(e)?ul(e.slice(2),t?2:8):Ia.test(e)?Ut:+e}function bs(e){return en(e,Ae(e))}function zp(e){return e?Un(F(e),-9007199254740991,$n):e===0?e:0}function X(e){return e==null?"":Re(e)}var Zp=et(function(e,n){if(Dt(n)||xe(n)){en(n,ce(n),e);return}for(var t in n)J.call(n,t)&&Ct(e,t,n[t])}),Ps=et(function(e,n){en(n,Ae(n),e)}),br=et(function(e,n,t,r){en(n,Ae(n),e,r)}),Yp=et(function(e,n,t,r){en(n,ce(n),e,r)}),Xp=cn(ci);function Jp(e,n){var t=jn(e);return n==null?t:ou(t,n)}var Qp=W(function(e,n){e=ee(e);var t=-1,r=n.length,i=r>2?n[2]:s;for(i&&Se(n[0],n[1],i)&&(r=1);++t<r;)for(var u=n[t],a=Ae(u),l=-1,p=a.length;++l<p;){var _=a[l],v=e[_];(v===s||Ze(v,Xn[_])&&!J.call(e,_))&&(e[_]=u[_])}return e}),jp=W(function(e){return e.push(s,Ku),De(Ds,s,e)});function eg(e,n){return Go(e,L(n,3),je)}function ng(e,n){return Go(e,L(n,3),pi)}function tg(e,n){return e==null?e:di(e,L(n,3),Ae)}function rg(e,n){return e==null?e:fu(e,L(n,3),Ae)}function ig(e,n){return e&&je(e,L(n,3))}function og(e,n){return e&&pi(e,L(n,3))}function ug(e){return e==null?[]:sr(e,ce(e))}function sg(e){return e==null?[]:sr(e,Ae(e))}function Hi(e,n,t){var r=e==null?s:Mn(e,n);return r===s?t:r}function ag(e,n){return e!=null&&zu(e,n,Of)}function ki(e,n){return e!=null&&zu(e,n,Ef)}var lg=Wu(function(e,n,t){n!=null&&typeof n.toString!="function"&&(n=Zt.call(n)),e[n]=t},Zi(Ie)),fg=Wu(function(e,n,t){n!=null&&typeof n.toString!="function"&&(n=Zt.call(n)),J.call(e,n)?e[n].push(t):e[n]=[t]},L),cg=W(At);function ce(e){return xe(e)?ru(e):vi(e)}function Ae(e){return xe(e)?ru(e,!0):$f(e)}function dg(e,n){var t={};return n=L(n,3),je(e,function(r,i,u){ln(t,n(r,i,u),r)}),t}function pg(e,n){var t={};return n=L(n,3),je(e,function(r,i,u){ln(t,i,n(r,i,u))}),t}var gg=et(function(e,n,t){ar(e,n,t)}),Ds=et(function(e,n,t,r){ar(e,n,t,r)}),hg=cn(function(e,n){var t={};if(e==null)return t;var r=!1;n=ie(n,function(u){return u=An(u,e),r||(r=u.length>1),u}),en(e,Oi(e),t),r&&(t=Ne(t,Q|K|G,lc));for(var i=n.length;i--;)xi(t,n[i]);return t});function _g(e,n){return Ls(e,xr(L(n)))}var vg=cn(function(e,n){return e==null?{}:Kf(e,n)});function Ls(e,n){if(e==null)return{};var t=ie(Oi(e),function(r){return[r]});return n=L(n),Su(e,t,function(r,i){return n(r,i[0])})}function mg(e,n,t){n=An(n,e);var r=-1,i=n.length;for(i||(i=1,e=s);++r<i;){var u=e==null?s:e[nn(n[r])];u===s&&(r=i,u=t),e=pn(u)?u.call(e):u}return e}function wg(e,n,t){return e==null?e:bt(e,n,t)}function Sg(e,n,t,r){return r=typeof r=="function"?r:s,e==null?e:bt(e,n,t,r)}var Rs=$u(ce),Os=$u(Ae);function yg(e,n,t){var r=B(e),i=r||bn(e)||rt(e);if(n=L(n,4),t==null){var u=e&&e.constructor;i?t=r?new u:[]:oe(e)?t=pn(u)?jn(Jt(e)):{}:t={}}return(i?Me:je)(e,function(a,l,p){return n(t,a,l,p)}),t}function Cg(e,n){return e==null?!0:xi(e,n)}function xg(e,n,t){return e==null?e:Iu(e,n,bi(t))}function Ag(e,n,t,r){return r=typeof r=="function"?r:s,e==null?e:Iu(e,n,bi(t),r)}function it(e){return e==null?[]:ii(e,ce(e))}function Ig(e){return e==null?[]:ii(e,Ae(e))}function bg(e,n,t){return t===s&&(t=n,n=s),t!==s&&(t=Ve(t),t=t===t?t:0),n!==s&&(n=Ve(n),n=n===n?n:0),Un(Ve(e),n,t)}function Pg(e,n,t){return n=gn(n),t===s?(t=n,n=0):t=gn(t),e=Ve(e),Tf(e,n,t)}function Dg(e,n,t){if(t&&typeof t!="boolean"&&Se(e,n,t)&&(n=t=s),t===s&&(typeof n=="boolean"?(t=n,n=s):typeof e=="boolean"&&(t=e,e=s)),e===s&&n===s?(e=0,n=1):(e=gn(e),n===s?(n=e,e=0):n=gn(n)),e>n){var r=e;e=n,n=r}if(t||e%1||n%1){var i=nu();return ve(e+i*(n-e+ol("1e-"+((i+"").length-1))),n)}return Si(e,n)}var Lg=nt(function(e,n,t){return n=n.toLowerCase(),e+(t?Es(n):n)});function Es(e){return zi(X(e).toLowerCase())}function Ts(e){return e=X(e),e&&e.replace(Ra,ml).replace(Ya,"")}function Rg(e,n,t){e=X(e),n=Re(n);var r=e.length;t=t===s?r:Un(F(t),0,r);var i=t;return t-=n.length,t>=0&&e.slice(t,i)==n}function Og(e){return e=X(e),e&&fa.test(e)?e.replace(lo,wl):e}function Eg(e){return e=X(e),e&&_a.test(e)?e.replace($r,"\\$&"):e}var Tg=nt(function(e,n,t){return e+(t?"-":"")+n.toLowerCase()}),Ug=nt(function(e,n,t){return e+(t?" ":"")+n.toLowerCase()}),Mg=Bu("toLowerCase");function Bg(e,n,t){e=X(e),n=F(n);var r=n?Zn(e):0;if(!n||r>=n)return e;var i=(n-r)/2;return gr(nr(i),t)+e+gr(er(i),t)}function Fg(e,n,t){e=X(e),n=F(n);var r=n?Zn(e):0;return n&&r<n?e+gr(n-r,t):e}function Ng(e,n,t){e=X(e),n=F(n);var r=n?Zn(e):0;return n&&r<n?gr(n-r,t)+e:e}function Wg(e,n,t){return t||n==null?n=0:n&&(n=+n),Hl(X(e).replace(qr,""),n||0)}function Gg(e,n,t){return(t?Se(e,n,t):n===s)?n=1:n=F(n),yi(X(e),n)}function Vg(){var e=arguments,n=X(e[0]);return e.length<3?n:n.replace(e[1],e[2])}var $g=nt(function(e,n,t){return e+(t?"_":"")+n.toLowerCase()});function qg(e,n,t){return t&&typeof t!="number"&&Se(e,n,t)&&(n=t=s),t=t===s?Qe:t>>>0,t?(e=X(e),e&&(typeof n=="string"||n!=null&&!Ki(n))&&(n=Re(n),!n&&zn(e))?In(ke(e),0,t):e.split(n,t)):[]}var Kg=nt(function(e,n,t){return e+(t?" ":"")+zi(n)});function Hg(e,n,t){return e=X(e),t=t==null?0:Un(F(t),0,e.length),n=Re(n),e.slice(t,t+n.length)==n}function kg(e,n,t){var r=o.templateSettings;t&&Se(e,n,t)&&(n=s),e=X(e),n=br({},n,r,qu);var i=br({},n.imports,r.imports,qu),u=ce(i),a=ii(i,u),l,p,_=0,v=n.interpolate||Wt,w="__p += '",C=ui((n.escape||Wt).source+"|"+v.source+"|"+(v===fo?Aa:Wt).source+"|"+(n.evaluate||Wt).source+"|$","g"),b="//# sourceURL="+(J.call(n,"sourceURL")?(n.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++el+"]")+`
`;e.replace(C,function(O,V,H,Ee,ye,Te){return H||(H=Ee),w+=e.slice(_,Te).replace(Oa,Sl),V&&(l=!0,w+=`' +
__e(`+V+`) +
'`),ye&&(p=!0,w+=`';
`+ye+`;
__p += '`),H&&(w+=`' +
((__t = (`+H+`)) == null ? '' : __t) +
'`),_=Te+O.length,O}),w+=`';
`;var R=J.call(n,"variable")&&n.variable;if(!R)w=`with (obj) {
`+w+`
}
`;else if(Ca.test(R))throw new M(U);w=(p?w.replace(ua,""):w).replace(sa,"$1").replace(aa,"$1;"),w="function("+(R||"obj")+`) {
`+(R?"":`obj || (obj = {});
`)+"var __t, __p = ''"+(l?", __e = _.escape":"")+(p?`, __j = Array.prototype.join;
function print() { __p += __j.call(arguments, '') }
`:`;
`)+w+`return __p
}`;var N=Ms(function(){return Y(u,b+"return "+w).apply(s,a)});if(N.source=w,qi(N))throw N;return N}function zg(e){return X(e).toLowerCase()}function Zg(e){return X(e).toUpperCase()}function Yg(e,n,t){if(e=X(e),e&&(t||n===s))return Ko(e);if(!e||!(n=Re(n)))return e;var r=ke(e),i=ke(n),u=Ho(r,i),a=ko(r,i)+1;return In(r,u,a).join("")}function Xg(e,n,t){if(e=X(e),e&&(t||n===s))return e.slice(0,Zo(e)+1);if(!e||!(n=Re(n)))return e;var r=ke(e),i=ko(r,ke(n))+1;return In(r,0,i).join("")}function Jg(e,n,t){if(e=X(e),e&&(t||n===s))return e.replace(qr,"");if(!e||!(n=Re(n)))return e;var r=ke(e),i=Ho(r,ke(n));return In(r,i).join("")}function Qg(e,n){var t=Et,r=_e;if(oe(n)){var i="separator"in n?n.separator:i;t="length"in n?F(n.length):t,r="omission"in n?Re(n.omission):r}e=X(e);var u=e.length;if(zn(e)){var a=ke(e);u=a.length}if(t>=u)return e;var l=t-Zn(r);if(l<1)return r;var p=a?In(a,0,l).join(""):e.slice(0,l);if(i===s)return p+r;if(a&&(l+=p.length-l),Ki(i)){if(e.slice(l).search(i)){var _,v=p;for(i.global||(i=ui(i.source,X(co.exec(i))+"g")),i.lastIndex=0;_=i.exec(v);)var w=_.index;p=p.slice(0,w===s?l:w)}}else if(e.indexOf(Re(i),l)!=l){var C=p.lastIndexOf(i);C>-1&&(p=p.slice(0,C))}return p+r}function jg(e){return e=X(e),e&&la.test(e)?e.replace(ao,Pl):e}var eh=nt(function(e,n,t){return e+(t?" ":"")+n.toUpperCase()}),zi=Bu("toUpperCase");function Us(e,n,t){return e=X(e),n=t?s:n,n===s?Cl(e)?Rl(e):pl(e):e.match(n)||[]}var Ms=W(function(e,n){try{return De(e,s,n)}catch(t){return qi(t)?t:new M(t)}}),nh=cn(function(e,n){return Me(n,function(t){t=nn(t),ln(e,t,Vi(e[t],e))}),e});function th(e){var n=e==null?0:e.length,t=L();return e=n?ie(e,function(r){if(typeof r[1]!="function")throw new Be(P);return[t(r[0]),r[1]]}):[],W(function(r){for(var i=-1;++i<n;){var u=e[i];if(De(u[0],this,r))return De(u[1],this,r)}})}function rh(e){return Df(Ne(e,Q))}function Zi(e){return function(){return e}}function ih(e,n){return e==null||e!==e?n:e}var oh=Nu(),uh=Nu(!0);function Ie(e){return e}function Yi(e){return gu(typeof e=="function"?e:Ne(e,Q))}function sh(e){return _u(Ne(e,Q))}function ah(e,n){return vu(e,Ne(n,Q))}var lh=W(function(e,n){return function(t){return At(t,e,n)}}),fh=W(function(e,n){return function(t){return At(e,t,n)}});function Xi(e,n,t){var r=ce(n),i=sr(n,r);t==null&&!(oe(n)&&(i.length||!r.length))&&(t=n,n=e,e=this,i=sr(n,ce(n)));var u=!(oe(t)&&"chain"in t)||!!t.chain,a=pn(e);return Me(i,function(l){var p=n[l];e[l]=p,a&&(e.prototype[l]=function(){var _=this.__chain__;if(u||_){var v=e(this.__wrapped__),w=v.__actions__=Ce(this.__actions__);return w.push({func:p,args:arguments,thisArg:e}),v.__chain__=_,v}return p.apply(e,wn([this.value()],arguments))})}),e}function ch(){return pe._===this&&(pe._=Bl),this}function Ji(){}function dh(e){return e=F(e),W(function(n){return mu(n,e)})}var ph=Di(ie),gh=Di(Wo),hh=Di(jr);function Bs(e){return Mi(e)?ei(nn(e)):Hf(e)}function _h(e){return function(n){return e==null?s:Mn(e,n)}}var vh=Gu(),mh=Gu(!0);function Qi(){return[]}function ji(){return!1}function wh(){return{}}function Sh(){return""}function yh(){return!0}function Ch(e,n){if(e=F(e),e<1||e>$n)return[];var t=Qe,r=ve(e,Qe);n=L(n),e-=Qe;for(var i=ri(r,n);++t<e;)n(t);return i}function xh(e){return B(e)?ie(e,nn):Oe(e)?[e]:Ce(ts(X(e)))}function Ah(e){var n=++Ul;return X(e)+n}var Ih=pr(function(e,n){return e+n},0),bh=Li("ceil"),Ph=pr(function(e,n){return e/n},1),Dh=Li("floor");function Lh(e){return e&&e.length?ur(e,Ie,gi):s}function Rh(e,n){return e&&e.length?ur(e,L(n,2),gi):s}function Oh(e){return $o(e,Ie)}function Eh(e,n){return $o(e,L(n,2))}function Th(e){return e&&e.length?ur(e,Ie,mi):s}function Uh(e,n){return e&&e.length?ur(e,L(n,2),mi):s}var Mh=pr(function(e,n){return e*n},1),Bh=Li("round"),Fh=pr(function(e,n){return e-n},0);function Nh(e){return e&&e.length?ti(e,Ie):0}function Wh(e,n){return e&&e.length?ti(e,L(n,2)):0}return o.after=sp,o.ary=ps,o.assign=Zp,o.assignIn=Ps,o.assignInWith=br,o.assignWith=Yp,o.at=Xp,o.before=gs,o.bind=Vi,o.bindAll=nh,o.bindKey=hs,o.castArray=wp,o.chain=fs,o.chunk=Dc,o.compact=Lc,o.concat=Rc,o.cond=th,o.conforms=rh,o.constant=Zi,o.countBy=Nd,o.create=Jp,o.curry=_s,o.curryRight=vs,o.debounce=ms,o.defaults=Qp,o.defaultsDeep=jp,o.defer=ap,o.delay=lp,o.difference=Oc,o.differenceBy=Ec,o.differenceWith=Tc,o.drop=Uc,o.dropRight=Mc,o.dropRightWhile=Bc,o.dropWhile=Fc,o.fill=Nc,o.filter=Gd,o.flatMap=qd,o.flatMapDeep=Kd,o.flatMapDepth=Hd,o.flatten=us,o.flattenDeep=Wc,o.flattenDepth=Gc,o.flip=fp,o.flow=oh,o.flowRight=uh,o.fromPairs=Vc,o.functions=ug,o.functionsIn=sg,o.groupBy=kd,o.initial=qc,o.intersection=Kc,o.intersectionBy=Hc,o.intersectionWith=kc,o.invert=lg,o.invertBy=fg,o.invokeMap=Zd,o.iteratee=Yi,o.keyBy=Yd,o.keys=ce,o.keysIn=Ae,o.map=Sr,o.mapKeys=dg,o.mapValues=pg,o.matches=sh,o.matchesProperty=ah,o.memoize=Cr,o.merge=gg,o.mergeWith=Ds,o.method=lh,o.methodOf=fh,o.mixin=Xi,o.negate=xr,o.nthArg=dh,o.omit=hg,o.omitBy=_g,o.once=cp,o.orderBy=Xd,o.over=ph,o.overArgs=dp,o.overEvery=gh,o.overSome=hh,o.partial=$i,o.partialRight=ws,o.partition=Jd,o.pick=vg,o.pickBy=Ls,o.property=Bs,o.propertyOf=_h,o.pull=Xc,o.pullAll=as,o.pullAllBy=Jc,o.pullAllWith=Qc,o.pullAt=jc,o.range=vh,o.rangeRight=mh,o.rearg=pp,o.reject=ep,o.remove=ed,o.rest=gp,o.reverse=Wi,o.sampleSize=tp,o.set=wg,o.setWith=Sg,o.shuffle=rp,o.slice=nd,o.sortBy=up,o.sortedUniq=ad,o.sortedUniqBy=ld,o.split=qg,o.spread=hp,o.tail=fd,o.take=cd,o.takeRight=dd,o.takeRightWhile=pd,o.takeWhile=gd,o.tap=Ld,o.throttle=_p,o.thru=wr,o.toArray=As,o.toPairs=Rs,o.toPairsIn=Os,o.toPath=xh,o.toPlainObject=bs,o.transform=yg,o.unary=vp,o.union=hd,o.unionBy=_d,o.unionWith=vd,o.uniq=md,o.uniqBy=wd,o.uniqWith=Sd,o.unset=Cg,o.unzip=Gi,o.unzipWith=ls,o.update=xg,o.updateWith=Ag,o.values=it,o.valuesIn=Ig,o.without=yd,o.words=Us,o.wrap=mp,o.xor=Cd,o.xorBy=xd,o.xorWith=Ad,o.zip=Id,o.zipObject=bd,o.zipObjectDeep=Pd,o.zipWith=Dd,o.entries=Rs,o.entriesIn=Os,o.extend=Ps,o.extendWith=br,Xi(o,o),o.add=Ih,o.attempt=Ms,o.camelCase=Lg,o.capitalize=Es,o.ceil=bh,o.clamp=bg,o.clone=Sp,o.cloneDeep=Cp,o.cloneDeepWith=xp,o.cloneWith=yp,o.conformsTo=Ap,o.deburr=Ts,o.defaultTo=ih,o.divide=Ph,o.endsWith=Rg,o.eq=Ze,o.escape=Og,o.escapeRegExp=Eg,o.every=Wd,o.find=Vd,o.findIndex=is,o.findKey=eg,o.findLast=$d,o.findLastIndex=os,o.findLastKey=ng,o.floor=Dh,o.forEach=cs,o.forEachRight=ds,o.forIn=tg,o.forInRight=rg,o.forOwn=ig,o.forOwnRight=og,o.get=Hi,o.gt=Ip,o.gte=bp,o.has=ag,o.hasIn=ki,o.head=ss,o.identity=Ie,o.includes=zd,o.indexOf=$c,o.inRange=Pg,o.invoke=cg,o.isArguments=Nn,o.isArray=B,o.isArrayBuffer=Pp,o.isArrayLike=xe,o.isArrayLikeObject=se,o.isBoolean=Dp,o.isBuffer=bn,o.isDate=Lp,o.isElement=Rp,o.isEmpty=Op,o.isEqual=Ep,o.isEqualWith=Tp,o.isError=qi,o.isFinite=Up,o.isFunction=pn,o.isInteger=Ss,o.isLength=Ar,o.isMap=ys,o.isMatch=Mp,o.isMatchWith=Bp,o.isNaN=Fp,o.isNative=Np,o.isNil=Gp,o.isNull=Wp,o.isNumber=Cs,o.isObject=oe,o.isObjectLike=ue,o.isPlainObject=Rt,o.isRegExp=Ki,o.isSafeInteger=Vp,o.isSet=xs,o.isString=Ir,o.isSymbol=Oe,o.isTypedArray=rt,o.isUndefined=$p,o.isWeakMap=qp,o.isWeakSet=Kp,o.join=zc,o.kebabCase=Tg,o.last=Ge,o.lastIndexOf=Zc,o.lowerCase=Ug,o.lowerFirst=Mg,o.lt=Hp,o.lte=kp,o.max=Lh,o.maxBy=Rh,o.mean=Oh,o.meanBy=Eh,o.min=Th,o.minBy=Uh,o.stubArray=Qi,o.stubFalse=ji,o.stubObject=wh,o.stubString=Sh,o.stubTrue=yh,o.multiply=Mh,o.nth=Yc,o.noConflict=ch,o.noop=Ji,o.now=yr,o.pad=Bg,o.padEnd=Fg,o.padStart=Ng,o.parseInt=Wg,o.random=Dg,o.reduce=Qd,o.reduceRight=jd,o.repeat=Gg,o.replace=Vg,o.result=mg,o.round=Bh,o.runInContext=d,o.sample=np,o.size=ip,o.snakeCase=$g,o.some=op,o.sortedIndex=td,o.sortedIndexBy=rd,o.sortedIndexOf=id,o.sortedLastIndex=od,o.sortedLastIndexBy=ud,o.sortedLastIndexOf=sd,o.startCase=Kg,o.startsWith=Hg,o.subtract=Fh,o.sum=Nh,o.sumBy=Wh,o.template=kg,o.times=Ch,o.toFinite=gn,o.toInteger=F,o.toLength=Is,o.toLower=zg,o.toNumber=Ve,o.toSafeInteger=zp,o.toString=X,o.toUpper=Zg,o.trim=Yg,o.trimEnd=Xg,o.trimStart=Jg,o.truncate=Qg,o.unescape=jg,o.uniqueId=Ah,o.upperCase=eh,o.upperFirst=zi,o.each=cs,o.eachRight=ds,o.first=ss,Xi(o,function(){var e={};return je(o,function(n,t){J.call(o.prototype,t)||(e[t]=n)}),e}(),{chain:!1}),o.VERSION=x,Me(["bind","bindKey","curry","curryRight","partial","partialRight"],function(e){o[e].placeholder=o}),Me(["drop","take"],function(e,n){q.prototype[e]=function(t){t=t===s?1:fe(F(t),0);var r=this.__filtered__&&!n?new q(this):this.clone();return r.__filtered__?r.__takeCount__=ve(t,r.__takeCount__):r.__views__.push({size:ve(t,Qe),type:e+(r.__dir__<0?"Right":"")}),r},q.prototype[e+"Right"]=function(t){return this.reverse()[e](t).reverse()}}),Me(["filter","map","takeWhile"],function(e,n){var t=n+1,r=t==oo||t==Ys;q.prototype[e]=function(i){var u=this.clone();return u.__iteratees__.push({iteratee:L(i,3),type:t}),u.__filtered__=u.__filtered__||r,u}}),Me(["head","last"],function(e,n){var t="take"+(n?"Right":"");q.prototype[e]=function(){return this[t](1).value()[0]}}),Me(["initial","tail"],function(e,n){var t="drop"+(n?"":"Right");q.prototype[e]=function(){return this.__filtered__?new q(this):this[t](1)}}),q.prototype.compact=function(){return this.filter(Ie)},q.prototype.find=function(e){return this.filter(e).head()},q.prototype.findLast=function(e){return this.reverse().find(e)},q.prototype.invokeMap=W(function(e,n){return typeof e=="function"?new q(this):this.map(function(t){return At(t,e,n)})}),q.prototype.reject=function(e){return this.filter(xr(L(e)))},q.prototype.slice=function(e,n){e=F(e);var t=this;return t.__filtered__&&(e>0||n<0)?new q(t):(e<0?t=t.takeRight(-e):e&&(t=t.drop(e)),n!==s&&(n=F(n),t=n<0?t.dropRight(-n):t.take(n-e)),t)},q.prototype.takeRightWhile=function(e){return this.reverse().takeWhile(e).reverse()},q.prototype.toArray=function(){return this.take(Qe)},je(q.prototype,function(e,n){var t=/^(?:filter|find|map|reject)|While$/.test(n),r=/^(?:head|last)$/.test(n),i=o[r?"take"+(n=="last"?"Right":""):n],u=r||/^find/.test(n);i&&(o.prototype[n]=function(){var a=this.__wrapped__,l=r?[1]:arguments,p=a instanceof q,_=l[0],v=p||B(a),w=function(V){var H=i.apply(o,wn([V],l));return r&&C?H[0]:H};v&&t&&typeof _=="function"&&_.length!=1&&(p=v=!1);var C=this.__chain__,b=!!this.__actions__.length,R=u&&!C,N=p&&!b;if(!u&&v){a=N?a:new q(this);var O=e.apply(a,l);return O.__actions__.push({func:wr,args:[w],thisArg:s}),new Fe(O,C)}return R&&N?e.apply(this,l):(O=this.thru(w),R?r?O.value()[0]:O.value():O)})}),Me(["pop","push","shift","sort","splice","unshift"],function(e){var n=Ht[e],t=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",r=/^(?:pop|shift)$/.test(e);o.prototype[e]=function(){var i=arguments;if(r&&!this.__chain__){var u=this.value();return n.apply(B(u)?u:[],i)}return this[t](function(a){return n.apply(B(a)?a:[],i)})}}),je(q.prototype,function(e,n){var t=o[n];if(t){var r=t.name+"";J.call(Qn,r)||(Qn[r]=[]),Qn[r].push({name:n,func:t})}}),Qn[dr(s,Je).name]=[{name:"wrapper",func:s}],q.prototype.clone=Ql,q.prototype.reverse=jl,q.prototype.value=ef,o.prototype.at=Rd,o.prototype.chain=Od,o.prototype.commit=Ed,o.prototype.next=Td,o.prototype.plant=Md,o.prototype.reverse=Bd,o.prototype.toJSON=o.prototype.valueOf=o.prototype.value=Fd,o.prototype.first=o.prototype.head,vt&&(o.prototype[vt]=Ud),o},Yn=Ol();Rn?((Rn.exports=Yn)._=Yn,Yr._=Yn):pe._=Yn}).call(y_)}(Ot,Ot.exports)),Ot.exports}C_();Or.registerLanguage("json",Vh);Or.registerLanguage("javascript",$h);Or.registerLanguage("bash",qh);Kh.use(Hh,{Hljs:Or});const x_={props:{modelValue:String,height:{type:String,default:"300px"}},emits:["update:modelValue"],setup(f,{emit:c}){const s=tn(f.modelValue||"");return Dn(()=>f.modelValue,y=>{s.value=y}),{localValue:s,handleChange:y=>{c("update:modelValue",y)}}}};function A_(f,c,s,x,y,I){const P=he("v-md-editor");return ne(),_n(P,{modelValue:x.localValue,"onUpdate:modelValue":c[0]||(c[0]=U=>x.localValue=U),height:s.height,"left-toolbar":"undo redo clear | h bold italic strikethrough quote | ul ol table hr | link image code",onChange:x.handleChange},null,8,["modelValue","height","onChange"])}const I_=Xe(x_,[["render",A_]]),b_={name:"ModelSettings",components:{MarkdownEditor:I_},props:{modelOptions:{type:Array,default:()=>[],validator:f=>f.every(c=>c.id&&c.platform&&c.name)},modelId:{type:String,required:!0,default:""},rolePrompt:{type:String,default:""},rulePrompt:{type:String,default:"{}",validator:f=>!0},responsePrompt:{type:String,default:"{}",validator:f=>!0}},emits:{"update:modelId":f=>typeof f=="string","update:rolePrompt":f=>typeof f=="string","update:modelSettings":f=>typeof f.rolePrompt=="string"&&typeof f.rulePrompt=="string"&&typeof f.responsePrompt=="string","update:functionCallSupport":f=>typeof f=="boolean"},setup(f,{emit:c}){const s=tn(!1),x=tn(0),y=tn(f.rolePrompt),I=tn(f.rulePrompt),P=tn(f.responsePrompt),U=eo({get:()=>f.modelId,set:T=>c("update:modelId",T)}),$=eo(()=>f.modelOptions.filter(T=>T.id)),z=eo(()=>{if(!U.value)return!1;const T=f.modelOptions.find(k=>k.id.toString()===U.value.toString());return T&&T.functionCall===1});return Dn(()=>f.rulePrompt,T=>{I.value=T}),Dn(()=>f.responsePrompt,T=>{P.value=T}),Dn(()=>f.rolePrompt,T=>{y.value=T}),Dn(()=>z.value,T=>{c("update:functionCallSupport",T)},{immediate:!0}),Dn(()=>U.value,()=>{c("update:functionCallSupport",z.value)}),{Edit:Er,selectedModel:U,validatedModelOptions:$,dialogVisible:s,localRolePrompt:y,editorKey:x,updateRolePrompt:T=>{c("update:rolePrompt",T)},openPromptDialog:async()=>{x.value+=1,await Ks(),s.value=!0},handleDialogClose:()=>{},savePrompts:()=>{if(y.value.trim()===""){$e.error("角色提示不能为空");return}c("update:modelSettings",{rolePrompt:y.value,rulePrompt:I.value,responsePrompt:P.value}),$e.success("设置保存成功"),s.value=!1},localRulePrompt:I,localResponsePrompt:P,currentModelSupportsFunctionCall:z}}},P_={class:"model-settings"},D_={class:"section-header"};function L_(f,c,s,x,y,I){const P=ut,U=Gn,$=$s,z=Vs,E=Wn,Q=st,K=Rr,G=he("markdown-editor"),T=qs;return ne(),de("div",P_,[m(Q,{class:"settings-card"},{header:S(()=>[D("div",D_,[c[7]||(c[7]=D("span",{class:"group-title"},"模型选择",-1)),m(P,{type:"primary",text:"",icon:x.Edit,class:"action-button",onClick:x.openPromptDialog},{default:S(()=>c[6]||(c[6]=[be(" 用户指令 ")])),_:1},8,["icon","onClick"])])]),default:S(()=>[m(E,{class:"model-selection"},{default:S(()=>[m(U,{span:6},{default:S(()=>c[8]||(c[8]=[D("span",{class:"selection-label"},"生成模型",-1)])),_:1}),m(U,{span:16,offset:2},{default:S(()=>[m(z,{modelValue:x.selectedModel,"onUpdate:modelValue":c[0]||(c[0]=k=>x.selectedModel=k),placeholder:"选择模型",size:"large",class:"model-selector"},{default:S(()=>[(ne(!0),de(Dr,null,Lr(x.validatedModelOptions,k=>(ne(),_n($,{key:k.id,label:`${k.platform}-${k.name}`,value:k.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),m(T,{modelValue:x.dialogVisible,"onUpdate:modelValue":c[5]||(c[5]=k=>x.dialogVisible=k),title:"模型交互设置",width:"70%",onClosed:x.handleDialogClose},{footer:S(()=>[m(P,{onClick:c[4]||(c[4]=k=>x.dialogVisible=!1)},{default:S(()=>c[12]||(c[12]=[be("取消")])),_:1}),m(P,{type:"primary",onClick:x.savePrompts},{default:S(()=>c[13]||(c[13]=[be("保存")])),_:1},8,["onClick"])]),default:S(()=>[m(E,{class:"model-selection"},{default:S(()=>[m(U,{span:4},{default:S(()=>c[9]||(c[9]=[D("span",{class:"selection-label"},"系统角色",-1)])),_:1}),m(U,{span:20},{default:S(()=>[m(K,{modelValue:x.localRolePrompt,"onUpdate:modelValue":c[1]||(c[1]=k=>x.localRolePrompt=k),placeholder:"设置模型的角色",onInput:x.updateRolePrompt},null,8,["modelValue","onInput"])]),_:1})]),_:1}),m(E,{class:"model-selection"},{default:S(()=>[m(U,{span:4},{default:S(()=>c[10]||(c[10]=[D("span",{class:"selection-label"},"用户指令",-1)])),_:1}),m(U,{span:20},{default:S(()=>[m(G,{modelValue:x.localRulePrompt,"onUpdate:modelValue":c[2]||(c[2]=k=>x.localRulePrompt=k),height:"300px"},null,8,["modelValue"])]),_:1})]),_:1}),m(E,{class:"model-selection"},{default:S(()=>[m(U,{span:4},{default:S(()=>c[11]||(c[11]=[D("span",{class:"selection-label"},"应用回复",-1)])),_:1}),m(U,{span:20},{default:S(()=>[m(G,{modelValue:x.localResponsePrompt,"onUpdate:modelValue":c[3]||(c[3]=k=>x.localResponsePrompt=k),height:"300px"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue","onClosed"])])}const R_=Xe(b_,[["render",L_],["__scopeId","data-v-7ba20d90"]]),O_={name:"KnowledgeSettings",components:{CopyDocument:kh},props:{recallCount:{type:Number,default:1},similarity:{type:Number,default:.1},selectedKnowledgeBases:{type:Array,default:()=>[]}},emits:["update:recallCount","update:similarity","openAssociationDialog"],setup(f,{emit:c}){return{Edit:Er,updateRecallCount:I=>{c("update:recallCount",I)},updateSimilarity:I=>{c("update:similarity",I)},openAssociationDialog:()=>{c("openAssociationDialog")}}}},E_={class:"knowledge-settings"},T_={class:"section-header"},U_={class:"settings-content"},M_={class:"setting-control"},B_={class:"setting-control"};function F_(f,c,s,x,y,I){const P=ut,U=zh,$=Gs,z=Wn,E=he("copy-document"),Q=to,K=io,G=ro,T=Gn,k=st;return ne(),de("div",E_,[m(k,{class:"settings-card"},{header:S(()=>[D("div",T_,[c[1]||(c[1]=D("span",{class:"group-title"},"知识",-1)),m(P,{type:"primary",text:"",icon:x.Edit,class:"association-btn",onClick:x.openAssociationDialog},{default:S(()=>c[0]||(c[0]=[be(" 关联知识库 ")])),_:1},8,["icon","onClick"])])]),default:S(()=>[D("div",U_,[m(z,{class:"setting-item"},{default:S(()=>[D("div",M_,[c[2]||(c[2]=D("span",{class:"setting-label"},"召回数",-1)),m(U,{"model-value":s.recallCount,"onUpdate:modelValue":x.updateRecallCount,min:1,max:10,size:"small","controls-position":"right"},null,8,["model-value","onUpdate:modelValue"])]),m($,{type:"info",class:"hint-tag"},{default:S(()=>c[3]||(c[3]=[be("召回切片的字符总数不应超过所选模型上下文长度")])),_:1})]),_:1}),m(z,{class:"setting-item"},{default:S(()=>[D("div",B_,[c[4]||(c[4]=D("span",{class:"setting-label"},"相似度",-1)),m(U,{"model-value":s.similarity,"onUpdate:modelValue":x.updateSimilarity,min:.1,max:1,step:.1,size:"small","controls-position":"right"},null,8,["model-value","onUpdate:modelValue"])]),m($,{type:"info",class:"hint-tag"},{default:S(()=>c[5]||(c[5]=[be("调整匹配分阈值以过滤得到最相关答案")])),_:1})]),_:1}),m(z,null,{default:S(()=>[m(T,null,{default:S(()=>[m(G,{data:s.selectedKnowledgeBases,style:{width:"100%"},"empty-text":"暂无关联知识库",class:"knowledge-table"},{default:S(()=>[m(K,{width:"40px"},{default:S(()=>[m(Q,null,{default:S(()=>[m(E)]),_:1})]),_:1}),m(K,{prop:"name",label:"知识库名称"})]),_:1},8,["data"])]),_:1})]),_:1})])]),_:1})])}const N_=Xe(O_,[["render",F_],["__scopeId","data-v-06d81479"]]),W_={name:"MCPServerSettings",props:{selectedMCPServers:{type:Array,default:()=>[]},functionCallSupported:{type:Boolean,default:!1}},emits:["update:selectedMCPServers","openMCPServerDialog"],setup(f,{emit:c}){return{Edit:Er,Delete:Yh,Monitor:Zh,openMCPServerDialog:()=>{f.functionCallSupported&&(console.log("MCPServerSettings: openMCPServerDialog被调用"),c("openMCPServerDialog"))},updateMCPServers:()=>{c("update:selectedMCPServers",[...f.selectedMCPServers])},removeMCPServer:I=>{const P=[...f.selectedMCPServers];P.splice(I,1),c("update:selectedMCPServers",P)}}}},G_={class:"mcpserver-settings"},V_={class:"section-header"},$_={class:"settings-content"},q_={class:"order-number"};function K_(f,c,s,x,y,I){const P=ut,U=Xh,$=io,z=ro,E=Gn,Q=Wn,K=st;return ne(),de("div",G_,[m(K,{class:"settings-card"},{header:S(()=>[D("div",V_,[c[1]||(c[1]=D("span",{class:"group-title"},"MCP Server",-1)),m(U,{content:s.functionCallSupported?"":"当前模型不支持关联MCP Server",placement:"top",disabled:s.functionCallSupported},{default:S(()=>[m(P,{type:"primary",text:"",icon:x.Edit,class:"association-btn",onClick:x.openMCPServerDialog,disabled:!s.functionCallSupported},{default:S(()=>c[0]||(c[0]=[be(" 关联 MCP Server ")])),_:1},8,["icon","onClick","disabled"])]),_:1},8,["content","disabled"])])]),default:S(()=>[D("div",$_,[m(Q,null,{default:S(()=>[m(E,null,{default:S(()=>[m(z,{data:s.selectedMCPServers,style:{width:"100%"},"empty-text":"暂无关联 MCP Server",class:"mcpserver-table"},{default:S(()=>[m($,{width:"40px"},{default:S(G=>[D("div",q_,Pn(G.$index+1),1)]),_:1}),m($,{prop:"mcpServerName",width:"120px",label:"Server 名称"}),m($,{prop:"description",label:"功能描述"}),m($,{width:"80px"},{default:S(G=>[m(P,{type:"danger",text:"",icon:x.Delete,onClick:T=>x.removeMCPServer(G.$index)},null,8,["icon","onClick"])]),_:1})]),_:1},8,["data"])]),_:1})]),_:1})])]),_:1})])}const H_=Xe(W_,[["render",K_],["__scopeId","data-v-fd170a76"]]),k_=Hs({name:"AgentConfig",components:{BasicSettings:S_,ModelSettings:R_,KnowledgeSettings:N_,MCPServerSettings:H_,UserFilled:ks,Tools:Jh},props:{agentData:{type:Object,required:!0},modelOptions:{type:Array,default:()=>[]},baseURL:{type:String,default:""}},emits:["update:agentData","openAssociationDialog","openMCPServerDialog","createSuccess"],setup(f,{emit:c}){const s=tn(!1),x=tn(!1),y=tn(!1),I=(K,G)=>{c("update:agentData",{...f.agentData,[K]:G})},P=K=>{c("update:agentData",{...f.agentData,...K})},U=K=>{y.value=K},$=()=>{var K;return(K=f.agentData.name)!=null&&K.trim()?f.agentData.groupId?!0:($e.error("工作组不能为空，请选择工作组"),!1):($e.error("智能体标题不能为空"),!1)},z=()=>{const{name:K,description:G="",imageUrl:T="",knowledgeBaseIds:k="",recallCount:j=1,similarity:Je=.1,modelId:rn,groupId:Pe,rolePrompt:on="qa",responsePrompt:Z="",rulePrompt:vn="",flowNodes:qe=[]}=f.agentData,at=[...qe].sort((_e,Ln)=>_e.numbering-Ln.numbering).map((_e,Ln)=>({..._e,numbering:Ln}));let Et="";if(rn&&f.modelOptions){const _e=f.modelOptions.find(Ln=>Ln.id===rn);_e&&(Et=_e.name)}return{name:K,description:G,url:T,knowledgeBaseIds:k,recallCount:j,similarity:Je,modelId:rn,modelName:Et,groupId:Pe,rolePrompt:on,responsePrompt:Z,rulePrompt:vn,flowNodes:at.map(_e=>({..._e.id?{id:_e.id}:{},mcpServerId:_e.mcpServerId,mcpServerName:_e.mcpServerName||"",numbering:_e.numbering,inputLimit:_e.inputLimit||"",outputLimit:_e.outputLimit||""}))}};return{dialogTableVisible:s,loading:x,functionCallSupported:y,updateField:I,updateModelSettings:P,updateFunctionCallSupport:U,createAgent:async()=>{if($()){x.value=!0;try{const K=z(),G=await no.saveAgent(K);if(G&&G.data&&G.code===200)$e.success("创建智能体成功"),c("createSuccess",parseInt(G.data));else{const T=(G==null?void 0:G.message)||"未知错误";$e.error(`创建失败: ${T}`)}}catch(K){console.error("创建智能体出错:",K),$e.error("创建智能体时发生错误，请稍后重试")}finally{x.value=!1}}},updateAgent:async()=>{if($()){if(!f.agentData.id){$e.error("缺少智能体ID，无法更新");return}x.value=!0;try{const G={...z(),id:f.agentData.id},T=await no.updateAgent(G);(T==null?void 0:T.code)===200&&$e.success((T==null?void 0:T.data)||"更新智能体成功")}finally{x.value=!1}}}}}}),z_={class:"agent-config"},Z_={class:"config-container"},Y_={class:"config-section"},X_={class:"section-content"},J_={class:"setting-group"},Q_={class:"setting-group"},j_={class:"setting-group"},ev={class:"setting-group"};function nv(f,c,s,x,y,I){const P=he("BasicSettings"),U=he("ModelSettings"),$=he("KnowledgeSettings"),z=he("MCPServerSettings");return ne(),de("div",z_,[D("div",Z_,[D("div",Y_,[D("div",X_,[D("div",J_,[m(P,{baseURL:f.baseURL,imageUrl:f.agentData.imageUrl,agentName:f.agentData.name,description:f.agentData.description,groupId:f.agentData.groupId,"onUpdate:imageUrl":c[0]||(c[0]=E=>f.updateField("imageUrl",E)),"onUpdate:agentName":c[1]||(c[1]=E=>f.updateField("name",E)),"onUpdate:description":c[2]||(c[2]=E=>f.updateField("description",E)),"onUpdate:groupId":c[3]||(c[3]=E=>f.updateField("groupId",E))},null,8,["baseURL","imageUrl","agentName","description","groupId"])]),D("div",Q_,[m(U,{modelOptions:f.modelOptions,modelId:f.agentData.modelId,rolePrompt:f.agentData.rolePrompt,rulePrompt:f.agentData.rulePrompt,responsePrompt:f.agentData.responsePrompt,"onUpdate:modelId":c[4]||(c[4]=E=>f.updateField("modelId",E)),"onUpdate:modelSettings":f.updateModelSettings,"onUpdate:functionCallSupport":f.updateFunctionCallSupport},null,8,["modelOptions","modelId","rolePrompt","rulePrompt","responsePrompt","onUpdate:modelSettings","onUpdate:functionCallSupport"])]),D("div",j_,[m($,{recallCount:f.agentData.recallCount,similarity:f.agentData.similarity,selectedKnowledgeBases:f.agentData.selectedRows,"onUpdate:recallCount":c[5]||(c[5]=E=>f.updateField("recallCount",E)),"onUpdate:similarity":c[6]||(c[6]=E=>f.updateField("similarity",E)),onOpenAssociationDialog:c[7]||(c[7]=E=>f.$emit("openAssociationDialog"))},null,8,["recallCount","similarity","selectedKnowledgeBases"])]),D("div",ev,[m(z,{selectedMCPServers:f.agentData.flowNodes||[],functionCallSupported:f.functionCallSupported,"onUpdate:selectedMCPServers":c[8]||(c[8]=E=>f.updateField("flowNodes",E)),onOpenMCPServerDialog:c[9]||(c[9]=E=>f.$emit("openMCPServerDialog"))},null,8,["selectedMCPServers","functionCallSupported"])])])])])])}const tv=Xe(k_,[["render",nv],["__scopeId","data-v-fb0db142"]]),rv=Hs({name:"AgentInput",props:{isLoading:Boolean,agentId:{type:[Number,String],default:0},modelValue:String},emits:["submit","update:modelValue"],setup(f,{emit:c}){const s=tn(f.modelValue||""),x=()=>{var y;console.log("onSubmit",s.value),(y=s.value)!=null&&y.trim()?c("submit",{prompt:s.value,agentId:f.agentId||0}):$e.error("请输入问题")};return Dn(()=>f.modelValue,y=>{s.value=y||""}),Dn(s,y=>{c("update:modelValue",y)}),{inputPrompt:s,onSubmit:x}}}),iv={class:"input-container"},ov={class:"grid-content bg-purple-dark",style:{margin:"10px",display:"flex","justify-content":"flex-end","align-items":"center"}},uv={key:0,src:o_,height:"25px",width:"25px"};function sv(f,c,s,x,y,I){const P=Rr,U=ut,$=st,z=Gn,E=Wn;return ne(),de("div",iv,[m(E,{justify:"center"},{default:S(()=>[m(z,{span:22,style:{"border-radius":"12px"}},{default:S(()=>[m($,{"body-style":{padding:"0px"},class:"fixed-input"},{default:S(()=>{var Q;return[D("div",ov,[m(P,{modelValue:f.inputPrompt,"onUpdate:modelValue":c[0]||(c[0]=K=>f.inputPrompt=K),placeholder:"输入你的问题...",style:{height:"40px",border:"none !important",flex:"1"},onKeyup:Qh(f.onSubmit,["enter"]),disabled:f.isLoading},null,8,["modelValue","onKeyup","disabled"]),m(U,{link:"",onClick:f.onSubmit,style:{width:"40px"},loading:f.isLoading,disabled:!((Q=f.inputPrompt)!=null&&Q.trim())||f.isLoading},{default:S(()=>[f.isLoading?ot("",!0):(ne(),de("img",uv))]),_:1},8,["onClick","loading","disabled"])])]}),_:1})]),_:1})]),_:1})])}const av=Xe(rv,[["render",sv],["__scopeId","data-v-c75e4363"]]),lv={name:"AgentPreview",components:{ChatContainer:u_,AgentInput:av},props:{agentId:{type:Number,default:0}},data(){return{prompt:"",isLoading:!1,currentQuestion:"",currentAnswer:"",conversations:[],showOutput:!1}},methods:{async startChat(f){var x;const{prompt:c,agentId:s}=f;this.isLoading=!0,this.currentQuestion=c,this.currentAnswer="",this.prompt="",this.showOutput=!0;try{const y=await fetch("/rag/api/agent/stream?stream=true",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:this.currentQuestion,agentId:s})});if(!y.ok)throw new Error("Network response was not ok");const I=(x=y.body)==null?void 0:x.getReader(),P=new TextDecoder("utf-8");let U="";if(I){let $="";for(;;){const{value:z,done:E}=await I.read();if(E)break;const Q=P.decode(z,{stream:!0});$+=Q;const K=$.split(`
`);$=K.pop()||"";for(const G of K)if(G.startsWith('data:"')){const T=G.split(':"');if(T.length>1){const k=T[1].slice(0,-1);U+=k,this.currentAnswer=await a_(U.replace(/\n+/g,"<br/>")),await Ks()}}}}this.conversations.push({question:this.currentQuestion,answer:this.currentAnswer}),this.currentQuestion="",this.currentAnswer=""}catch(y){console.error("Error:",y),$e.error("请求失败，请重试"),this.conversations.push({question:this.currentQuestion,answer:"请求失败，请重试"}),this.currentQuestion="",this.currentAnswer=""}finally{this.isLoading=!1}}}},fv={class:"agent-preview"},cv={class:"chat-container-wrapper"},dv={class:"input-container"};function pv(f,c,s,x,y,I){const P=Gn,U=Wn,$=he("chat-container"),z=he("agent-input");return ne(),de("div",fv,[D("div",cv,[m($,{conversations:y.conversations,"current-question":y.currentQuestion,"current-answer":y.currentAnswer},{empty:S(()=>[m(U,{justify:"center"},{default:S(()=>[m(P,{span:20},{default:S(()=>c[1]||(c[1]=[D("div",{style:{"text-align":"center","font-size":"10px","font-family":"'Courier New', Courier, monospace"}},null,-1)])),_:1})]),_:1}),y.showOutput?ot("",!0):(ne(),_n(U,{key:0,style:{"min-height":"400px"}},{default:S(()=>[m(P,null,{default:S(()=>[m(U,{style:{"margin-top":"100px"}},{default:S(()=>[m(P,{span:24,style:{display:"flex","justify-content":"center","align-items":"center",height:"100%"}},{default:S(()=>c[2]||(c[2]=[D("img",{src:s_,style:{width:"72px",height:"72px"}},null,-1)])),_:1})]),_:1}),m(U,null,{default:S(()=>[m(P,{span:24,style:{display:"flex","justify-content":"center","align-items":"center",height:"100%"}},{default:S(()=>c[3]||(c[3]=[D("span",null,"我的Agent",-1)])),_:1})]),_:1})]),_:1})]),_:1}))]),_:1},8,["conversations","current-question","current-answer"])]),D("div",dv,[m(z,{modelValue:y.prompt,"onUpdate:modelValue":c[0]||(c[0]=E=>y.prompt=E),agentId:s.agentId,isLoading:y.isLoading,onSubmit:I.startChat},null,8,["modelValue","agentId","isLoading","onSubmit"])])])}const gv=Xe(lv,[["render",pv],["__scopeId","data-v-4de12301"]]),hv={name:"AgentTable",props:{groupId:{type:Number,default:0}},data(){return{selectedRows:[],tableData:[]}},created(){this.getTableData()},methods:{getTableData(){l_(this.groupId).then(f=>{this.tableData=f.data})},handleSelectionChange(f){this.selectedRows=f,this.$emit("updateValue",this.selectedRows),console.log(this.selectedRows)}}};function _v(f,c,s,x,y,I){const P=io,U=ro;return ne(),_n(U,{onSelectionChange:I.handleSelectionChange,data:y.tableData},{default:S(()=>[m(P,{type:"selection",width:"55"}),m(P,{property:"name",label:"名字",width:"300"}),m(P,{property:"description",label:"描述",width:"400"})]),_:1},8,["onSelectionChange","data"])}const vv=Xe(hv,[["render",_v]]),mv={components:{AgentHeader:g_,AgentConfig:tv,AgentPreview:gv,AgentTable:vv,UserFilled:ks,ViewIcon:e_,Search:jh},data(){return{agentData:{id:0,imageUrl:"",name:"",description:"",groupId:0,responsePrompt:"",rulePrompt:"",rolePrompt:"qa",recallCount:1,similarity:.1,modelId:"",selectedRows:[],knowledgeBaseIds:"",mcpServerIds:"",selectedMcpServers:[],flowNodes:[]},dialogTableVisible:!1,mcpServerDialogVisible:!1,isLoading:!1,isLoadingFaBu:!0,isLoadingUpdate:!1,baseURL:window.location.origin+"/rag/api/agent/image/upload",modelOptions:[],Edit:Er,mcpServers:[],mcpSearchKeyword:"",selectedServersInOrder:[],nextOrder:1}},computed:{filteredMcpServers(){if(!this.mcpSearchKeyword)return this.mcpServers;const f=this.mcpSearchKeyword.toLowerCase();return this.mcpServers.filter(c=>c.name.toLowerCase().includes(f))},sortedSelectedServers(){return[...this.selectedServersInOrder].sort((f,c)=>f.order-c.order)}},watch:{mcpServerDialogVisible(f){f&&this.initSelectedMCPServers()}},created(){this.initData()},methods:{handleOpenDialog(){this.dialogTableVisible=!0},handleOpenMCPServerDialog(){this.fetchMCPServers(),this.mcpServerDialogVisible=!0},initSelectedMCPServers(){this.selectedServersInOrder=[],this.nextOrder=1,this.agentData.flowNodes&&this.agentData.flowNodes.length>0&&[...this.agentData.flowNodes].sort((c,s)=>c.numbering-s.numbering).forEach(c=>{this.selectedServersInOrder.push({id:c.mcpServerId,order:this.nextOrder++,description:c.description||""})})},isServerSelected(f){return this.selectedServersInOrder.some(c=>c.id===f)},getServerOrder(f){const c=this.selectedServersInOrder.find(s=>s.id===f);return c?c.order:""},getServerData(f){const c=this.selectedServersInOrder.find(s=>s.id===f);return c?("description"in c||(c.description=""),c):{description:""}},toggleServer(f,c){if(c){const s=this.mcpServers.find(x=>x.id===f);this.selectedServersInOrder.push({id:f,order:this.nextOrder++,description:s.description||""})}else{const s=this.selectedServersInOrder.findIndex(x=>x.id===f);s!==-1&&(this.selectedServersInOrder.splice(s,1),this.reorderSelectedServers())}},reorderSelectedServers(){this.selectedServersInOrder.sort((f,c)=>f.order-c.order),this.selectedServersInOrder.forEach((f,c)=>{f.order=c+1}),this.nextOrder=this.selectedServersInOrder.length+1},confirmMCPServerSelection(){const f=this.selectedServersInOrder.map((c,s)=>{const x=this.mcpServers.find(I=>I.id===c.id),y=this.agentData.flowNodes?this.agentData.flowNodes.find(I=>I.mcpServerId===c.id):null;return y?{...y,numbering:s,description:x.description||""}:{mcpServerId:c.id,mcpServerName:x.name,numbering:s,description:x.description||""}});this.agentData.flowNodes=f,this.mcpServerDialogVisible=!1},handleMCPServerSelected(f){const c=this.agentData.flowNodes?this.agentData.flowNodes.map(y=>y.mcpServerId):[],s=f.filter(y=>!c.includes(y.mcpServerId)),x=[...this.agentData.flowNodes||[],...s];this.agentData.flowNodes=x},initData(){this.getModels(),this.baseURL=window.location.origin+"/rag/api/agent/image/upload";const f=this.$route.query.id;this.agentData.id=typeof f=="string"&&parseInt(f,10)||0,this.agentData.id!==0&&this.loadAgentData()},async loadAgentData(){try{const f=await no.getAgentById(this.agentData.id);f.code===200&&(this.isLoadingFaBu=!1,this.isLoadingUpdate=!0,Object.assign(this.agentData,{similarity:f.data.similarity,recallCount:f.data.recallCount,name:f.data.name,description:f.data.description,imageUrl:f.data.url,modelId:f.data.modelId,knowledgeBaseIds:f.data.knowledgeBaseIds,selectedRows:f.data.knowledgeBaseList||[],rulePrompt:f.data.rulePrompt,rolePrompt:f.data.rolePrompt,responsePrompt:f.data.responsePrompt,groupId:f.data.groupId,flowNodes:f.data.flowNodes||[]}))}catch(f){console.error("加载Agent数据失败:",f)}},async getModels(){try{const f=await f_.getChatModels();f.code===200&&(this.modelOptions=f.data)}catch(f){console.error("获取模型列表失败:",f)}},updateAgentData(f){this.agentData={...this.agentData,...f}},handleSelectedData(f){this.agentData.selectedRows=f,this.agentData.knowledgeBaseIds=f.map(c=>c.id).join(";")},checkData(){const f=[{field:"name",message:"Agent名称为空，请填写！"},{field:"description",message:"描述为空，请填写！"},{field:"groupId",message:"工作组为空，请选择！"},{field:"recallCount",message:"召回次数为空，请填写！"},{field:"similarity",message:"相似度为空，请填写！"},{field:"modelId",message:"模型ID为空，请填写！"}];for(const{field:c,message:s}of f)if(!this.agentData[c])return this.$message.error(s),!1;return!0},async saveAgent(){this.checkData()&&this.$refs.agentConfigRef&&await this.$refs.agentConfigRef.createAgent()},async updateAgent(){this.checkData()&&this.$refs.agentConfigRef&&await this.$refs.agentConfigRef.updateAgent()},handleCreateSuccess(f){this.isLoadingUpdate=!0,this.isLoadingFaBu=!1,this.agentData.id=f},async fetchMCPServers(){try{const f=await c_.getListByGroup(this.agentData.groupId);f.code===200?this.mcpServers=f.data.map(c=>({id:c.id,name:c.name,description:c.description||""})):console.error("获取MCP服务器列表失败:",f.message)}catch(f){console.error("获取MCP服务器列表异常:",f)}},getMCPServerName(f){const c=this.mcpServers.find(s=>s.id===f);return c?c.name:`服务器 ${f}`}}},wv={class:"agent-app"},Sv={key:0,class:"selected-servers-display"},yv={class:"selected-servers-list"},Cv={class:"server-order-badge"},xv={class:"selected-server-name"},Av={class:"server-list-container"},Iv={class:"server-list-header"},bv={class:"server-count"},Pv={class:"server-checkbox"},Dv=["onClick"],Lv={key:0,class:"checkbox-number"},Rv={class:"server-info"},Ov={class:"server-name"},Ev={class:"dialog-footer"},Tv={class:"card-container"},Uv={class:"section-header"},Mv={class:"section-header"};function Bv(f,c,s,x,y,I){const P=he("agent-header"),U=he("agent-table"),$=qs,z=he("Search"),E=to,Q=Rr,K=t_,G=n_,T=r_,k=ut,j=he("UserFilled"),Je=he("agent-config"),rn=st,Pe=he("ViewIcon"),on=he("agent-preview");return ne(),de("div",wv,[m(P,{"is-loading-fa-bu":y.isLoadingFaBu,"is-loading-update":y.isLoadingUpdate,onSave:I.saveAgent,onUpdate:I.updateAgent},null,8,["is-loading-fa-bu","is-loading-update","onSave","onUpdate"]),m($,{modelValue:y.dialogTableVisible,"onUpdate:modelValue":c[0]||(c[0]=Z=>y.dialogTableVisible=Z),title:"知识库关联"},{default:S(()=>[m(U,{onUpdateValue:I.handleSelectedData,groupId:y.agentData.groupId},null,8,["onUpdateValue","groupId"])]),_:1},8,["modelValue"]),m($,{modelValue:y.mcpServerDialogVisible,"onUpdate:modelValue":c[3]||(c[3]=Z=>y.mcpServerDialogVisible=Z),title:"关联MCP服务器",width:"600px","destroy-on-close":""},{footer:S(()=>[D("div",Ev,[m(k,{onClick:c[2]||(c[2]=Z=>y.mcpServerDialogVisible=!1)},{default:S(()=>c[6]||(c[6]=[be("取消")])),_:1}),m(k,{type:"primary",onClick:I.confirmMCPServerSelection,disabled:y.selectedServersInOrder.length===0},{default:S(()=>c[7]||(c[7]=[be(" 确定 ")])),_:1},8,["onClick","disabled"])])]),default:S(()=>[m(G,null,{default:S(()=>[m(K,null,{default:S(()=>[m(Q,{modelValue:y.mcpSearchKeyword,"onUpdate:modelValue":c[1]||(c[1]=Z=>y.mcpSearchKeyword=Z),placeholder:"搜索 MCP Server",clearable:""},{prefix:S(()=>[m(E,null,{default:S(()=>[m(z)]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),y.selectedServersInOrder.length>0?(ne(),de("div",Sv,[c[4]||(c[4]=D("div",{class:"selected-servers-header"},[D("span",null,"已选执行顺序")],-1)),D("div",yv,[(ne(!0),de(Dr,null,Lr(I.sortedSelectedServers,Z=>(ne(),de("div",{key:Z.id,class:"selected-server-tag"},[D("span",Cv,Pn(Z.order),1),D("span",xv,Pn(I.getMCPServerName(Z.id)),1)]))),128))])])):ot("",!0),D("div",Av,[D("div",Iv,[c[5]||(c[5]=D("span",null,"可选 MCP Server （选择顺序决定执行顺序）",-1)),D("span",bv,Pn(I.filteredMcpServers.length)+"个",1)]),m(T,{height:"300px"},{default:S(()=>[(ne(!0),de(Dr,null,Lr(I.filteredMcpServers,Z=>(ne(),de("div",{class:"server-item",key:Z.id},[D("div",Pv,[D("div",{class:i_(["custom-order-checkbox",{"is-selected":I.isServerSelected(Z.id)}]),onClick:vn=>I.toggleServer(Z.id,!I.isServerSelected(Z.id))},[I.isServerSelected(Z.id)?(ne(),de("span",Lv,Pn(I.getServerOrder(Z.id)),1)):ot("",!0)],10,Dv),D("div",Rv,[D("span",Ov,Pn(Z.id)+" - "+Pn(Z.name)+" - "+Pn(Z.description),1)])])]))),128))]),_:1})])]),_:1},8,["modelValue"]),D("div",Tv,[m(rn,{class:"wide-card"},{header:S(()=>[D("span",Uv,[m(E,null,{default:S(()=>[m(j)]),_:1}),c[8]||(c[8]=be(" 应用设定 "))])]),default:S(()=>[m(Je,{baseURL:y.baseURL,"agent-data":y.agentData,"model-options":y.modelOptions,"dialog-visible":y.dialogTableVisible,"onUpdate:agentData":I.updateAgentData,onOpenAssociationDialog:I.handleOpenDialog,onOpenMCPServerDialog:I.handleOpenMCPServerDialog,onCreateSuccess:I.handleCreateSuccess,ref:"agentConfigRef"},null,8,["baseURL","agent-data","model-options","dialog-visible","onUpdate:agentData","onOpenAssociationDialog","onOpenMCPServerDialog","onCreateSuccess"])]),_:1}),m(rn,{class:"narrow-card"},{header:S(()=>[D("span",Mv,[m(E,null,{default:S(()=>[m(Pe)]),_:1}),c[9]||(c[9]=be(" 预览和调试 "))])]),default:S(()=>[m(on,{"agent-id":y.agentData.id},null,8,["agent-id"])]),_:1})])])}const t1=Xe(mv,[["render",Bv],["__scopeId","data-v-acf714b5"]]);export{t1 as default};
