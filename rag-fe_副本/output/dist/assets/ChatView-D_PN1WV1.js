/* empty css                  *//* empty css               */import{_ as I,I as D,C as N}from"./play-BrM_F_ME.js";/* empty css                *//* empty css                  *//* empty css                     */import{h as b,g as C,_ as k,q as y,o as g,a as l,w as h,m as v,n as E,T as $,d as x,U as B,F as T,t as O,c as Q,V as U,W as q,X as j,Y as W,G as F,Z as K,r as _}from"./index-LoRUQG_-.js";import{a as M}from"./AgentApi-CkSBsfcP.js";import{r as G}from"./renderMarkdown-C0qkyt2C.js";/* empty css                  */const L="lastSelectedAgent",R=b({name:"ChatInput",props:{isLoading:Boolean,options:{type:Array,default:()=>[]},modelValue:String},emits:["update:modelValue","submit"],data(){return{selectedValue:this.getLastSelectedAgent(),isOptionsLoaded:!1}},computed:{prompt:{get(){return this.modelValue},set(e){this.$emit("update:modelValue",e)}}},watch:{options:{immediate:!0,handler(e){e.length>0&&!this.isOptionsLoaded&&(this.isOptionsLoaded=!0,this.selectedValue=this.getLastSelectedAgent()),this.selectedValue&&!e.some(t=>t.value===this.selectedValue)&&(this.selectedValue="")}},selectedValue(e){e?localStorage.setItem(L,e):localStorage.removeItem(L)}},methods:{onSubmit(){this.selectedValue&&this.prompt.trim()?this.$emit("submit",{prompt:this.prompt,agentId:this.selectedValue}):C.error("请选择Agent并输入问题")},getLastSelectedAgent(){try{const e=localStorage.getItem(L);if(!e||!this.options.length)return console.log("No stored value or options not loaded"),"";const t=this.options.find(n=>String(n.value)===String(e));return t?t.value:(console.log("No matching option found"),"")}catch{return""}}}}),Y={class:"input-container"},z={class:"grid-content bg-purple-dark",style:{margin:"10px",display:"flex","justify-content":"flex-end","align-items":"center"}},H={key:0,src:I,height:"25px",width:"25px"};function J(e,t,n,c,d,f){const o=U,a=B,u=q,s=W,r=$,i=E,p=v;return g(),y("div",Y,[l(p,{justify:"center",style:{"margin-top":"20px"}},{default:h(()=>[l(i,{span:22,style:{"margin-left":"10px","border-radius":"12px"}},{default:h(()=>[l(r,{"body-style":{padding:"0px"},class:"fixed-input"},{default:h(()=>[x("div",z,[l(a,{modelValue:e.selectedValue,"onUpdate:modelValue":t[0]||(t[0]=m=>e.selectedValue=m),placeholder:"Agent",size:"large",style:{width:"150px"},disabled:e.isLoading},{default:h(()=>[(g(!0),y(T,null,O(e.options,m=>(g(),Q(o,{key:m.value,label:m.label,value:m.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"]),l(u,{modelValue:e.prompt,"onUpdate:modelValue":t[1]||(t[1]=m=>e.prompt=m),placeholder:"输入你的问题...",style:{height:"40px",border:"none !important",flex:"1","margin-left":"10px","margin-right":"10px"},onKeyup:j(e.onSubmit,["enter"]),disabled:e.isLoading},null,8,["modelValue","onKeyup","disabled"]),l(s,{link:"",onClick:e.onSubmit,style:{width:"40px"},loading:e.isLoading,disabled:!e.prompt.trim()||e.isLoading},{default:h(()=>[e.isLoading?F("",!0):(g(),y("img",H))]),_:1},8,["onClick","loading","disabled"])])]),_:1})]),_:1})]),_:1})])}const P=k(R,[["render",J],["__scopeId","data-v-b44c4780"]]),X=b({name:"ChatView",components:{ChatContainer:N,ChatInput:P,IndexAction:D},data(){return{prompt:"",isLoading:!1,currentQuestion:"",currentAnswer:"",conversations:[],options:[],selectedValue:"",docUrls:[],docLinks:[],rawBuffer:""}},async created(){this.getAgentData()},methods:{getAgentData(){const e={type:0,keyword:"",username:""};M.getAgent(e).then(t=>{t.code===200&&(this.options=t.data.map(n=>({value:n.id,label:n.name})))})},async startChat({prompt:e,agentId:t}){var n;this.isLoading=!0,this.currentQuestion=e,this.currentAnswer="",this.prompt="",this.docUrls=[],this.docLinks=[],this.rawBuffer="";try{const c=await fetch("/rag/api/agent/stream?stream=true",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:this.currentQuestion,agentId:t})});if(!c.ok)throw new Error("Network response was not ok");const d=(n=c.body)==null?void 0:n.getReader(),f=new TextDecoder("utf-8");if(d){let o="";for(;;){const{value:a,done:u}=await d.read();if(u)break;const s=f.decode(a,{stream:!0});o+=s;const r=o.split(`
`);o=r.pop()||"";for(const i of r)if(i.startsWith('data:"')){const p=i.substring(6,i.length-1);if(p.startsWith("docurl:")){const w=p.substring(7).split("-");if(w.length>=3){const A=w[0],V=w[1],S=w.slice(2).join("-").replace(/\\n/g,"").trim();this.docLinks.push({id:A,fileName:S,docId:V}),await this.updateDocumentsDisplay()}this.docUrls.push(p);continue}this.rawBuffer+=p,await this.updateDocumentsDisplay()}}if(o.startsWith('data:"')){const a=o.substring(6,o.length-1);if(a.startsWith("docurl:")){const s=a.substring(7).split("-");if(s.length>=3){const r=s[0],i=s[1],p=s.slice(2).join("-").replace(/\\n/g,"").trim();this.docLinks.push({id:r,fileName:p,docId:i}),await this.updateDocumentsDisplay()}this.docUrls.push(a)}else this.rawBuffer+=a,await this.updateDocumentsDisplay()}await this.updateDocumentsDisplay(!0),this.conversations.push({question:this.currentQuestion,answer:this.currentAnswer,docLinks:[]}),this.currentQuestion="",this.currentAnswer="",this.docLinks=[],this.rawBuffer=""}}catch(c){console.error("Error:",c),C.error("请求失败，请重试"),this.currentAnswer="请求失败，请重试"}finally{this.isLoading=!1}},setupDocumentListeners(){document.querySelectorAll(".reference-docs-header").forEach(e=>{e.getAttribute("data-has-click")||(e.setAttribute("data-has-click","true"),e.addEventListener("click",function(){const t=this.nextElementSibling,n=this.querySelector(".custom-arrow");t.style.display==="none"?(t.style.display="flex",n&&n.classList.remove("arrow-right"),n&&n.classList.add("arrow-down")):(t.style.display="none",n&&n.classList.remove("arrow-down"),n&&n.classList.add("arrow-right"))}))})},async updateDocumentsDisplay(e=!1){let t=await G(this.rawBuffer.replace(/\\"/g,'"').replace(/\\n/g,`
`));if(this.docLinks.length>0){const n=document.createElement("div");n.className="reference-docs-container",n.id="docs-container";const c=document.createElement("div");c.className="reference-docs-header";const d=document.createElement("span");d.className="reference-docs-title",d.textContent="参考文档 ";const f=document.createElement("span");f.className="custom-arrow arrow-down",d.appendChild(f),c.appendChild(d);const o=document.createElement("div");o.className="reference-docs-content",this.docLinks.forEach((a,u)=>{const s=document.createElement("div");s.className="doc-link-item";const r=document.createElement("span");r.textContent=`文档 ${u+1}： `;const i=document.createElement("a");i.href=`${window.location.origin}/#/knowledge/docs?id=${a.id}&docId=${a.docId}`,i.target="_blank",i.textContent=a.fileName,s.appendChild(r),s.appendChild(i),o.appendChild(s)}),n.appendChild(c),n.appendChild(o),this.currentAnswer=n.outerHTML+t}else this.currentAnswer=t;e||(await K(),this.setupDocumentListeners())}},mounted(){this.$nextTick(()=>{this.setupDocumentListeners();const e=new MutationObserver(()=>{this.setupDocumentListeners()}),t=document.querySelector(".total-css");t&&e.observe(t,{childList:!0,subtree:!0})})}}),Z={class:"content-home-page"};function ee(e,t,n,c,d,f){const o=_("indexAction"),a=E,u=v,s=_("ChatContainer"),r=_("ChatInput");return g(),y("div",Z,[l(s,{conversations:e.conversations,currentQuestion:e.currentQuestion,currentAnswer:e.currentAnswer},{empty:h(()=>[l(u,{style:{"min-height":"200px"}},{default:h(()=>[l(a,null,{default:h(()=>[l(o)]),_:1})]),_:1})]),_:1},8,["conversations","currentQuestion","currentAnswer"]),l(r,{modelValue:e.prompt,"onUpdate:modelValue":t[0]||(t[0]=i=>e.prompt=i),isLoading:e.isLoading,options:e.options,onSubmit:e.startChat},null,8,["modelValue","isLoading","options","onSubmit"])])}const ue=k(X,[["render",ee],["__scopeId","data-v-749f1de2"]]);export{ue as default};
