/* empty css                  *//* empty css                    */import{h as L,$ as k,i as ne,a0 as X,k as K,q as I,a,a1 as Z,w as n,g as U,o as m,a2 as x,W as ee,U as oe,F as q,t as A,Y as O,x as v,c as $,V as le,_ as S,a3 as re,G,a4 as se,a5 as de,a6 as ie,a7 as ue,y as pe,a8 as me,a9 as ge,B as D,d as B,f as ce,b as be,aa as J,j as ae,ab as fe,ac as Q,ad as ke,ae as ve,af as Y}from"./index-LoRUQG_-.js";/* empty css                     *//* empty css                  *//* empty css                     */import{g as j}from"./GroupApi-C0GuTlOK.js";/* empty css                   *//* empty css                      *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                   *//* empty css                  *//* empty css                   */import"./UserSelect.vue_vue_type_style_index_0_scoped_3500df00_lang-CqcJvsUu.js";import{d as H}from"./dict-Dy7cw003.js";import{m as we,b as M}from"./ModelApi-CqIWtrMp.js";const ye={class:"query-container"},_e=L({__name:"KnowledgeQueryForm",props:{query:{},workgroups:{},workgroupsLoading:{type:Boolean},tabActive:{}},emits:["search","create"],setup(E,{emit:C}){const p=E,c=k([]),y=k(!1),_=ne(()=>p.tabActive==="all"?c.value:p.workgroups);X(()=>p.tabActive,s=>{s==="all"&&f()},{immediate:!0});const f=async()=>{if(p.tabActive==="all"){y.value=!0;try{const s=await j.getAllGroupsList();s!=null&&s.data&&(c.value=s.data)}catch(s){console.error("获取所有工作组列表错误:",s),U.error("获取所有工作组列表失败")}finally{y.value=!1}}};return K(()=>{p.tabActive==="all"&&f()}),(s,o)=>{const r=ee,u=x,V=le,t=oe,l=O,w=Z;return m(),I("div",ye,[a(w,{class:"query-form",inline:""},{default:n(()=>[a(u,{label:"知识库名称:",class:"form-item-wide"},{default:n(()=>[a(r,{modelValue:s.query.name,"onUpdate:modelValue":o[0]||(o[0]=i=>s.query.name=i),placeholder:"根据知识库标题模糊查询",clearable:"",class:"wide-input"},null,8,["modelValue"])]),_:1}),a(u,{label:"工作组:",class:"form-item-wide"},{default:n(()=>[a(t,{modelValue:s.query.groupId,"onUpdate:modelValue":o[1]||(o[1]=i=>s.query.groupId=i),placeholder:"请选择工作组",clearable:"","loading-text":"加载中...",loading:p.tabActive==="all"?y.value:s.workgroupsLoading,class:"wide-input"},{default:n(()=>[(m(!0),I(q,null,A(_.value,i=>(m(),$(V,{key:i.id,label:`${i.id} - ${i.name} (${i.business})`,value:i.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1}),a(u,null,{default:n(()=>[a(l,{type:"primary",onClick:o[2]||(o[2]=i=>s.$emit("search"))},{default:n(()=>o[4]||(o[4]=[v("查询")])),_:1})]),_:1}),a(u,{class:"create-btn"},{default:n(()=>[a(l,{type:"primary",onClick:o[3]||(o[3]=i=>s.$emit("create"))},{default:n(()=>o[5]||(o[5]=[v("新建")])),_:1})]),_:1})]),_:1})])}}}),Ve=S(_e,[["__scopeId","data-v-50842cb4"]]),$e={class:"table-container"},he=L({__name:"KnowledgeTable",props:{tableData:{},loading:{type:Boolean},paging:{},workgroups:{}},emits:["viewDocs","edit","delete","update:page","update:size"],setup(E){const C=E,p=k([]),c=k(!1),y=async()=>{c.value=!0;try{const o=await j.getAllGroupsList();o!=null&&o.data&&(p.value=o.data)}catch(o){console.error("获取所有工作组列表错误:",o),U.error("获取所有工作组列表失败")}finally{c.value=!1}};K(()=>{y()});const _=o=>o?C.workgroups.some(r=>r.id===o):!1,f=o=>_(o.groupId),s=o=>{if(!o)return"-";let r=C.workgroups.find(u=>u.id===o);return!r&&p.value.length>0&&(r=p.value.find(u=>u.id===o)),r?`${r.id} - ${r.name} (${r.business})`:`${o}`};return(o,r)=>{const u=ue,V=O,t=me,l=de,w=ie,i=se;return m(),I("div",$e,[re((m(),$(l,{data:o.tableData,stripe:"","table-layout":"auto",style:{width:"100%"}},{default:n(()=>[a(u,{fixed:"",prop:"id",label:"ID",width:"80",align:"center"}),a(u,{prop:"name",label:"知识库名称",width:"200","show-overflow-tooltip":""}),a(u,{prop:"description",label:"知识库描述","show-overflow-tooltip":""}),a(u,{label:"工作组","show-overflow-tooltip":""},{default:n(({row:g})=>[v(pe(s(g.groupId)),1)]),_:1}),a(u,{prop:"createAt",label:"创建时间",width:"180",align:"center"}),a(u,{label:"操作",width:"200",align:"center",fixed:"right"},{default:n(({row:g})=>[a(V,{type:"primary",size:"small",onClick:b=>o.$emit("viewDocs",g.id),link:""},{default:n(()=>r[2]||(r[2]=[v("查看文档")])),_:2},1032,["onClick"]),f(g)?(m(),$(V,{key:0,type:"primary",size:"small",onClick:b=>o.$emit("edit",g),link:""},{default:n(()=>r[3]||(r[3]=[v(" 修改 ")])),_:2},1032,["onClick"])):G("",!0),f(g)?(m(),$(t,{key:1,title:"确认要删除该条记录吗?",onConfirm:b=>o.$emit("delete",g.id)},{reference:n(()=>[a(V,{type:"danger",size:"small",link:""},{default:n(()=>r[4]||(r[4]=[v("删除")])),_:1})]),_:2},1032,["onConfirm"])):G("",!0)]),_:1})]),_:1},8,["data"])),[[i,o.loading]]),o.paging.total>0?(m(),$(w,{key:0,"current-page":o.paging.page,"page-size":o.paging.size,total:o.paging.total,background:!0,layout:"prev, pager, next, jumper, total","onUpdate:currentPage":r[0]||(r[0]=g=>o.$emit("update:page",g)),"onUpdate:pageSize":r[1]||(r[1]=g=>o.$emit("update:size",g))},null,8,["current-page","page-size","total"])):G("",!0)])}}}),Ie=S(he,[["__scopeId","data-v-375b6d15"]]),Ce={class:"empty-workgroup"},Ee=L({__name:"KnowledgeDialog",props:{modelValue:{type:Boolean},title:{},book:{},models:{},workgroups:{},workgroupsLoading:{type:Boolean}},emits:["update:modelValue","save"],setup(E,{emit:C}){var V;const p=E,c=C,y=ae(),_=k(),f=k({...p.book,owner:Array.isArray(p.book.owner)?p.book.owner:((V=p.book.owner)==null?void 0:V.split(";").filter(Boolean))||[]});X(()=>p.book,t=>{f.value={...t,owner:Array.isArray(t.owner)?t.owner:t.owner?t.owner.split(";").filter(Boolean):[]}},{deep:!0});const s=()=>{localStorage.setItem("currentPath","/team/group"),y.push("/team/group").then(()=>{window.location.reload()}).catch(()=>{U.warning("跳转失败，请稍后重试")})},o={name:[{required:!0,max:10,message:"请输入知识库名称（不超过10个字）",trigger:"blur"}],description:[{required:!0,max:100,message:"请填写知识库描述",trigger:"blur"}],embeddingModelId:[{required:!0,message:"请选择大模型",trigger:"change"}],groupId:[{required:!0,type:"number",message:"请选择工作组",trigger:"change"}],parseId:[{required:!0,message:"请选择切词方式",trigger:"change"}],"embeddingRule.delimiter":[{required:!0,message:"请指定切词分隔符",trigger:"change"}],"embeddingRule.chunkTokenNum":[{required:!0,message:"请输入最大切片长度",trigger:"blur"},{pattern:/^\d+$/,message:"必须输入数字",trigger:"blur"}],owner:[{required:!0,validator:(t,l,w)=>{!l||l.length===0?w(new Error("请至少选择一名管理员")):w()},trigger:"change"}]},r=async()=>{try{await _.value.validate();const t=f.value.owner.join(";"),l={...f.value};l.owner=t,c("save",l),c("update:modelValue",!1)}catch{U.error("请检查表单填写是否正确")}},u=()=>{var t;(t=_.value)==null||t.resetFields(),c("update:modelValue",!1)};return(t,l)=>{const w=ee,i=x,g=le,b=oe,R=be,z=ce,F=fe,T=O,N=Z,W=ge;return m(),$(W,{"model-value":t.modelValue,title:t.title,width:"600",onClose:l[7]||(l[7]=e=>t.$emit("update:modelValue",!1)),"onUpdate:modelValue":l[8]||(l[8]=e=>t.$emit("update:modelValue",e))},{footer:n(()=>[a(T,{onClick:u},{default:n(()=>l[15]||(l[15]=[v("取消")])),_:1}),a(T,{type:"primary",onClick:r},{default:n(()=>l[16]||(l[16]=[v("保存")])),_:1})]),default:n(()=>[a(N,{ref_key:"formRef",ref:_,model:t.book,rules:o,"label-width":"140px"},{default:n(()=>[a(i,{label:"知识库名称:",prop:"name"},{default:n(()=>[a(w,{modelValue:t.book.name,"onUpdate:modelValue":l[0]||(l[0]=e=>t.book.name=e),placeholder:"请输入知识库名称",clearable:""},null,8,["modelValue"])]),_:1}),a(i,{label:"知识库描述:",prop:"description"},{default:n(()=>[a(w,{modelValue:t.book.description,"onUpdate:modelValue":l[1]||(l[1]=e=>t.book.description=e),placeholder:"输入知识库描述",clearable:""},null,8,["modelValue"])]),_:1}),a(i,{label:"选择大模型:",prop:"embeddingModelId"},{default:n(()=>[a(b,{modelValue:t.book.embeddingModelId,"onUpdate:modelValue":l[2]||(l[2]=e=>t.book.embeddingModelId=e),placeholder:"请选择大模型",clearable:""},{default:n(()=>[(m(!0),I(q,null,A(t.models,e=>(m(),$(g,{key:e.id,label:`${e.platform}-${e.name}`,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(i,{label:"切词类型:",prop:"parseId"},{default:n(()=>[a(b,{modelValue:t.book.parseId,"onUpdate:modelValue":l[3]||(l[3]=e=>t.book.parseId=e),placeholder:"请选择切词类型",clearable:""},{default:n(()=>[(m(!0),I(q,null,A(D(H).parserOptions,e=>(m(),$(g,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(i,{label:"切片分隔符",prop:"embeddingRule.delimiter"},{label:n(()=>[B("span",null,[l[9]||(l[9]=v(" 切片分隔符 ")),a(z,{effect:"light",placement:"top-start","show-after":300,content:"按照指定的标识符切分文本,可以有多个分割符"},{default:n(()=>[a(R,{class:"tooltip-icon"},{default:n(()=>[a(D(J))]),_:1})]),_:1}),l[10]||(l[10]=v(" : "))])]),default:n(()=>[a(b,{multiple:"",modelValue:t.book.embeddingRule.delimiter,"onUpdate:modelValue":l[4]||(l[4]=e=>t.book.embeddingRule.delimiter=e),placeholder:"切词分隔符，可以多个"},{default:n(()=>[(m(!0),I(q,null,A(D(H).delimiters,e=>(m(),$(F,{key:e.label,label:e.label},{default:n(()=>[(m(!0),I(q,null,A(e.options,d=>(m(),$(g,{key:d.value,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(i,{label:"最大切片长",prop:"embeddingRule.chunkTokenNum"},{label:n(()=>[B("span",null,[l[11]||(l[11]=v(" 最大切片长 ")),a(z,{effect:"light",placement:"top-start","show-after":300,content:"切片长度越大，召回的上下文越丰富。长度越小，召回的信息越精简"},{default:n(()=>[a(R,{class:"tooltip-icon"},{default:n(()=>[a(D(J))]),_:1})]),_:1}),l[12]||(l[12]=v(" : "))])]),default:n(()=>[a(w,{modelValue:t.book.embeddingRule.chunkTokenNum,"onUpdate:modelValue":l[5]||(l[5]=e=>t.book.embeddingRule.chunkTokenNum=e),placeholder:"最大切片长度",clearable:""},null,8,["modelValue"])]),_:1}),a(i,{label:"工作组:",prop:"groupId"},{default:n(()=>[a(b,{modelValue:t.book.groupId,"onUpdate:modelValue":l[6]||(l[6]=e=>t.book.groupId=e),placeholder:"请选择工作组",clearable:"","loading-text":"加载中...",loading:t.workgroupsLoading},{empty:n(()=>[B("div",Ce,[l[14]||(l[14]=B("span",null,"暂无工作组",-1)),a(T,{type:"primary",link:"",onClick:s},{default:n(()=>l[13]||(l[13]=[v("点击去加入工作组")])),_:1})])]),default:n(()=>[(m(!0),I(q,null,A(t.workgroups,e=>(m(),$(g,{key:e.id,label:`${e.id} - ${e.name} (${e.business})`,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["model-value","title"])}}}),qe=S(Ee,[["__scopeId","data-v-33907d13"]]),Ae={class:"app-workspace"},Ue=L({__name:"index",setup(E){const C=ae(),p=k(!1),c=k(!1),y=k(""),_=k("mine"),f=k([]),s=k(!1),o=Q({name:"",owner:"",page:1,size:10,type:1}),r=Q({page:1,size:10,total:0,items:[]}),u=k({name:"",description:"",owner:"",embeddingModelId:"",parseId:1,groupId:null,embeddingRule:{delimiter:[],chunkTokenNum:600}}),V=k([]);K(()=>{g(),b(),t()});const t=async()=>{s.value=!0;try{const e=await j.getJoinedList();f.value=(e==null?void 0:e.data)||[]}catch(e){console.error("获取工作组列表错误:",e),U.error("获取工作组列表失败")}finally{s.value=!1}},l=e=>{o.type=e.props.name==="all"?0:1,o.page=1,b()},w=e=>{o.page=e,b()},i=e=>{o.size=e,o.page=1,b()},g=async()=>{try{const e=await we.getEmbddingModels();e.code===200&&(V.value=e.data)}catch(e){console.error("Failed to fetch models:",e)}},b=async(e=o)=>{p.value=!0;try{const d=await M.getBooks(e);(d==null?void 0:d.code)===200&&Object.assign(r,d.data)}catch(d){console.error("Failed to fetch books:",d)}finally{p.value=!1}},R=()=>{o.page=1,b()},z=()=>{y.value="新建知识库",u.value={name:"",description:"",owner:Y.loginUser(),embeddingModelId:"",parseId:1,groupId:null,embeddingRule:{delimiter:[],chunkTokenNum:600}},c.value=!0},F=e=>{y.value="编辑知识库",u.value={...e,embeddingRule:typeof e.embeddingRule=="string"?JSON.parse(e.embeddingRule):e.embeddingRule},c.value=!0},T=async e=>{try{const h=await(e.id?M.edit:M.create)(e);(h==null?void 0:h.code)===200&&(b(),c.value=!1,U.success((h==null?void 0:h.message)||"操作成功"))}catch(d){console.error("Failed to save book:",d)}},N=e=>{C.push({path:"/knowledge/docs",query:{id:e}})},W=async e=>{try{const d=await M.remove({id:e,owner:Y.loginUser()});(d==null?void 0:d.code)===200&&b()}catch(d){console.error("Failed to delete book:",d)}};return(e,d)=>{const h=ve,te=ke;return m(),I("div",Ae,[a(te,{modelValue:_.value,"onUpdate:modelValue":d[0]||(d[0]=P=>_.value=P),class:"knowledge-tabs",onTabClick:l},{default:n(()=>[a(h,{label:"我的",name:"mine",lazy:!0}),a(h,{label:"全部",name:"all",lazy:!0})]),_:1},8,["modelValue"]),a(Ve,{query:o,workgroups:f.value,"workgroups-loading":s.value,"tab-active":_.value,onSearch:R,onCreate:z},null,8,["query","workgroups","workgroups-loading","tab-active"]),a(Ie,{"table-data":r.items,loading:p.value,paging:r,workgroups:f.value,onViewDocs:N,onEdit:F,onDelete:W,"onUpdate:page":w,"onUpdate:size":i},null,8,["table-data","loading","paging","workgroups"]),a(qe,{modelValue:c.value,"onUpdate:modelValue":d[1]||(d[1]=P=>c.value=P),title:y.value,book:u.value,models:V.value,workgroups:f.value,"workgroups-loading":s.value,onSave:T},null,8,["modelValue","title","book","models","workgroups","workgroups-loading"])])}}}),Qe=S(Ue,[["__scopeId","data-v-419c4339"]]);export{Qe as default};
