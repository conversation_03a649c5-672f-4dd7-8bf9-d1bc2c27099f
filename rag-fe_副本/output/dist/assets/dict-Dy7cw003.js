const l={delimiters:[{label:"中文",options:[{value:"。",label:"中文句号[。]"},{value:"！",label:"中文感叹符[！]"},{value:"？",label:"中文问号[？]"},{value:"；",label:"中文分号[；]"}]},{label:"英文",options:[{value:".",label:"英文句号[.]"},{value:"!",label:"英文感叹号[!]"},{value:"?",label:"英文问号[?]"},{value:";",label:"英文分号[;]"}]},{label:"其他",options:[{value:`
`,label:"换行符[\\n]"},{value:`

`,label:"换行符*2[\\n\\n]"}]}],parserOptions:[{label:"通用",value:1},{label:"不切词",value:2},{label:"excel表格",value:3},{label:"通用（增强）",value:4}]};export{l as d};
