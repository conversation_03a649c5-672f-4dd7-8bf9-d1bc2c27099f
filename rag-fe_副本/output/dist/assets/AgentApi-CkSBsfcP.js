var _=Object.defineProperty;var p=(e,t,R)=>t in e?_(e,t,{enumerable:!0,configurable:!0,writable:!0,value:R}):e[t]=R;var a=(e,t,R)=>p(e,typeof t!="symbol"?t+"":t,R);import{al as U}from"./index-LoRUQG_-.js";const n="/rag/api";var g=(e=>(e.QUERY_URL=n+"/agent/query",e.QUERY_PAGING_URL=n+"/agent/query/paging",e.SAVE_URL=n+"/agent/add",e.UPDATE_URL=n+"/agent/update",e.QUERY_BY_ID_URL=n+"/agent/detail",e))(g||{});class A{constructor(){a(this,"getAgent",t=>U.post(g.QUERY_URL,t));a(this,"getAgentPaging",t=>U.post(g.QUERY_PAGING_URL,t));a(this,"saveAgent",t=>U.post(g.SAVE_URL,t));a(this,"getAgentById",t=>U.get(`${g.QUERY_BY_ID_URL}?id=${t}`));a(this,"updateAgent",t=>U.put(g.UPDATE_URL,t))}}const s=new A;export{s as a};
