<!-- eslint-disable -->
<template>
	<!-- 显示如流用户群的提示框 -->
	<el-tooltip placement="bottom-end" effect="light" class="group-tooltip">
		<template #content>
			<!-- 提示框内容，包含群号和两种加入群聊的方式 -->
			<div class="tooltip-content">
				<div class="group-number">如流用户群：11088845</div>
				<div class="instruction">
					<span class="instruction-text">已加入用户：点击</span>
					<el-link type="primary" class="jump-link" :href="groupLink" target="_blank">
						<el-icon :size="20"><ChatDotRound /></el-icon>
					</el-link>
					<span class="instruction-text">直接跳转</span>
				</div>
				<div class="instruction">
					<span class="instruction-text">新用户：点击</span>
					<!-- eslint-disable-next-line -->
					<span class="copy-link" @click.stop="copyGroupNumber('11088845')">
						<!-- eslint-disable-next-line -->
						复制群号
					</span>
					<span class="instruction-text">后手动加入</span>
				</div>
			</div>
		</template>

		<!-- 显示群聊链接的图标 -->
		<!-- eslint-disable-next-line -->
		<el-link class="group-icon" type="primary" :href="groupLink" target="_blank">
			<el-icon :size="20"><ChatDotRound /></el-icon>
		</el-link>
	</el-tooltip>
</template>

<script setup>
import { ElMessage } from 'element-plus';

// 定义群聊链接
const groupLink = 'baidu://message/?gid=11088845';

/**
 * 将文本复制到剪贴板并提示用户
 *
 * @param text 要复制的文本
 */
const copyGroupNumber = (text) => {
	navigator.clipboard
		.writeText(text)
		.then(() => {
			ElMessage.success(`已复制群号：${text}，请手动打开如流加入群聊`);
		})
		.catch(() => {
			ElMessage.error(`复制失败，请手动记录群号：${text}`);
		});
};
</script>

<style scoped>
/* 提示框内容样式 */
.tooltip-content {
	line-height: 1.5;
	max-width: 220px;
}

/* 群号样式 */
.group-number {
	font-weight: 500;
	margin-bottom: 6px;
}

/* 指令文本样式 */
.instruction {
	font-size: 12px;
	color: var(--el-text-color-secondary);
	margin-top: 4px;
	display: flex;
	align-items: center;
}

/* 指令文本内部样式 */
.instruction-text {
	margin: 0 2px;
}

/* 跳转链接样式 */
.jump-link {
	margin: 0 2px;
	vertical-align: middle;
}

/* 复制链接样式 */
.copy-link {
	color: var(--el-color-primary);
	cursor: pointer;
	margin: 0 2px;
}

/* 群聊图标样式 */
.group-icon {
	margin-right: 10px;
}
</style>
