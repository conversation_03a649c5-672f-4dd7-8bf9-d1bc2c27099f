<template>
  <div class="logo">
    <el-image
        class="logo-img"
        :src="imagePath"
        @click="fullscreen"
    />
    <el-text class="logo-text">QE-RAG</el-text>
  </div>
</template>
<script lang="ts">
import {defineComponent} from 'vue';

export default defineComponent({
  data() {
    return {
      imagePath: new URL('@/assets/title.png', import.meta.url).href
    };
  },
  methods: {
    // 设置全屏
    fullscreen: function () {
      this.$emit('set-fullscreen', false);
    },
  }
});
</script>

<style scoped>

.logo {
  background-color: #001529;
  color: #e9eef3;
  height: 60px;
}

.logo-img {
  margin-left: 15px;
  margin-top: 18px;
  width: 30px;
  border-radius: 5px;
}

.logo-img:hover {
  cursor: pointer;
}

.logo-text {
  position: absolute;
  margin-top: 20px;
  margin-left: 10px;
  color: #e9eef3;
  font-size: 20px;
  font-weight: bold;
}

</style>
