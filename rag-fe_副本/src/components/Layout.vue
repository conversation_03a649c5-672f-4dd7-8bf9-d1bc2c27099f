<template>
  <div class="common-layout">
    <el-container>
      <el-aside class="alide" v-if="fullscreen">
        <i-logo @set-fullscreen="setFullscreen"></i-logo>
        <i-menu></i-menu>
      </el-aside>
      <el-container>
        <el-header class="header" v-if="fullscreen">
          <i-header></i-header>
        </el-header>
        <el-main class="main">
          <router-view></router-view>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>
<script lang="ts">
import IMenu from '@/components/IMenu.vue';
import IHeader from '@/components/IHeader/IHeader.vue';
import ILogo from '@/components/ILogo.vue';
import { defineComponent } from 'vue';

export default defineComponent({
  // eslint-disable-next-line vue/multi-word-component-names
  name: 'Layout',
  components: {
    IHeader,
    IMenu,
    ILogo,
  },
  data() {
    return {
      fullscreen: true,
    };
  },
  mounted() {
    window.addEventListener('keydown', this.cancelFullscreen);
  },
  setup() {},
  methods: {
    // 设置全屏
    setFullscreen(fullscreen) {
      this.fullscreen = fullscreen;
    },
    // 取消全屏
    cancelFullscreen(event) {
      if (event.key === 'Escape' || event.keyCode === 27) {
        this.fullscreen = true;
      }
    },
  },
});
</script>

<style scoped>
.header {
  position: relative;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  z-index: 1;
  padding: 0 10px 0 10px;
}

.alide {
  background-color: #001529;
  min-height: 100vh;
  color: #e9eef3;
  width: 220px;
}

.main {
  background-color: #e9eef3;
  padding: 0;
}
</style>
