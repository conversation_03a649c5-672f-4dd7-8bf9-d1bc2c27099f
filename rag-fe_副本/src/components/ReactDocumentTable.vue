<template>
  <div>
    <!-- React微应用容器 -->
    <div id="react-doc-table-container">
      <div id="react-doc-table-root"></div>
    </div>
  </div>
</template>

<script>
import { loadMicroApp } from 'qiankun';

export default {
  name: 'ReactDocumentTable',
  props: {
    book: Object,
    statuses: Array,
  },
  emits: ['getDocuments', 'getDocumentContent', 'openDrawer', 'statusSwitch', 'remove', 'routeChange', 'refresh'],
  data() {
    return {
      microApp: null,
    };
  },
  async mounted() {
    await this.loadReactApp();
  },
  beforeUnmount() {
    if (this.microApp) {
      this.microApp.unmount();
    }
  },
  watch: {
    // 监听props变化，更新微应用
    book: {
      handler() {
        this.updateMicroAppProps();
      },
      deep: true,
    },
    statuses: {
      handler() {
        this.updateMicroAppProps();
      },
      deep: true,
    },
  },
  methods: {
    async loadReactApp() {
      try {
        // 根据环境确定微应用入口
        const isDevelopment = import.meta.env.MODE === 'dev';
        const entry = isDevelopment
          ? 'http://localhost:3001'
          : `${window.location.origin}/micro-apps/react-doc-table/index.html`;

        this.microApp = loadMicroApp({
          name: 'react-doc-table',
          entry,
          container: '#react-doc-table-container',
          props: this.getMicroAppProps(),
        });

        console.log('React微应用加载成功');
      } catch (error) {
        console.error('React微应用加载失败:', error);
      }
    },

    getMicroAppProps() {
      return {
        book: this.book,
        statuses: this.statuses,
        // 路由变化回调
        onRouteChange: (docId) => {
          console.log('ReactDocumentTable: 接收到路由变化事件', docId);
          this.$emit('routeChange', docId);
        },
        onAddSuccess: () => {
          console.log('ReactDocumentTable: 文档添加成功');
        },
      };
    },

    async updateMicroAppProps() {
      if (this.microApp) {
        try {
          const props = this.getMicroAppProps();
          console.log('ReactDocumentTable: 更新微应用 props，items 长度:', props.items?.length);

          if (this.microApp.update) {
            this.microApp.update(props);
          } else {
            // 如果 update 方法不可用，重新挂载微应用
            console.log('ReactDocumentTable: update 方法不可用，重新挂载微应用');
            await this.microApp.unmount();
            await this.loadReactApp();
          }
        } catch (error) {
          console.error('ReactDocumentTable: 更新微应用失败:', error);
          // 重新加载微应用
          await this.loadReactApp();
        }
      } else {
        console.log('ReactDocumentTable: 微应用不存在，重新加载');
        await this.loadReactApp();
      }
    },
  },
};
</script>

<style scoped>
#react-doc-table-container {
  width: 100%;
  min-height: 400px;
}
</style>
