import axios from "axios";
import { ElMessage } from "element-plus";

// 全局开启跨域携带 Cookie
axios.defaults.withCredentials = true;

const http = axios.create({
    baseURL: '/',
    timeout: 5000,
    withCredentials: true, // 重要：允许跨域携带 cookie
    headers: {
        'Content-Type': 'application/json'
    }
});

// 添加请求拦截器
http.interceptors.request.use(
    (config) => {
        config.headers['originalUrl'] = window.location.origin;
        return config;
    },
    (err) => {
        return Promise.reject(err);
    }
);

// 添加响应拦截器
http.interceptors.response.use(
    (res) => {
        const data = res.data;
        if (data.code !== 200 && data.code !== 40001) {
            ElMessage.error(data['message']);
        }
        return data;
    },
    (err) => {
        ElMessage.error(err.response?.data?.message || `${err.response?.config?.url} 请求失败`);
        return Promise.reject(err);
    }
);

export default http;
