import { useUserStore } from '@/stores/user';

// 权限配置
const getGroupIdByDomain = () => {
    const currentUrl = window.location.href;
    if (currentUrl.startsWith('https://qerag.baidu-int.com')) {
        return 2;
    }
    return 18;
};

export const PERMISSION_CONFIG = {
    VALUE_MODEL: {
        // 价值模型-数据报表 权限配置
        groupId: getGroupIdByDomain(), // 动态工作组ID
        routes: ['/value-model'],
    },
};

/**
 * 检查用户是否有指定路由的访问权限
 * @param routePath 路由路径
 * @returns 是否有权限
 */
export function hasRoutePermission(routePath: string): boolean {
    const userStore = useUserStore();

    // 检查价值模型权限
    if (routePath.startsWith('/value-model')) {
        return userStore.hasGroupPermission(PERMISSION_CONFIG.VALUE_MODEL.groupId);
    }

    // 其他路由默认有权限
    return true;
}

/**
 * 获取用户有权限访问的路由列表
 * @param routes 所有路由配置
 * @returns 过滤后的路由列表
 */
export function getPermittedRoutes(routes: any[]): any[] {
    const userStore = useUserStore();

    return routes.filter((route) => {
        // 检查价值模型权限
        if (route.path === '/value-model') {
            return userStore.hasGroupPermission(PERMISSION_CONFIG.VALUE_MODEL.groupId);
        }

        return true;
    });
}

/**
 * 检查用户是否在指定工作组中
 * @param groupId 工作组ID
 * @returns 是否在工作组中
 */
export function hasGroupPermission(groupId: number): boolean {
    const userStore = useUserStore();
    return userStore.hasGroupPermission(groupId);
}
