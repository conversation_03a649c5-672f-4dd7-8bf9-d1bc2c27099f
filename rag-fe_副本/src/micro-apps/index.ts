import { registerMicroApps, start } from 'qiankun';

// 获取环境变量
const isDevelopment = import.meta.env.MODE === 'dev';

// 微应用配置
const microAppConfigs = [
  {
    name: 'react-doc-table',
    entry: isDevelopment
      ? 'http://localhost:3001'
      : `${window.location.origin}/micro-apps/react-doc-table/`,
    container: '#react-doc-table-container',
    activeRule: '/react-doc-table',
    props: {
      // 传递容器信息给子应用
      container: '#react-doc-table-container',
    },
  },
];

// 注册微应用
registerMicroApps(microAppConfigs, {
  beforeLoad: [
    app => {
      console.log('[LifeCycle] before load %c%s', 'color: green;', app.name);
      return Promise.resolve();
    },
  ],
  beforeMount: [
    app => {
      console.log('[LifeCycle] before mount %c%s', 'color: green;', app.name);
      return Promise.resolve();
    },
  ],
  afterUnmount: [
    app => {
      console.log('[LifeCycle] after unmount %c%s', 'color: green;', app.name);
      return Promise.resolve();
    },
  ],
});

// 启动qiankun - 修改沙箱配置
start({
  sandbox: {
    strictStyleIsolation: false, // 关闭严格样式隔离
    experimentalStyleIsolation: false, // 关闭实验性样式隔离
  },
});

export default {
  registerMicroApps,
  start,
};
