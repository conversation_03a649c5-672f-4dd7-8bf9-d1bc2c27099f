import http from '@/utils/http.ts';
import userApi from '@/api/UserApi.ts';

const BASEURL = '/rag/api';
// 枚举请求接口地址
enum API {
	QUERY_BY_USER_URL = BASEURL + '/knowledge/base/user/query',
	QUERY_BY_GROUP_URL = BASEURL + '/knowledge/base/get/in/group',
	GET_DETAIL_URL = BASEURL + '/knowledge/base/detail',
	PAGING_URL = BASEURL + '/knowledge/base/paging',
	DELETE_URL = BASEURL + '/knowledge/base/delete',
	UPDATE_URL = BASEURL + '/knowledge/base/update',
	ADD_URL = BASEURL + '/knowledge/base/add',
}

class BookApi {
	getBook(id) {
		return http.get(API.GET_DETAIL_URL + '?id=' + id);
	}

	/**
	 * 获取列表
	 */
	getBooks(query) {
		return http.post(API.PAGING_URL, query);
	}

	/**
	 * 删除
	 */
	remove(query) {
		return http.post(API.DELETE_URL, query);
	}

	edit(book) {
		return http.put(API.UPDATE_URL, book);
	}

	create(book) {
		// 发送POST请求，将书籍信息添加到服务器
		return http.post(API.ADD_URL, book);
	}
}

// export const getKDB = (userId: string) => http.get(`${API.QUERY_BY_USER_URL}?username=${userId}`);
export const getKDB = (groupId: number) => {
	return http.get(`${API.QUERY_BY_GROUP_URL}?groupId=${groupId ?? 0}`);
};

const bookApi: BookApi = new BookApi();
export default bookApi;
