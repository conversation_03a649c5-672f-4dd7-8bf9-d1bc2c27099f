import http from '@/utils/http.ts';

const BASEURL = '/rag/api';

enum API {
	UPLOAD_IMAGE_USER_URL = BASEURL + '/user/image/upload',
	UPDATE_USER_INFO = BASEURL + '/user/update',
}

class UploadImageApi {
	uploadImage(
		query: object = {
			name: '',
			hiImageUrl: '',
		},
	) {
		return http.post(API.UPDATE_USER_INFO, query);
	}

	uploadUserInfo() {
		return http.post(API.UPDATE_USER_INFO);
	}
}
const uploadImageApi: UploadImageApi = new UploadImageApi();
export default uploadImageApi;
