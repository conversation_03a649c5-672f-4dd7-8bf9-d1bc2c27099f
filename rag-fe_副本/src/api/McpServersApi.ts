import http from '@/utils/http.ts';
import type { McpServer } from '@/views/agent/mcpServers/components/types';

interface QueryParams {
	name?: string;
	groupId?: string;
	page?: string;
	size?: string;
}

interface DeleteParams {
	id: number | string;
}

const BASEURL = '/rag/api';
// 枚举请求接口地址
enum API {
	ADD_URL = BASEURL + '/mcpserver/add',
	DELETE_URL = BASEURL + '/mcpserver/delete',
	UPDATE_URL = BASEURL + '/mcpserver/update',
	PAGING_URL = BASEURL + '/mcpserver/paging',
	CURRENT_LIST_URL = BASEURL + '/mcpserver/current/list',
	GROUP_LIST_URL = BASEURL + '/mcpserver/get/list/in/group',
}

class McpServersApi {
	/**
	 * 添加mcp-server
	 */
	create(server: Partial<McpServer>) {
		return http.post(API.ADD_URL, server);
	}

	/**
	 * 删除mcp-server
	 */
	remove(query: DeleteParams) {
		return http.post(API.DELETE_URL, query);
	}

	/**
	 * 更新mcp-server
	 */
	edit(server: Partial<McpServer>) {
		return http.put(API.UPDATE_URL, server);
	}

	/**
	 * 分页查询mcp-server
	 */
	getServers(query: QueryParams) {
		return http.post(API.PAGING_URL, query);
	}


	/**
	 * 获取指定分组的MCP服务器列表
	 */
	getListByGroup = (groupId: number) => http.get(`${API.GROUP_LIST_URL}?groupId=${groupId}`);
}

const mcpServersApi = new McpServersApi();
export default mcpServersApi;
