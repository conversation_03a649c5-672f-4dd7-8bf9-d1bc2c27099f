import http from "@/utils/http.ts";
import type {queryModelParam} from "@/types/ModelTypes.ts";

const BASEURL = "/rag/api";
// 枚举请求接口地址
enum API {
    QUERY_URL = BASEURL + '/model/query',
}

class ModelApi {
    getEmbddingModels = () => {
        const data : queryModelParam = {
            isDelete: 0,
            type: 0,
        };
        return http.post(API.QUERY_URL, data);
    }

    getChatModels = () => {
        const data : queryModelParam = {
            isDelete: 0,
            type: 1,
        };
        return http.post(API.QUERY_URL, data);
    }
}

const modelApi: ModelApi = new ModelApi()
export default modelApi
