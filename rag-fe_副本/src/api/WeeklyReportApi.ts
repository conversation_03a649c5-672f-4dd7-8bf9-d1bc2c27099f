import http from '@/utils/http';

export interface WeeklyReportRequest {
    firstDayOfQuarter: string;
    currentDate: string;
}

export interface WeeklyReportDataItem {
    [key: string]: number;
}

export interface WeeklyReportData {
    userNum: {
        userNumPreQ: number;
        userNumCurWeek: number;
        userNumLastWeek: number;
        userNumTotal: number;
    };
    kbNum: {
        kbNumPreQ: number;
        kbNumCurWeek: number;
        kbNumLastWeek: number;
        kbNumTotal: number;
    };
    docNum: {
        docNumPreQ: number;
        docNumCurWeek: number;
        docNumLastWeek: number;
        docNumTotal: number;
    };
    agentNum: {
        agentNumPreQ: number;
        agentNumCurWeek: number;
        agentNumLastWeek: number;
        agentNumTotal: number;
    };
    taskNum: {
        taskNumPreQ: number;
        taskNumCurWeek: number;
        taskNumLastWeek: number;
    };
    inputTokenNum: {
        inputTokenNumPreQ: number;
        inputTokenNumCurWeek: number;
        inputTokenNumLastWeek: number;
    };
    outputTokenNum: {
        outputTokenNumPreQ: number;
        outputTokenNumCurWeek: number;
        outputTokenNumLastWeek: number;
    };
}

export interface WeeklyReportResponse {
    code: number;
    data: WeeklyReportData;
    message: string;
    timestamp: number;
}

const weeklyReportApi = {
    // 价值模型-周报数据
    getWeeklyReportData(params: WeeklyReportRequest): Promise<WeeklyReportResponse> {
        return http.post('rag/api/report/week', params, {
            timeout: 60000,
        });
    },
};

export default weeklyReportApi;
