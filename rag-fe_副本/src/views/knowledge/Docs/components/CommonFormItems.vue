<!-- eslint-disable -->
<template>
	<div>
		<!-- 文档标题 -->
		<el-form-item v-if="showFields.includes('title')" label="文档标题:" prop="title" label-width="120px">
			<el-input
				type="text"
				v-model="document.title"
				placeholder="请输入文档标题"
				clearable
				style="width: 700px"
			/>
		</el-form-item>

		<!-- URL链接 -->
		<el-form-item v-if="showFields.includes('sourceUrl')" label="URL 链接:" prop="sourceUrl" label-width="120px">
			<el-input
				type="text"
				v-model="document.sourceUrl"
				placeholder="文档的在线访问地址 URL"
				clearable
				style="width: 700px"
			/>
		</el-form-item>


		<!-- 选择大模型 -->
		<el-form-item
			v-if="showFields.includes('embeddingModelId')"
			label="切词模型:"
			prop="embeddingModelId"
			label-width="120px"
		>
			<el-select v-model="document.embeddingModelId" placeholder="请选择切词模型" style="width: 700px" clearable>
				<el-option
					v-for="item in models"
					:key="item.id"
					:label="`${item.platform}-${item.name}`"
					:value="item.id"
				/>
			</el-select>
		</el-form-item>

		<!-- 切词类型 -->
		<el-form-item v-if="showFields.includes('parseId')" label="切词类型:" prop="parseId" label-width="120px">
			<el-select v-model="document.parseId" placeholder="请选择切词类型" style="width: 700px" clearable>
				<el-option
					v-for="item in $options.dict.parserOptions"
					:key="item.value"
					:label="item.label"
					:value="item.value"
				/>
			</el-select>
		</el-form-item>

		<!-- 切片分隔符 -->
		<el-form-item
			v-if="showFields.includes('delimiter')"
			label="切片分隔符:"
			prop="embeddingRule.delimiter"
			label-width="120px"
		>
			<el-select
				multiple
				style="width: 700px"
				v-model="document.embeddingRule.delimiter"
				placeholder="切词分隔符，可以多个，置空表示不做切片"
			>
				<el-option-group v-for="group in $options.dict.delimiters" :key="group.label" :label="group.label">
					<el-option
						v-for="item in group.options"
						:key="item.value"
						:label="item.label"
						:value="item.value"
					/>
				</el-option-group>
			</el-select>
			<el-tooltip content="按照指定的标识符切分文本,可以有多个分割符" placement="top" effect="light">
				<el-icon style="margin-left: 5px">
					<InfoFilled />
				</el-icon>
			</el-tooltip>
		</el-form-item>

		<!-- 最大切片长 -->
		<el-form-item
			v-if="showFields.includes('chunkTokenNum')"
			label="最大切片长:"
			prop="embeddingRule.chunkTokenNum"
			label-width="120px"
		>
			<el-input
				type="text"
				v-model="document.embeddingRule.chunkTokenNum"
				placeholder="最大切片长度"
				clearable
				style="width: 700px"
			/>
			<el-tooltip
				content="切片长度越大，召回的上下文越丰富。长度越小，召回的信息越精简"
				placement="top"
				effect="light"
			>
				<el-icon style="margin-left: 5px">
					<InfoFilled />
				</el-icon>
			</el-tooltip>
		</el-form-item>

		<!-- 定时任务开关 -->
		<el-form-item v-if="showFields.includes('cronOpen')" label="定时任务:" prop="cronOpen" label-width="120px">
			<el-switch v-model="document.cronOpen" :active-value="1" :inactive-value="0" />
		</el-form-item>

		<!-- Cron表达式 -->
		<el-form-item 
			v-if="showFields.includes('cronExpression')" 
			label="Cron 表达式:" 
			prop="cronExpression" 
			label-width="120px"
			:required="document.cronOpen === 1"
		>
			<el-input
				type="text"
				v-model="document.cronExpression"
				placeholder="请输入Cron表达式，例如: 0 0 12 * * ?"
				clearable
				style="width: 700px"
			/>

			<el-tooltip content="Cron表达式用于设置定时任务执行时间，格式为: 秒 分 时 日 月 星期" placement="top" effect="light">
				<el-icon style="margin-left: 5px">
					<InfoFilled />
				</el-icon>
			</el-tooltip>
		</el-form-item>
	</div>
</template>

<script>
	import { InfoFilled } from '@element-plus/icons-vue';
	import dict from '@/utils/dict';

	export default {
		dict,
		props: {
			document: {
				type: Object,
				required: true,
			},
			models: {
				type: Array,
				default: () => [],
			},
			showFields: {
				type: Array,
				default: () => [
					'title',
					'sourceUrl',
					'embeddingModelId',
					'parseId',
					'delimiter',
					'chunkTokenNum',
					'cronOpen',
					'cronExpression',
				],
			},
		},
		data() {
			return {
				InfoFilled,
			};
		},
	};
</script>
