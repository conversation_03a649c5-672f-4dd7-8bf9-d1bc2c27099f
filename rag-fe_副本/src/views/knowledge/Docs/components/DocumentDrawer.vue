<!-- eslint-disable -->
<template>
	<el-drawer :model-value="show" @update:model-value="$emit('update:show', $event)" :title="title" size="70%">
		<el-form :model="document" ref="documentForm" :rules="activeTabRules" label-position="right">
			<el-tabs :model-value="tabActive" @update:model-value="handleTabChange">
				<!-- 文件上传标签页 -->
				<el-tab-pane label="文件上传" name="upload" :lazy="true"
					v-if="!document.type || !['link', 'virtual'].includes(document.type)">
					<el-form-item label="选择文件：" label-width="100px">
						<el-upload :file-list="files" action="" drag :http-request="handleUpload" :show-file-list="true"
							:multiple="true" :limit="5" :before-upload="handleBeforeUpload"
							:on-remove="handleBeforeRemove" ref="uploadRef">
							<el-icon class="el-icon--upload">
								<upload-filled />
							</el-icon>
							<div class="el-upload__text" style="width: 678px">
								<el-row>
									<el-col :span="24">
										将文档拖动至此处，或
										<em>点击上传</em>
									</el-col>
								</el-row>
								<span style="color: #e6a23c">
									单次上传文档数量为5个；单个文件小于50M；支持.doc | .txt | .docx | .pdf | .json |
									.excel
								</span>
							</div>
						</el-upload>
					</el-form-item>
					<!-- 引用公共表单部分 -->
					<CommonFormItems :document="document" :models="models" :validate-rules="activeTabRules"
						:show-fields="['embeddingModelId', 'parseId', 'delimiter', 'chunkTokenNum']" />
				</el-tab-pane>

				<!-- 在线采集标签页 -->
				<el-tab-pane label="知识库" name="link" :lazy="true" v-if="!document.type || document.type === 'link'">
					<common-form-items :document="document" :models="models" :validate-rules="activeTabRules"
						:show-fields="[
							'title',
							'sourceUrl',
							'embeddingModelId',
							'parseId',
							'delimiter',
							'chunkTokenNum',
							'cronOpen',
							'cronExpression',
						]" />
				</el-tab-pane>

				<!-- 新增虚拟文档标签页 -->
				<el-tab-pane label="虚拟文档" name="virtual" :lazy="true"
					v-if="!document.type || document.type === 'virtual'">
					<common-form-items :document="document" :models="models"
						:show-fields="['title', 'embeddingModelId']" />
				</el-tab-pane>
			</el-tabs>
		</el-form>

		<template #footer>
			<div class="dialog-footer">
				<el-button @click="$emit('update:show', false)">取消</el-button>
				<el-button type="primary" @click="validateAndSubmit">保存</el-button>
			</div>
		</template>
	</el-drawer>
</template>

<script>
import { InfoFilled, UploadFilled } from '@element-plus/icons-vue';
import dict from '@/utils/dict';
import CommonFormItems from './CommonFormItems.vue';
import { isValidCron } from 'cron-validator';

export default {
	components: {
		CommonFormItems,
	},
	dict,
	props: {
		show: Boolean,
		title: String,
		document: Object,
		files: Array,
		models: Array,
		tabActive: String,
	},
	emits: ['update:show', 'update:tabActive', 'uploadDocuments', 'beforeUpload', 'beforeRemove', 'editOrCreate'],
	data() {
		// cron表达式校验函数
		const validateCronExpression = (rule, value, callback) => {
			// 如果定时任务未开启，不验证
			if (this.document.cronOpen !== 1) {
				callback();
				return;
			}

			if (!value) {
				callback(new Error('请输入Cron表达式'));
				return;
			}

			// 使用cron-validator库验证Cron表达式
			const isOk = isValidCron(value, {
				// 启用秒字段支持
				seconds: true,
				// 启用月份和工作日的别名支持
				alias: true,
				// 允许使用?符号将天或工作日标记为空白
				allowBlankDay: true,
				// 支持数字7作为星期日
				allowSevenAsSunday: true,
			});

			if (!isOk) {
				callback(new Error('Cron表达式格式错误，请检查语法'));
			} else {
				callback();
			}
		};

		return {
			InfoFilled,
			UploadFilled,
			rules: {
				title: [{ required: true, message: '请输入文档标题', trigger: 'blur' }],
				sourceUrl: [{ required: true, message: '请输入文档链接', trigger: 'blur' }],
				embeddingModelId: [{ required: true, message: '请选择切词模型', trigger: 'change' }],
				parseId: [{ required: true, message: '请选择切词方式', trigger: 'change' }],
				'embeddingRule.delimiter': [{ required: true, message: '请指定切词分隔符', trigger: 'change' }],
				'embeddingRule.chunkTokenNum': [{ required: true, message: '请输入最大切片长度', trigger: 'blur' }],
				cronExpression: [{ validator: validateCronExpression, trigger: 'blur' }],
			},
		};
	},
	computed: {
		activeTabRules() {
			if (!this.tabActive) {
				return {};
			}

			const rules = {};
			const fieldRules = {
				upload: [
					'embeddingModelId',
					'parseId',
					'embeddingRule.delimiter',
					'embeddingRule.chunkTokenNum',
					'cronExpression',
				],
				link: [
					'title',
					'sourceUrl',
					'embeddingModelId',
					'parseId',
					'embeddingRule.delimiter',
					'embeddingRule.chunkTokenNum',
					'cronExpression',
				],
				virtual: ['title', 'embeddingModelId'],
			};

			fieldRules[this.tabActive]?.forEach((field) => {
				if (this.rules[field]) {
					rules[field] = this.rules[field];
				}
			});

			return rules;
		},
	},

	methods: {
		validateAndSubmit() {
			// 确保表单引用存在
			if (!this.$refs.documentForm) {
				console.error('表单引用未初始化');
				return;
			}

			// 保存原始规则
			const originalRules = { ...this.$refs.documentForm.rules };

			try {
				// 设置动态规则
				this.$refs.documentForm.rules = this.activeTabRules;
				this.$refs.documentForm.validate((valid) => {
					// 恢复原始规则
					this.$refs.documentForm.rules = originalRules;
					if (valid) {
						this.$emit('editOrCreate');
					} else {
						this.$message.error('请填写当前标签页的必填内容');
					}
				});
			} catch (error) {
				console.error('验证错误:', error);
				this.$refs.documentForm.rules = originalRules;
			}
		},
		handleTabChange(newTab) {
			console.log('Tab changed to:', newTab); // 调试用
			this.$emit('update:tabActive', newTab);

			// 如果需要根据tab切换重置表单数据
			if (newTab === 'virtual') {
				// this.resetFormForVirtual();
			}
		},
		handleBeforeUpload(file) {
			console.log('Before upload:', file.name);
			const isLt50M = file.size / 1024 / 1024 < 50;
			if (!isLt50M) {
				this.$message.error('上传文件大小不能超过50MB!');
				return false;
			}

			const allowedTypes = ['doc', 'docx', 'txt', 'pdf', 'json', 'xls', 'xlsx'];
			const fileExt = file.name.split('.').pop().toLowerCase();

			if (!allowedTypes.includes(fileExt)) {
				this.$message.error(`不支持的文件类型，请上传${allowedTypes.join(',')}格式`);
				return false;
			}

			this.$emit('beforeUpload', file);
			return true; // 必须返回true才会继续执行handleUpload
		},

		handleUpload(params) {
			console.log('Uploading:', params.file.name);
			const formData = new FormData();
			formData.append('file', params.file);
			formData.append('knowledgeBaseId', this.$route.query.id);

			this.$emit('uploadDocuments', {
				file: params.file,
				formData: formData,
				onProgress: params.onProgress,
			});
		},

		handleBeforeRemove(file) {
			this.$emit('beforeRemove', file);
		},
	},
};
</script>
