<!-- eslint-disable -->
<template>
	<div>
		<el-row class="table-container">
			<el-col :span="24">
				<el-table v-loading="loading" :data="items" stripe table-layout="auto">
					<el-table-column fixed prop="id" label="ID" width="80" />
					<el-table-column prop="title" label="文档名称" />
					<el-table-column prop="type" label="文档格式" width="100" />
					<el-table-column prop="owner" label="创建人" width="100" />
					<el-table-column prop="status" label="状态" width="100">
						<template v-slot="scope">
							<el-tag type="warning" v-if="scope.row.status === 5" size="small" effect="light">
								{{ documentStatusFormat(scope.row) }}
							</el-tag>
							<el-tag type="warning" v-if="scope.row.status === 1" size="small" effect="light">
								{{ documentStatusFormat(scope.row) }}
							</el-tag>
							<el-tag type="success" effect="dark" v-if="scope.row.status === 2" size="small">
								{{ documentStatusFormat(scope.row) }}
							</el-tag>
							<el-tag
								type="danger"
								v-if="scope.row.status === 3"
								style="cursor: pointer"
								effect="dark"
								@click="showMessage(scope.row)"
								size="small"
							>
								{{ documentStatusFormat(scope.row) }}
							</el-tag>
							<el-tag type="info" effect="dark" v-if="scope.row.status === 4" size="small">
								{{ documentStatusFormat(scope.row) }}
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column prop="createAt" label="创建时间" width="180" />
					<el-table-column label="操作" v-slot="scope" width="220">
						<el-button
							type="primary"
							size="small"
							@click="$emit('getDocumentContent', scope.row.id)"
							link
							v-if="scope.row.status === 2"
						>
							切片
						</el-button>
						<el-button
							type="primary"
							size="small"
							@click="$emit('openDrawer', scope.$index, scope.row)"
							link
							v-if="scope.row.status !== 4"
						>
							修改
						</el-button>
						<el-button
							type="info"
							size="small"
							link
							v-if="scope.row.status === 2"
							@click="$emit('statusSwitch', scope.row)"
						>
							禁用
						</el-button>
						<el-button
							type="success"
							size="small"
							link
							v-if="scope.row.status === 4"
							@click="$emit('statusSwitch', scope.row)"
						>
							启用
						</el-button>
						<el-popconfirm
							width="220"
							confirm-button-text="确认"
							cancel-button-text="取消"
							:icon="InfoFilled"
							title="确认要删除该条记录吗?"
							@confirm="$emit('remove', scope.row.id)"
						>
							<template #reference>
								<el-button type="danger" size="small" link>删除</el-button>
							</template>
						</el-popconfirm>
					</el-table-column>
				</el-table>
			</el-col>
		</el-row>
		<el-row justify="center">
			<el-pagination
				v-model:current-page="query.page"
				v-model:page-size="query.size"
				@change="$emit('getDocuments')"
				background
				layout="prev, pager, next, jumper, total"
				:total="total"
			/>
		</el-row>
	</div>
</template>

<script>
import { InfoFilled } from '@element-plus/icons-vue';

export default {
	props: {
		loading: Boolean,
		items: Array,
		total: Number,
		query: Object,
		statuses: Array,
	},
	data() {
		return {
			InfoFilled,
		};
	},
	methods: {
		documentStatusFormat(row) {
			for (const item of this.statuses) {
				if (item.key === row.status) {
					return item.value;
				}
			}
			return '';
		},
		showMessage(document) {
			this.$message.error(`失败原因： ${document.message}`);
		},
	},
};
</script>
