<!-- eslint-disable -->
<template>
	<el-drawer
		class="slices-container"
		:model-value="show"
		@update:model-value="$emit('update:show', $event)"
		title="文档切片"
		size="60%"
	>
		<el-row class="table-container">
			<el-col 
				:span="24" 
				v-for="(item, index) in renderedSlices" 
				:key="index" 
				style="margin-bottom: 5px">
				<el-card>
					<!--bca-disable-next-line-->
					<div v-html="item.renderedContent"></div>
				</el-card>
			</el-col>
		</el-row>
	</el-drawer>
</template>

<script>
	import { renderMarkdown } from '@/utils/renderMarkdown';

	export default {
		props: {
			show: Boolean,
			documentSlices: Array,
		},
		data() {
			return {
				renderedSlices: [], // 存储渲染后的内容
			};
		},
		watch: {
			documentSlices: {
				immediate: true,
				async handler(newVal) {
					if (newVal && newVal.length) {
						// 并行处理所有markdown渲染
						const renderingPromises = newVal.map(async (item) => {
							return {
								...item,
								renderedContent: await renderMarkdown(item.content),
							};
						});

						this.renderedSlices = await Promise.all(renderingPromises);
					} else {
						this.renderedSlices = [];
					}
				},
			},
		},
		emits: ['update:show'],
	};
</script>

<style scoped>
	/* 确保markdown内容样式正确 */
	.el-card >>> pre {
		background: #f6f8fa;
		padding: 16px;
		border-radius: 3px;
	}

	.el-card >>> code {
		font-family: monospace;
	}
</style>
