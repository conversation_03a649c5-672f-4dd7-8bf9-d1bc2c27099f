export interface QueryParams {
	name: string;
	groupId?: number;
	page: number;
	size: number;
	type?: number; // 0---查询全部；1--查询个人
	owner?: string;
}

export interface Paging {
	page: number;
	size: number;
	total: number;
	items: Book[];
}

export interface Book {
	id?: number;
	name: string;
	description: string;
	// owner: Array<string>;
	owner: string;
	createAt?: string;
	embeddingModelId: string | null;
	parseId: number;
	groupId: number | null;
	embeddingRule:
		| {
				delimiter: string[];
				chunkTokenNum: number;
		  }
		| string;
}

export interface Model {
	id: string;
	platform: string;
	name: string;
}
