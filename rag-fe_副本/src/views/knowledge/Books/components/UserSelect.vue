<template>
	<el-select
		v-model="selectedOwners"
		:multiple="mode === 'multiple'"
		filterable
		remote
		reserve-keyword
		:placeholder="placeholderText"
		:remote-method="searchUsers"
		:loading="searchLoading"
		ref="selectRef"
		@change="updateValue"
		@visible-change="handleDropdownVisible"
		@focus="handleFocus"
	>
		<el-option
			v-for="user in userOptions"
			:key="`${user.name}-${user.departmentName}`"
			:label="user.name"
			:value="user.name"
		>
			<span>{{ user.name }}</span>
			<span style="float: right; color: #8492a6; font-size: 13px">
				{{ user.departmentName || '未知' }}
			</span>
		</el-option>
	</el-select>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import UserSelectApi from '@/api/UserSelect.ts';
const props = defineProps({
	modelValue: {
		type: [String, Array],
		default: () => [],
	},
	mode: {
		type: String,
		default: 'multiple',
		validator: (value) => ['multiple', 'single'].includes(value),
	},
	placeholder: {
		type: String,
		default: '',
	},
});

const emit = defineEmits(['update:modelValue', 'change']);

const placeholderText = computed(
	() => props.placeholder || (props.mode === 'multiple' ? '指定知识库管理员，输入用户名搜索' : '请选择管理员')
);

const selectedOwners = ref(props.mode === 'multiple' ? [] : '');
const userOptions = ref([]);
const searchLoading = ref(false);
const selectRef = ref(null);
let timeout = null;

// 获取用户数据
const fetchUsers = async (name) => {
	try {
		const response = await UserSelectApi.getUser({ name });
		return response.data || [];
	} catch (error) {
		console.error('搜索用户失败:', error);
		return [];
	}
};

// 更新值并触发事件
const updateValue = () => {
	const value = props.mode === 'multiple' ? [...selectedOwners.value] : selectedOwners.value;

	emit('update:modelValue', value);
	emit('change', value);
};

// 加载初始用户数据
const loadInitialUsers = async () => {
	const initialNames =
		props.mode === 'multiple'
			? Array.isArray(props.modelValue)
				? props.modelValue
				: []
			: props.modelValue
			? [props.modelValue]
			: [];

	if (initialNames.length > 0) {
		searchLoading.value = true;
		try {
			const users = await Promise.all(initialNames.map((name) => fetchUsers(name)));
			userOptions.value = users.flat().filter((u) => u && u.name);
		} catch (error) {
			console.error('初始化用户数据失败:', error);
		} finally {
			searchLoading.value = false;
		}
	}
};

const handleFocus = () => {
	console.log('Select focused, current options:', {
		userOptions: userOptions.value,
		selected: selectedOwners.value,
	});
};

// 修改后的搜索方法
const searchUsers = async (query) => {
	const trimmedQuery = query.trim();

	if (trimmedQuery === '') {
		userOptions.value = [];
		return;
	}

	searchLoading.value = true;
	clearTimeout(timeout);

	timeout = setTimeout(async () => {
		try {
			const users = await fetchUsers(trimmedQuery);

			// 确保数据结构正确
			const validUsers = users
				.map((user) => ({
					name: String(user.name || ''),
					departmentName: String(user.departmentName || '未知'),
				}))
				.filter((user) => user.name);

			userOptions.value = validUsers;
		} catch (error) {
			userOptions.value = [];
		} finally {
			searchLoading.value = false;
		}
	}, 500);
};

// 当下拉框显示时，如果选项为空且有已选值，则加载已选用户
const handleDropdownVisible = (visible) => {
	if (visible && userOptions.value.length === 0 && selectedOwners.value) {
		loadInitialUsers();
	}
};

// 监听 modelValue 变化
watch(
	() => props.modelValue,
	(newVal) => {
		if (props.mode === 'multiple') {
			selectedOwners.value = Array.isArray(newVal) ? newVal : [];
		} else {
			selectedOwners.value = typeof newVal === 'string' ? newVal : '';
		}
	},
	{ immediate: true }
);

// 组件挂载时加载初始数据
onMounted(() => {
	loadInitialUsers();
});

// 暴露方法
const setUserOptions = (users) => {
	userOptions.value = Array.isArray(users) ? users : [];
};

defineExpose({ setUserOptions });
</script>

<style scoped>
/* 多选标签样式 */
:deep(.el-select__tags) {
	white-space: nowrap;
	overflow-x: auto;
	max-width: 380px;
}

/* 选项列表样式 */
:deep(.el-select-dropdown__item) {
	display: flex;
	justify-content: space-between;
}
</style>
