<!-- eslint-disable -->
<template>
	<el-dialog
		:model-value="modelValue"
		:title="title"
		width="600"
		@close="$emit('update:modelValue', false)"
		@update:modelValue="$emit('update:modelValue', $event)"
	>
		<el-form ref="formRef" :model="book" :rules="rules" label-width="140px">
			<el-form-item label="知识库名称:" prop="name">
				<el-input v-model="book.name" placeholder="请输入知识库名称" clearable />
			</el-form-item>
			<el-form-item label="知识库描述:" prop="description">
				<el-input v-model="book.description" placeholder="输入知识库描述" clearable />
			</el-form-item>
			<el-form-item label="切词模型:" prop="embeddingModelId">
				<el-select v-model="book.embeddingModelId" placeholder="请选择切词模型" clearable>
					<el-option
						v-for="item in models"
						:key="item.id"
						:label="`${item.platform}-${item.name}`"
						:value="item.id"
					/>
				</el-select>
			</el-form-item>
			<el-form-item label="切词类型:" prop="parseId">
				<el-select v-model="book.parseId" placeholder="请选择切词类型" clearable>
					<el-option
						v-for="item in dict.parserOptions"
						:key="item.value"
						:label="item.label"
						:value="item.value"
					/>
				</el-select>
			</el-form-item>
			<el-form-item label="切片分隔符" prop="embeddingRule.delimiter">
				<template #label>
					<span>
						切片分隔符
						<el-tooltip
							effect="light"
							placement="top-start"
							:show-after="300"
							content="按照指定的标识符切分文本,可以有多个分割符"
						>
							<el-icon class="tooltip-icon"><InfoFilled /></el-icon>
						</el-tooltip>
						:
					</span>
				</template>
				<el-select multiple v-model="book.embeddingRule.delimiter" placeholder="切词分隔符，可以多个">
					<el-option-group v-for="group in dict.delimiters" :key="group.label" :label="group.label">
						<el-option
							v-for="item in group.options"
							:key="item.value"
							:label="item.label"
							:value="item.value"
						/>
					</el-option-group>
				</el-select>
			</el-form-item>

			<el-form-item label="最大切片长" prop="embeddingRule.chunkTokenNum">
				<template #label>
					<span>
						最大切片长
						<el-tooltip
							effect="light"
							placement="top-start"
							:show-after="300"
							content="切片长度越大，召回的上下文越丰富。长度越小，召回的信息越精简"
						>
							<el-icon class="tooltip-icon"><InfoFilled /></el-icon>
						</el-tooltip>
						:
					</span>
				</template>
				<el-input v-model="book.embeddingRule.chunkTokenNum" placeholder="最大切片长度" clearable />
			</el-form-item>

			<!-- <el-form-item label="知识管理员" prop="owner" :key="formData.owner.length">
				<template #label>
					<span class="form-label">
						知识管理员
						<el-tooltip
							effect="light"
							placement="top-start"
							:show-after="300"
							content="可多选用户作为知识库管理员"
						>
							<el-icon class="tooltip-icon"><InfoFilled /></el-icon>
						</el-tooltip>
						:
					</span>
				</template>
				<UserSelect v-model="formData.owner" @change="handleOwnersChange" style="width: 100%" />
			</el-form-item> -->
			<el-form-item label="工作组:" prop="groupId">
				<el-select v-model="book.groupId" placeholder="请选择工作组" clearable loading-text="加载中..." :loading="workgroupsLoading">
					<el-option 
						v-for="item in workgroups" 
						:key="item.id" 
						:label="`${item.id} - ${item.name} (${item.business})`" 
						:value="item.id" 
					/>
					<template #empty>
						<div class="empty-workgroup">
							<span>暂无工作组</span>
							<el-button type="primary" link @click="goToCreateWorkgroup">点击去加入工作组</el-button>
						</div>
					</template>
				</el-select>
			</el-form-item>
		</el-form>

		<template #footer>
			<el-button @click="handleClose">取消</el-button>
			<el-button type="primary" @click="handleSubmit">保存</el-button>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import UserSelect from '@/views/knowledge/Books/components/UserSelect.vue';
import groupApi, { type Workgroup } from '@/api/GroupApi';
import type { Book } from './types';
import dict from '@/utils/dict';
import { InfoFilled } from '@element-plus/icons-vue';
import { useRouter } from 'vue-router';

const props = defineProps<{
	modelValue: boolean;
	title: string;
	book: Book;
	models: any[];
	workgroups?: any[];
	workgroupsLoading?: boolean;
}>();

const emit = defineEmits(['update:modelValue', 'save']);
const router = useRouter();

const formRef = ref();
// 表单数据（创建副本避免直接修改props）
const formData = ref<Book>({
	...props.book,
	owner: Array.isArray(props.book.owner) ? props.book.owner : props.book.owner?.split(';').filter(Boolean) || [],
});
// 监听props变化更新表单数据
watch(
	() => props.book,
	(newVal) => {
		formData.value = {
			...newVal,
			owner: Array.isArray(newVal.owner)
				? newVal.owner
				: newVal.owner
				? newVal.owner.split(';').filter(Boolean)
				: [],
		};
	},
	{ deep: true }
);

// 用户选择处理
const handleOwnersChange = (users: string[]) => {
	formData.value.owner = users;
	// 手动触发验证
	formRef.value.validateField('owner');
};

// 跳转 团队信息
const goToCreateWorkgroup = () => {
	localStorage.setItem('currentPath', '/team/group');
	// 跳转到团队信息页面
	router.push('/team/group').then(() => {
		window.location.reload();
	}).catch(() => {
		ElMessage.warning('跳转失败，请稍后重试');
	});
};

const rules = {
	name: [{ required: true, max: 10, message: '请输入知识库名称（不超过10个字）', trigger: 'blur' }],
	description: [{ required: true, max: 100, message: '请填写知识库描述', trigger: 'blur' }],
	embeddingModelId: [{ required: true, message: '请选择切词模型', trigger: 'change' }],
	groupId: [{ required: true, type: 'number', message: '请选择工作组', trigger: 'change' }],
	parseId: [{ required: true, message: '请选择切词方式', trigger: 'change' }],
	'embeddingRule.delimiter': [{ required: true, message: '请指定切词分隔符', trigger: 'change' }],
	'embeddingRule.chunkTokenNum': [
		{ required: true, message: '请输入最大切片长度', trigger: 'blur' },
		{ pattern: /^\d+$/, message: '必须输入数字', trigger: 'blur' },
	],
	owner: [
		{
			required: true,
			validator: (_, value, callback) => {
				if (!value || value.length === 0) {
					callback(new Error('请至少选择一名管理员'));
				} else {
					callback();
				}
			},
			trigger: 'change',
		},
	],
};

// 提交处理
const handleSubmit = async () => {
	try {
		await formRef.value.validate();
		// 准备提交数据（将owners数组转换回字符串）
		const ownerString = formData.value.owner.join(';');
		const submitData = {
			...formData.value,
		};
		submitData.owner = ownerString;
		emit('save', submitData);
		emit('update:modelValue', false);
	} catch (error) {
		ElMessage.error('请检查表单填写是否正确');
	}
};

const handleClose = () => {
	formRef.value?.resetFields();
	emit('update:modelValue', false);
};
</script>

<style scoped>
.el-form-item {
	margin-bottom: 20px;
}

.el-select,
.el-input {
	width: 100%;
}

.tooltip-icon {
	margin-left: 4px;
	color: #909399;
	cursor: pointer;
	vertical-align: middle;
	transition: color 0.2s;
}

.tooltip-icon:hover {
	color: #409eff;
}

/* 自定义 tooltip 样式 */
:deep(.el-tooltip__popper) {
	max-width: 300px;
	line-height: 1.5;
	padding: 10px;
	font-size: 13px;
}

:deep(.el-tooltip__popper.is-light) {
	border: 1px solid #ebeef5;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.empty-workgroup {
	padding: 12px 0;
	text-align: center;
}

.empty-workgroup span {
	display: block;
	margin-bottom: 8px;
	color: #909399;
}

.form-label {
	display: inline-flex;
	align-items: center;
}
</style>
