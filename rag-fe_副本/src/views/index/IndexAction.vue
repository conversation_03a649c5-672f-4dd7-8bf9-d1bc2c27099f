<template>
  <div class="demo-1">
    <main>
      <div class="content">
        <div class="slideshow">
          <div class="slide">
            <div class="slide__bg"></div>
            <h2 class="word">QE-RAG</h2>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script lang="ts">
import './wordFx';
import charming from 'charming';
import './wordFx';

window.charming = charming; // 确保 window.charming 可用

export default {
  name: 'indexAction',
  mounted() {
    const effects = [
      {
        options: {
          shapeColors: [
            '#35c394', '#9985ee', '#f54665', '#4718f5', '#f5aa18',
            '#ec4747', '#5447ec', '#ecb447', '#a847ec', '#635f65',
            '#6435ea', '#295ddc', '#9fd7ff', '#d65380', '#0228f7', '#242627'
          ],
          shapesOnTop: true
        },
        show: {
          lettersAnimationOpts: {
            duration: 800,
            delay: () => anime.random(0, 75),
            easing: 'easeInOutExpo',
            opacity: [0, 1],
            translateY: ['-300%', '0%'],
            rotate: () => [anime.random(-50, 50), 0]
          },
          shapesAnimationOpts: {
            duration: () => anime.random(1000, 3000),
            delay: (t, i) => i * 20,
            easing: 'easeOutElastic',
            translateX: t => {
              const tx = anime.random(-400, 400);
              t.dataset.tx = tx;
              return [0, tx];
            },
            translateY: t => {
              const ty = anime.random(-250, 250);
              t.dataset.ty = ty;
              return [0, ty];
            },
            scale: t => {
              const s = randomBetween(0.1, 0.6);
              t.dataset.s = s;
              return [s, s];
            },
            rotate: () => anime.random(-90, 90),
            opacity: {
              value: () => randomBetween(0.3, 0.6),
              duration: 1000,
              easing: 'linear'
            }
          }
        }
      },
    ];

    class Slideshow {
      constructor(el) {
        this.DOM = {};
        this.DOM.el = el;
        this.DOM.slides = Array.from(this.DOM.el.querySelectorAll('.slide'));
        this.DOM.bgs = Array.from(this.DOM.el.querySelectorAll('.slide__bg'));
        this.DOM.words = Array.from(this.DOM.el.querySelectorAll('.word'));
        this.current = 0;
        this.words = [];
        this.DOM.words.forEach((word, pos) => {
          this.words.push(new Word(word, effects[pos].options));
        });

        this.isAnimating = true;
        this.words[0].show(effects[0].show).then(() => this.isAnimating = false);
      }

      show() {
        if (this.isAnimating) {
          return;
        }
        this.isAnimating = true;
        this.DOM.slides[0].style.opacity = 1;
        anime({
          targets: this.DOM.bgs[0],
          duration: 600,
          easing: [0.2, 1, 0.3, 1],
          complete: () => {
            this.words[0].show(effects[0].show).then(() => this.isAnimating = false);
          }
        });
      }
    }

    new Slideshow(document.querySelector('.slideshow'));
  }
};
</script>

<style scoped lang="less">
@import "./base.less";
</style>
