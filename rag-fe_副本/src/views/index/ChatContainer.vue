<template>
	<div class="total-css" ref="container">
		<template v-if="conversations.length === 0 && !currentQuestion">
			<slot name="empty">
				<indexAction />
			</slot>
		</template>

		<template v-else>
			<ChatHistory v-for="(conv, index) in conversations" :key="'conv-' + index" :conversation="conv" />

			<div v-if="currentQuestion" class="conversation-item">
				<ChatMessage :isUser="true" :content="currentQuestion" />
				<ChatMessage v-if="currentAnswer" :isUser="false" :content="currentAnswer" />
			</div>
		</template>
	</div>
</template>

<script lang="ts">
import { defineComponent, ref, watch, nextTick } from 'vue';
import ChatHistory from '@/views/index/ChatHistory.vue';
import ChatMessage from '@/views/index/ChatMessage.vue';
import IndexAction from '@/views/index/IndexAction.vue';

export default defineComponent({
	name: 'ChatContainer',
	components: { ChatHistory, ChatMessage, IndexAction },
	props: {
		conversations: {
			type: Array as () => Array<{ question: string; answer: string; docLinks?: Array<{ id: string; fileName: string }> }>,
			default: () => [],
		},
		currentQuestion: String,
		currentAnswer: String,
	},
	setup(props) {
		const container = ref<HTMLElement | null>(null);
		const scrollToBottom = () => {
			nextTick(() => {
				if (container.value) {
					container.value.scrollTop = container.value.scrollHeight;
				}
			});
		};

		// 监听内容变化自动滚动
		watch(() => [props.conversations, props.currentAnswer], scrollToBottom, {
			deep: true,
		});

		return { container };
	},
});
</script>

<style scoped>
.total-css {
	border-radius: 10px;
	max-height: calc(100vh - 200px);
	overflow-y: auto;
	margin-bottom: 80px;
	padding: 10px;
	scroll-behavior: smooth;
	scrollbar-width: none;
	-ms-overflow-style: none;
}

.total-css::-webkit-scrollbar {
	display: none;
}

.conversation-item {
	overflow-y: auto;
}
</style>
