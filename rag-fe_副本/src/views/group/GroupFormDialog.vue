<template>
	<el-dialog
		:model-value="visible"
		@update:model-value="(val) => emits('update:visible', val)"
		:title="dialogTitle"
		width="50%"
	>
		<el-form :model="formData" label-width="100px" :rules="rules" ref="formRef">
			<el-form-item label="名称" prop="name">
				<el-input v-model="formData.name" placeholder="请群组名称" />
			</el-form-item>
			<el-form-item label="创建者">
				<el-tag>{{ formData.createUser }}</el-tag>
			</el-form-item>
			<el-form-item label="事业群" prop="business">
				<el-select
					v-model="formData.business"
					filterable
					remote
					reserve-keyword
					placeholder="请输入事业群名称"
					:loading="businessLoading"
				>
					<el-option v-for="item in businessOptions" :key="item" :label="item" :value="item" />
				</el-select>
			</el-form-item>
			<el-form-item label="经理">
				<UserSelect
					v-model="formData.managerArray"
					mode="single"
					placeholder="请填写经理，输入用户名搜索"
					:default-selected="formData.managerArray"
					@change="handleManagerChange"
				/>
			</el-form-item>
			<el-form-item label="成员">
				<UserSelect
					v-model="formData.memberArray"
					:default-selected="formData.memberArray"
					placeholder="请填写成员，输入用户名搜索"
					@change="handleMemberChange"
				/>
			</el-form-item>
			<el-form-item label="管理员">
				<UserSelect
					v-model="formData.adminArray"
					:default-selected="formData.adminArray || [username]"
					placeholder="请填写管理员，输入用户名搜索"
					@change="handleAdminChange"
				/>
			</el-form-item>
		</el-form>

		<template #footer>
			<el-button @click="close">取消</el-button>
			<el-button type="primary" @click="submit" :loading="loading">确认</el-button>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch, computed, nextTick, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import UserSelect from '@/views/knowledge/Books/components/UserSelect.vue';
import groupApi from '@/api/GroupApi';
import { useUserStore } from '@/stores/user';
import type { Group } from './form.ts';
const userStore = useUserStore();
// 响应式解构（避免丢失响应性）
import { storeToRefs } from 'pinia';
const { username } = storeToRefs(userStore);

const props = defineProps<{
	visible: boolean;
	formData: Group;
	isEdit: boolean;
}>();

const emits = defineEmits<{
	(e: 'update:visible', value: boolean): void;
	(e: 'submit-success'): void;
}>();

const formRef = ref();
const loading = ref(false);
const formData = ref({ ...props.formData });
watch(
	() => props.formData,
	(val) => (formData.value = { ...val }),
	{ deep: true }
);

watch(
	() => props.visible,
	(val) => {
		if (val) {
			const newData = { ...props.formData };

			// 确保使用响应式的 username
			if (!newData.adminArray || newData.adminArray.length === 0) {
				newData.adminArray = [username.value]; // 注意使用 .value
			}

			formData.value = newData;

			// 添加 nextTick 确保 DOM 更新
			nextTick(() => {
				formData.value = newData;
			});
		}
	},
	{ immediate: true }
);
const rules = {
	name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
	business: [{ required: true, message: '请输入事业群名称', trigger: 'blur' }],
	managerArray: [{ required: true, message: '请填写经理，输入用户名搜索', trigger: 'blur' }],
};

const dialogTitle = computed(() => (props.isEdit ? '编辑群组' : '新建群组'));

function close() {
	emits('update:visible', false);
}

function handleManagerChange(val: string[]) {
	formData.value.managerArray = val;
}

function handleMemberChange(val: string[]) {
	formData.value.memberArray = val;
}

function handleAdminChange(val: string[]) {
	formData.value.adminArray = val;
}

async function submit() {
	await formRef.value?.validate();
	loading.value = true;
	const { id, name, business, manager, createUser, managerArray, memberArray, adminArray } = formData.value;

	const basicInfo = {
		name,
		business,
		manager: managerArray,
		createUser,
	};

	const users = {
		member: memberArray?.join(';') || '',
		admin: adminArray.join(';'),
	};
	try {
		let res;
		if (props.isEdit) {
			const payLoadEdit = {
				workGroup: {
					id,
					...basicInfo,
				},
				...users,
			};
			res = await groupApi.editGroup(payLoadEdit);
		} else {
			const payload: Group = {
				workGroup: {
					...basicInfo,
				},
				...users,
			};
			res = await groupApi.createGroup(payload);
		}

		if (res?.code === 200) {
			ElMessage.success(res?.message || (props.isEdit ? '更新成功' : '创建成功'));
			emits('submit-success');
			close(); // ✅ 成功时再关闭弹窗
		} else {
			// ElMessage.error(res?.message || '提交失败');
		}
	} catch (error) {
		console.error(error);
		ElMessage.error(error?.message || '提交失败');
	} finally {
		loading.value = false;
	}
}

// 添加事业群相关状态
const businessOptions = ref([]);
const businessLoading = ref(false);

// 初始化时加载默认事业群列表
onMounted(async () => {
	try {
		const res = await groupApi.getBuList();
		businessOptions.value = res.data || [];
	} catch (error) {
		console.error('获取事业群列表失败', error);
	}
});
</script>
