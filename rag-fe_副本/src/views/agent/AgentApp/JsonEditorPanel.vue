<!-- eslint-disable -->
<template>
	<div class="json-editor-panel">
		<el-row class="model-selection">
			<el-col :span="4">
				<span class="selection-label">{{ title }}</span>
			</el-col>
			<el-col :span="20">
				<vue-json-editor
					:key="'json-editor-' + editorKey"
					:value="internalValue"
					:show-btns="false"
					:mode="'code'"
					:expandedOnStart="true"
					:readOnly="readOnly"
					@json-change="handleChange"
					@has-error="handleError"
					class="json-editor"
				/>
				<div v-if="errorMessage" class="error-message">{{ errorMessage }}</div>
			</el-col>
		</el-row>
	</div>
</template>

<script setup>
import { ref, watch } from 'vue';
import VueJsonEditor from 'vue-json-editor';

const props = defineProps({
	title: { type: String, required: true },
	modelValue: { type: [Object, Array, String], default: () => ({}) },
	editorKey: { type: [String, Number], default: 0 },
	readOnly: { type: Boolean, default: false },
});
const emit = defineEmits(['update:modelValue', 'error-change']);

const errorMessage = ref('');
const internalValue = ref(parseModelValue(props.modelValue));

// 监听父组件传入的值变化
watch(
	() => props.modelValue,
	(newVal) => {
		internalValue.value = parseModelValue(newVal);
	},
	{ deep: true }
);

// JSON 格式解析
function parseModelValue(val) {
	if (!val) {
		return {};
	}
	if (typeof val === 'string') {
		try {
			return JSON.parse(val);
		} catch (err) {
			errorMessage.value = '初始 JSON 格式错误';
			return {};
		}
	}
	if (typeof val === 'object') {
		return val;
	}
	return {};
}

// json-change 事件处理
function handleChange(value) {
	try {
		JSON.stringify(value); // 简单验证格式
		errorMessage.value = '';
		internalValue.value = value;
		emit('update:modelValue', value);
		emit('error-change', '');
	} catch (e) {
		handleError(e);
	}
}

// 错误处理
function handleError(err) {
	const msg =
		err && typeof err === 'object'
			? err.message || JSON.stringify(err)
			: typeof err === 'string'
			? err
			: '未知错误';
	errorMessage.value = msg;
	emit('error-change', msg);
}
</script>

<style scoped>
.json-editor-panel {
	margin-bottom: 10px;
}
.selection-label {
	font-size: 14px;
	color: var(--el-text-color-secondary);
}
:deep(.jsoneditor) {
	border: 1px solid #dcdfe6;
	border-radius: 4px;
	height: 220px;
}
:deep(.jsoneditor-menu a.jsoneditor-poweredBy),
:deep(.jsoneditor-modes button.jsoneditor-separator) {
	display: none !important;
}
.error-message {
	color: #f56c6c;
	font-size: 12px;
	margin-top: 5px;
}
</style>
