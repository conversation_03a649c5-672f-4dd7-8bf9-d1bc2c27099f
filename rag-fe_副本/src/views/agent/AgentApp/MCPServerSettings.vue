<!-- eslint-disable -->
<template>
	<div class="mcpserver-settings">
		<el-card class="settings-card">
			<template #header>
				<div class="section-header">
					<span class="group-title">MCP Server</span>
					<el-tooltip
						:content="functionCallSupported ? '' : '当前模型不支持关联MCP Server'"
						placement="top"
						:disabled="functionCallSupported"
					>
						<el-button 
							type="primary" 
							text 
							:icon="Edit" 
							class="association-btn" 
							@click="openMCPServerDialog"
							:disabled="!functionCallSupported"
						>
							关联 MCP Server
						</el-button>
					</el-tooltip>
				</div>
			</template>

			<div class="settings-content">
				<el-row>
					<el-col>
						<el-table
							:data="selectedMCPServers"
							style="width: 100%"
							empty-text="暂无关联 MCP Server"
							class="mcpserver-table"
						>
							<el-table-column width="40px">
								<template #default="scope">
									<div class="order-number">{{ scope.$index + 1 }}</div>
								</template>
							</el-table-column>
							<el-table-column prop="mcpServerName" width="120px" label="Server 名称" />
							<el-table-column prop="description" label="功能描述" />
							<el-table-column width="80px">
								<template #default="scope">
									<el-button type="danger" text :icon="Delete" @click="removeMCPServer(scope.$index)" />
								</template>
							</el-table-column>
						</el-table>
					</el-col>
				</el-row>
			</div>
		</el-card>
	</div>
</template>

<script>
import { Delete, Edit, Monitor } from '@element-plus/icons-vue';

export default {
	name: 'MCPServerSettings',
	props: {
		selectedMCPServers: {
			type: Array,
			default: () => [],
		},
		functionCallSupported: {
			type: Boolean,
			default: false,
		},
	},
	emits: ['update:selectedMCPServers', 'openMCPServerDialog'],
	setup(props, { emit }) {
		const openMCPServerDialog = () => {
			if (!props.functionCallSupported) {
				return;
			}
			console.log('MCPServerSettings: openMCPServerDialog被调用');
			emit('openMCPServerDialog');
		};

		const updateMCPServers = () => {
			emit('update:selectedMCPServers', [...props.selectedMCPServers]);
		};

		const removeMCPServer = (index) => {
			const updatedServers = [...props.selectedMCPServers];
			updatedServers.splice(index, 1);
			emit('update:selectedMCPServers', updatedServers);
		};

		return {
			Edit,
			Delete,
			Monitor,
			openMCPServerDialog,
			updateMCPServers,
			removeMCPServer,
		};
	},
};
</script>

<style scoped lang="less">
.mcpserver-settings {
	border-radius: 8px;
	margin-top: 20px;

	.settings-card {
		padding: 10px;
		border-radius: 8px;
	}
}

.group-title {
	font-size: 14px;
	color: var(--el-text-color-secondary);
	margin: 5px;
	font-weight: 500;
}
.section-header {
	display: flex;
	align-items: center;
	.association-btn {
		margin-left: auto;
		font-size: 13px;
	}
}

.settings-content {
	display: flex;
	flex-direction: column;
	gap: 20px;
}

.mcpserver-table {
	margin-top: 10px;
	border: 1px solid var(--el-border-color-light);
	border-radius: 4px;

	:deep(.el-table__cell) {
		padding: 8px 0;
	}
}

.order-number {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 24px;
	height: 24px;
	background-color: var(--el-color-primary);
	color: white;
	border-radius: 4px;
	font-size: 12px;
	font-weight: bold;
}
</style>