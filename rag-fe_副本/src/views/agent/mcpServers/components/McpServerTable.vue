<!-- eslint-disable -->
<template>
	<div class="table-container">
		<el-table v-loading="loading" :data="tableData" stripe table-layout="auto" style="width: 100%">
			<el-table-column prop="id" label="ID" width="80" align="center" />
			<el-table-column prop="name" label="server名称" width="100" show-overflow-tooltip />
			<el-table-column label="工作组" width="120" align="center" show-overflow-tooltip>
				<template #default="{ row }">
					{{ formatGroupInfo(row.groupId) }}
				</template>
			</el-table-column>
			<el-table-column prop="type" label="服务类型" width="80" align="center">
				<template #default="{ row }">
					{{ row.type === 0 ? 'api' : 'local' }}
				</template>
			</el-table-column>
			<el-table-column prop="description" label="功能描述" width="150" show-overflow-tooltip />
			<el-table-column prop="command" label="执行命令" width="100" show-overflow-tooltip />
			<el-table-column prop="url" label="接口地址" width="100" show-overflow-tooltip />
			<el-table-column prop="env" label="配置信息" width="100" align="center" show-overflow-tooltip/>
			<el-table-column prop="queryPath" label="查询路径" show-overflow-tooltip />
			<el-table-column prop="executePath" label="执行路径" width="100" show-overflow-tooltip />
			<el-table-column prop="createAt" label="创建时间" width="180" align="center" />
			<el-table-column prop="updateAt" label="更新时间" width="180" align="center" />
			<el-table-column label="操作" width="160" align="center" fixed="right">
				<template #default="{ row }">
					<el-button v-if="hasEditPermission(row.groupId)" type="primary" size="small" @click="$emit('edit', row)" link>修改</el-button>
					<el-popconfirm v-if="hasEditPermission(row.groupId)" title="确认要删除该条记录吗?" @confirm="$emit('delete', row)">
						<template #reference>
							<el-button type="danger" size="small" link>删除</el-button>
						</template>
					</el-popconfirm>
				</template>
			</el-table-column>
		</el-table>

		<el-pagination
			v-if="paging.total > 0"
			:current-page="paging.page"
			:page-size="paging.size"
			:total="paging.total"
			:background="true"
			layout="prev, pager, next, jumper, total"
			@update:current-page="$emit('update:page', $event)"
			@update:page-size="$emit('update:size', $event)"
		/>
	</div>
</template>

<script lang="ts" setup>
import { defineProps, defineEmits, ref, onMounted } from 'vue';
import type { McpServer, Paging } from './types';
import groupApi from '@/api/GroupApi';
import { ElMessage } from 'element-plus';

const props = defineProps<{
	tableData: McpServer[];
	loading: boolean;
	paging: Paging;
}>();

defineEmits(['edit', 'delete', 'update:page', 'update:size']);

// 所有工作组数据
const allWorkgroups = ref<any[]>([]);
const workgroupsLoading = ref(false);

// 用户已加入的工作组
const joinedWorkgroups = ref<any[]>([]);

// 获取所有工作组列表
const fetchAllWorkgroups = async () => {
	workgroupsLoading.value = true;
	try {
		const response = await groupApi.getAllGroupsList();
		if (response?.data) {
			allWorkgroups.value = response.data;
		}
	} catch (error) {
		console.error('获取所有工作组列表错误:', error);
		ElMessage.error('获取所有工作组列表失败');
	} finally {
		workgroupsLoading.value = false;
	}
};

// 获取用户已加入的工作组
const fetchJoinedWorkgroups = async () => {
	try {
		const response = await groupApi.getJoinedList();
		if (response?.data) {
			joinedWorkgroups.value = response.data;
		}
	} catch (error) {
		console.error('获取已加入工作组列表错误:', error);
	}
};

onMounted(() => {
	fetchAllWorkgroups();
	fetchJoinedWorkgroups();
});

// 检查是否是该工作组的成员
const hasEditPermission = (groupId: number | string) => {
	if (!groupId) {
		return false;
	}
	const numericGroupId = Number(groupId);
	return joinedWorkgroups.value.some((group: any) => group.id === numericGroupId);
	
};

// 格式化工作组信息
const formatGroupInfo = (groupId: number | string | null) => {
	if (!groupId) {
		return '-';
	}
	
	const numericGroupId = Number(groupId);
	const group = allWorkgroups.value.find((g: any) => g.id === numericGroupId);
	return group ? `${group.name} (${group.business})` : `${groupId}`;
};
</script>

<style scoped>
.table-container {
	margin-bottom: 20px;
}

.el-pagination {
	margin-top: 20px;
	justify-content: center;
}
</style>
