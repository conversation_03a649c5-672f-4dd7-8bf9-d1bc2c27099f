<!-- eslint-disable -->
<template>
	<div class="query-container">
		<el-form class="query-form" inline>
			<el-form-item label="server名称:" class="form-item-wide">
				<el-input v-model="query.name" placeholder="根据server名称模糊查询" clearable class="wide-input" />
			</el-form-item>
			<el-form-item label="工作组:" class="form-item-wide">
				<el-select v-model="query.groupId" placeholder="请选择工作组" clearable loading-text="加载中..." :loading="tabActive === 'all' ? allWorkgroupsLoading : workgroupsLoading" class="wide-input">
					<el-option 
						v-for="item in displayWorkgroups" 
						:key="item.id" 
						:label="`${item.id} - ${item.name} (${item.business})`" 
						:value="item.id" 
					/>
				</el-select>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="$emit('search')">查询</el-button>
			</el-form-item>
			<el-form-item class="create-btn">
				<el-button type="primary" @click="$emit('create')">新建</el-button>
			</el-form-item>
		</el-form>
	</div>
</template>

<script lang="ts" setup>
import { defineProps, defineEmits, onMounted, ref, computed, watch } from 'vue';
import groupApi, { type Workgroup } from '@/api/GroupApi';
import { ElMessage } from 'element-plus';

const props = defineProps<{
	query: {
		name: string;
		groupId: string;
	};
	tabActive?: string;
}>();

defineEmits(['search', 'create']);

// 工作组数据
const workgroups = ref<Workgroup[]>([]);
const workgroupsLoading = ref(false);

// 所有工作组数据
const allWorkgroups = ref<Workgroup[]>([]);
const allWorkgroupsLoading = ref(false);

// 根据当前标签页计算展示的工作组列表
const displayWorkgroups = computed(() => {
	return props.tabActive === 'all' ? allWorkgroups.value : workgroups.value;
});

// 监听标签页变化，加载对应的工作组数据
watch(() => props.tabActive, (newTab) => {
	if (newTab === 'all') {
		fetchAllWorkgroups();
	}
}, { immediate: true });

// 获取工作组列表
const fetchWorkgroups = async () => {
	workgroupsLoading.value = true;
	try {
		const response = await groupApi.getJoinedList();
		workgroups.value = response?.data || [];
	} catch (error) {
		console.error('获取工作组列表错误:', error);
		ElMessage.error('获取工作组列表失败');
	} finally {
		workgroupsLoading.value = false;
	}
};

// 获取所有工作组列表
const fetchAllWorkgroups = async () => {
	if (props.tabActive !== 'all') return;
	
	allWorkgroupsLoading.value = true;
	try {
		const response = await groupApi.getAllGroupsList();
		if (response?.data) {
			allWorkgroups.value = response.data;
		}
	} catch (error) {
		console.error('获取所有工作组列表错误:', error);
		ElMessage.error('获取所有工作组列表失败');
	} finally {
		allWorkgroupsLoading.value = false;
	}
};

onMounted(() => {
	fetchWorkgroups();
	if (props.tabActive === 'all') {
		fetchAllWorkgroups();
	}
});
</script>

<style scoped lang="less">
.query-container {
	margin-bottom: 20px;
	padding: 0 20px;
	background-color: #fff;
	border-radius: 8px;
	height: 64px;
	display: flex;
	align-items: center;
	box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);

	.query-form {
		width: 100%;
		display: flex;
		align-items: center;

		:deep(.el-form-item) {
			margin-bottom: 0;
			margin-right: 16px;
			display: flex;
			align-items: center;
		}

		:deep(.el-form-item__label) {
			padding-right: 8px;
			width: 100px; /* 固定标签宽度 */
			flex: none;
		}

		.form-item-wide {
			flex: 1;
			max-width: 300px; /* 控制最大宽度 */

			.wide-input {
				width: 100%;

				:deep(.el-input__wrapper) {
					width: 100%;
				}
			}
		}

		.create-btn {
			margin-left: auto;
			margin-right: 0;
			flex: none;
		}
	}
}
</style>