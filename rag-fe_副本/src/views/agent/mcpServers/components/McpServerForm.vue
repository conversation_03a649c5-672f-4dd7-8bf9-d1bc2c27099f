<!-- eslint-disable -->
<template>
	<el-dialog
		:model-value="modelValue"
		:title="title"
		width="600"
		@close="$emit('update:modelValue', false)"
		@update:modelValue="$emit('update:modelValue', $event)"
	>
		<el-form ref="formRef" :model="server" :rules="rules" label-width="140px">
			<el-form-item label="server名称:" prop="name">
				<el-input v-model="server.name" placeholder="请输入server名称" clearable />
			</el-form-item>
			<el-form-item label="服务器类型:" prop="type">
				<el-select v-model.number="server.type" placeholder="请选择服务器类型" clearable>
					<el-option label="api" :value="0" />
					<el-option label="local" :value="1" />
				</el-select>
			</el-form-item>
			<el-form-item label="功能描述:" prop="description">
				<el-input 
					v-model="server.description"
					placeholder="请输入功能描述"
					type="textarea"
					:rows="2"
				/>
			</el-form-item>
			<el-form-item label="命令:" prop="command">
				<el-input v-model="server.command" placeholder="请输入命令" clearable />
			</el-form-item>
			<el-form-item label="接口地址:" prop="url">
				<el-input v-model="server.url" placeholder="请输入接口地址" clearable />
			</el-form-item>
			<el-form-item label="配置信息:" prop="env">
				<el-input v-model="server.env" placeholder="请输入配置信息" clearable />
			</el-form-item>
			<el-form-item label="查询路径:" prop="queryPath">
				<el-input v-model="server.queryPath" placeholder="请输入查询路径" clearable />
			</el-form-item>
			<el-form-item label="执行路径:" prop="executePath">
				<el-input v-model="server.executePath" placeholder="请输入执行路径" clearable />
			</el-form-item>
			<el-form-item label="工作组:" prop="groupId">
				<el-select v-model.number="server.groupId" placeholder="请选择工作组" clearable loading-text="加载中..." :loading="workgroupsLoading">
					<el-option 
						v-for="item in workgroups" 
						:key="item.id" 
						:label="`${item.id} - ${item.name} (${item.business})`" 
						:value="item.id" 
					/>
				</el-select>
			</el-form-item>
		</el-form>

		<template #footer>
			<el-button @click="handleClose">取消</el-button>
			<el-button type="primary" @click="handleSubmit">保存</el-button>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import groupApi, { type Workgroup } from '@/api/GroupApi';

const props = defineProps<{
	modelValue: boolean;
	title: string;
	server: {
		name: string;
		type: string;
		command: string;
		url: string;
		env: string;
		queryPath: string;
		executePath: string;
		groupId: string;
		description?: string;
	};
}>();

const emit = defineEmits(['update:modelValue', 'save']);

const formRef = ref();
// 表单数据
const formData = ref({
	...props.server,
	type: Number(props.server.type),
	groupId: Number(props.server.groupId),
});
// 监听props变化更新表单数据
watch(
	() => props.server,
	(newVal) => {
		formData.value = {
			...newVal,
			type: Number(newVal.type),
			groupId: Number(newVal.groupId),
		};
	},
	{ deep: true }
);

// 工作组数据
const workgroups = ref<Workgroup[]>([]);
const workgroupsLoading = ref(false);

// 获取工作组列表
const fetchWorkgroups = async () => {
	workgroupsLoading.value = true;
	try {
		const response = await groupApi.getJoinedList();
		workgroups.value = response?.data || [];
	} catch (error) {
		console.error('获取工作组列表错误:', error);
		ElMessage.error('获取工作组列表失败');
	} finally {
		workgroupsLoading.value = false;
	}
};

onMounted(() => {
	fetchWorkgroups();
});

const rules = {
	name: [{ required: true, max: 50, message: '请输入服务器名称（不超过50个字）', trigger: 'blur' }],
	type: [
		{ required: true, type: 'number', message: '请选择服务器类型', trigger: 'change' },
	],
	command: [], // 非必填
	url: [],     // 非必填
	env: [{ required: true, message: '请输入环境', trigger: 'blur' }],
	queryPath: [{ required: true, message: '请输入查询路径', trigger: 'blur' }],
	executePath: [{ required: true, message: '请输入执行路径', trigger: 'blur' }],
	groupId: [
		{ required: true, type: 'number', message: '请选择工作组', trigger: 'change' },
	],
};

// 提交处理
const handleSubmit = async () => {
	try {
		await formRef.value.validate();
		const submitData = {
			...formData.value,
			type: Number(formData.value.type),
			groupId: Number(formData.value.groupId),
		};
		emit('save', submitData);
		emit('update:modelValue', false);
	} catch (error) {
		ElMessage.error('请检查表单填写是否正确');
	}
};

const handleClose = () => {
	formRef.value?.resetFields();
	emit('update:modelValue', false);
};
</script>

<style scoped>
.el-form-item {
	margin-bottom: 20px;
}

.el-select,
.el-input {
	width: 100%;
}
</style>