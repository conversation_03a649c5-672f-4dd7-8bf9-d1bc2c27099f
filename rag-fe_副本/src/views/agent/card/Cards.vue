<template>
  <div>
    <el-row :gutter="10" style="width: 100%; margin: 0 auto;">
      <el-col :key="i.id" v-for="i in detail" :xs="24" :sm="12" :md="8" :lg="6" :xl="6" class="card-col">
        <el-card class="agent-card" :body-style="{ padding: '16px' }" shadow="hover" @click="jumpDetails(i)">
          <!-- 卡片内容区域 -->
          <div class="card-layout">
            <!-- 左侧正方形图片 -->
            <div class="card-image">
              <img
                :src="i.url ? i.url : 'https://agi-dev-platform-web.cdn.bcebos.com/ai_apaas/dist/img/education_bad9115a.png'"
                alt="agent-image" class="square-image" />
            </div>

            <!-- 右侧内容区域 -->
            <div class="card-content">
              <!-- 标题和图标 -->
              <div class="card-title">
                <div class="app-info">
                  <!-- <span class="decoration-icon">
                    <el-icon color="#FFD700">
                      <MagicStick />
                    </el-icon>
                  </span> -->
                  <el-tooltip :content="i.name" placement="top" :show-after="200" popper-class="custom-tooltip" :disabled="!isTextEllipsis(i.name)">
                    <div class="app-name">{{ i.name }}</div>
                  </el-tooltip>
                </div>
              </div>

              <!-- ID信息区域 -->
              <div class="id-info-container">
                <div class="id-item">
                  <span class="id-label">Agent ID：</span>
                  <span class="id-value">{{ i.id || '未知' }}</span>
                </div>
                <div class="id-item">
                  <span class="id-label">工作组 ID：</span>
                  <span class="id-value">{{ i.groupId || '未知' }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 底部标签和统计 -->
          <div class="card-footer">
            <!-- 标签 -->
            <!-- <div class="card-tags">
              <el-tag size="small" effect="plain" class="tag">企业服务</el-tag>
              <el-tag size="small" effect="plain" class="tag">自主规划Agent</el-tag>
            </div> -->
            <div class="card-description">
              <el-tooltip :content="i.description" placement="top" :show-after="200" popper-class="custom-tooltip" :disabled="!isTextEllipsis(i.description)">
                <div>{{ i.description }}</div>
              </el-tooltip>
            </div>
            <!-- 统计信息 -->
            <!-- <div class="card-stats">
              <span class="stat-item">
                <el-icon>
                  <View />
                </el-icon> 223
              </span>
              <span class="stat-item">
                <el-icon>
                  <Star />
                </el-icon> 3
              </span>
            </div> -->
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts">
import { defineComponent, type PropType, onMounted, onBeforeUnmount, ref } from 'vue';
import type { queryAgentResponseData } from "@/types/AgentTypes";
import { UserFilled, View } from '@element-plus/icons-vue';

export default defineComponent({
  name: 'AgentCards',
  components: {
    UserFilled,
    View
  },
  props: {
    detail: {
      type: Array as PropType<queryAgentResponseData[]>,
      default: () => ([] as queryAgentResponseData[]),
    },
  },
  emits: ['update-page-size'],
  setup(props, { emit }) {
    // 用于计算屏幕尺寸和每页显示数量
    const calcItemsPerPage = () => {
      const width = window.innerWidth;
      let columns;

      if (width < 768) {
        columns = 1; // xs屏幕每行1个
      } else if (width < 992) {
        columns = 2; // sm屏幕每行2个
      } else if (width < 1200) {
        columns = 3; // md屏幕每行3个
      } else if (width < 1600) {
        columns = 4; // lg屏幕每行4个
      } else {
        columns = 6; // xl及超大屏幕每行6个
      }

      // 计算屏幕高度可以容纳多少行
      const height = window.innerHeight;
      // 假设每个卡片高度约为200px，加上一些间距，每行大约需要220px
      const rowHeight = 220;
      const visibleRows = Math.max(3, Math.floor((height - 200) / rowHeight)); // 最少3行，根据屏幕高度可增加

      // 计算可容纳的数量
      let itemsPerPage = columns * visibleRows;

      // 调整为4的整数倍
      itemsPerPage = Math.floor(itemsPerPage / 4) * 4;

      // 确保最少显示4个
      if (itemsPerPage < 4) {
        itemsPerPage = 4;
      }

      emit('update-page-size', itemsPerPage);
      return itemsPerPage;
    };

    // 监听窗口大小变化
    const handleResize = () => {
      calcItemsPerPage();
    };

    // 组件挂载和卸载时添加/移除事件监听
    onMounted(() => {
      calcItemsPerPage();
      window.addEventListener('resize', handleResize);
    });

    onBeforeUnmount(() => {
      window.removeEventListener('resize', handleResize);
    });

    return {
      calcItemsPerPage
    };
  },
  methods: {
    jumpDetails(o: queryAgentResponseData) {
      this.$router.push({ path: '/apps/update', query: { id: o.id } });
    },
    // 基于文本长度判断是否可能被截断
    isTextEllipsis(text: string) {
      // 根据内容长度判断是否需要显示tooltip
      // 名称超过15个字符或描述超过40个字符，需要省略号
      return text && text.length > 15;
    }
  }
});
</script>

<style lang="less" scoped>
.card-col {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.card-description {
  font-size: 14px;
  color: #666;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5;
  margin-bottom: 12px;
}

.agent-card {
  width: 100%;
  max-width: 370px;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s;
  cursor: pointer;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 20px rgba(0, 0, 0, 0.1);
  }

  .card-layout {
    display: flex;
    margin-bottom: 16px;

    .card-image {
      // 图片大小
      flex: 0 0 80px;
      margin-right: 16px;

      .square-image {
        width: 80px;
        height: 80px;
        border-radius: 8px;
        object-fit: cover;
      }
    }

    .card-content {
      flex: 1;
      min-width: 0; // 防止文本溢出

      .card-title {
        margin-bottom: 8px;

        .app-info {
          font-size: 15px;
          font-weight: 500;
          color: #333;
          display: flex;
          align-items: center;

          .app-name {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
          }

          .decoration-icon {
            margin-right: 5px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;

            .el-icon {
              font-size: 16px;
            }
          }
        }
      }

      .id-info-container {
        display: flex;
        flex-direction: column;
        gap: 6px;
        margin-top: 8px;

        .id-item {
          background-color: #f5f7fa;
          border-radius: 3px;
          padding: 2px 6px;
          display: flex;
          align-items: center;
          width: fit-content;

          .id-label {
            color: #909399;
            font-size: 12px;
            margin-right: 4px;
            min-width: 45px;
          }

          .id-value {
            color: #606266;
            font-size: 12px;
            font-weight: 500;
          }
        }
      }
    }
  }

  .card-footer {
    .card-tags {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 12px;
      gap: 8px;

      .tag {
        background-color: #f7f7f7;
        border: 1px solid #e0e0e0;
        color: #666;
      }
    }

    .card-stats {
      display: flex;
      align-items: center;
      color: #999;
      font-size: 14px;
      gap: 16px;

      .stat-item {
        display: flex;
        align-items: center;
        gap: 4px;
      }
    }
  }
}
</style>

<style lang="less">
/* 全局样式，不使用scoped */
.custom-tooltip {
  max-width: 300px;
  word-break: break-word;
  white-space: normal;
  line-height: 1.5;
}
</style>
