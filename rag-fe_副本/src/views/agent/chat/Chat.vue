<!-- eslint-disable -->
<template>
	<div>
		<el-row justify="center">
			<el-col :span="20">
				<div style="text-align: center; font-size: 10px; font-family: 'Courier New', Courier, monospace"></div>
			</el-col>
		</el-row>
		<el-row style="min-height: 400px" v-if="!showOutput">
			<el-col>
				<el-row style="margin-top: 100px">
					<el-col
						:span="24"
						style="display: flex; justify-content: center; align-items: center; height: 100%"
					>
						<img src="@/assets/chat.webp" style="width: 72px; height: 72px" />
					</el-col>
				</el-row>
				<el-row>
					<el-col
						:span="24"
						style="display: flex; justify-content: center; align-items: center; height: 100%"
					>
						<span>我的Agent</span>
					</el-col>
				</el-row>
			</el-col>
		</el-row>
		<el-row justify="start" v-if="showOutput">
			<el-col :span="24">
				<div
					class="grid-content bg-purple-dark"
					style="
						border-radius: 10px;
						min-height: 400px;
						padding-top: 10px;
						padding-bottom: 10px;
						margin-top: 10px;
					"
				>
					<div class="stream">
						<pre ref="insert" class="preCss" v-if="showOutput"></pre>
						<img src="@/assets/chat.webp" style="width: 32px; height: 32px" />
					</div>
					<div class="stream">
						<img src="@/assets/chat.webp" height="32px" width="32px" />
						<pre ref="output" class="preCss" v-if="showOutput"></pre>
					</div>
				</div>
			</el-col>
		</el-row>
		<el-row justify="start" style="margin-top: 20px">
			<el-col :span="23" style="margin-left: 10px">
				<el-card :body-style="{ padding: '0px' }">
					<div
						class="grid-content bg-purple-dark"
						style="margin: 10px; display: flex; justify-content: flex-end"
					>
						<el-input
							v-model="prompt"
							placeholder="输入你的问题..."
							style="border: none !important; box-shadow: none"
							@keyup.enter="startChat"
						></el-input>
						<el-button link @click="startChat" style="width: 40px" :loading="isLoading">
							<img src="@/assets/play.png" height="25px" width="25px" v-if="!isLoading" />
						</el-button>
					</div>
				</el-card>
			</el-col>
		</el-row>
	</div>
</template>

<script lang="ts">
import { ref } from 'vue';
import { UserFilled } from '@element-plus/icons-vue';
import { nextTick } from 'vue';
import homeUrl from '@/assets/home.png';
import titleUrl from '@/assets/title.png';
import { ElMessage } from 'element-plus';

export default {
	name: 'LLMChat',
	props: {
		agentId: {
			type: Number, // 确保类型正确
			default: 0, // 如果没有传递，默认为 0
		},
	},
	computed: {
		UserFilled() {
			return UserFilled;
		},
	},
	data() {
		return {
			homeUrl: homeUrl,
			titleUrl: titleUrl,
			prompt: ref(''), // Vue3 响应式数据
			showOutput: false,
			isLoading: false,
		};
	},
	methods: {
		async startChat() {
			console.log('startChat');
			if (this.agentId === 0) {
				ElMessage.error('请先发布agent');
				return;
			}
			this.showOutput = true;
			this.isLoading = true;
			await nextTick();
			const output = this.$refs.output as HTMLElement;
			const insert = this.$refs.insert as HTMLElement;
			// 清空内容
			output.innerText = '';
			insert.innerHTML = ''; // bca-disable-line
			insert.innerHTML = this.prompt; // bca-disable-line

			try {
				const response = await fetch('/rag/api/agent/stream?stream=true', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						query: this.prompt,
						agentId: this.agentId,
					}),
				});

				if (!response.ok) {
					ElMessage.error('Network response was not ok');
					this.isLoading = false;
					this.prompt = '';
					return;
				}

				const reader = response.body?.getReader();
				const decoder = new TextDecoder('utf-8');

				if (reader) {
					let buffer = '';
					let tempBuffer = '';

					while (true) {
						const { value, done } = await reader.read();
						if (done) break;

						const chunk = decoder.decode(value, { stream: true });
						tempBuffer += chunk; // 先拼接，防止数据丢失

						const lines = tempBuffer.split('\n');
						tempBuffer = lines.pop() || ''; // 处理残缺数据

						lines.forEach((line) => {
							console.log(line);
							if (line.startsWith('data:"')) {
								// 使用 split 方法提取 data 后的值
								const parts = line.split(':"');
								if (parts.length > 1) {
									const dataValue = parts[1].slice(0, -1); // 去掉末尾的双引号
									console.log(dataValue); // 输出提取后的值
									buffer += dataValue;
								}
							}
						});

						output.innerText = buffer; // 让 <pre> 解析 \n
						await nextTick();
						output.scrollTop = output.scrollHeight; // 滚动到底部
					}

					if (tempBuffer) {
						const match = tempBuffer.match(/data:"(.*?)"/);
						if (match && match[1]) {
							buffer += match[1].replace(/\\n/g, '\n');
							output.innerText = buffer;
						}
					}
				}
				this.isLoading = false;
				this.prompt = '';
			} catch (error) {
				output.innerText += '\n[请求失败]';
				console.error('Error:', error);
			}
		},
	},
};
</script>

<style scoped>
@import '@/assets/global.css';
pre {
	white-space: pre-wrap; /* 允许自动换行 */
	word-break: break-word; /* 处理长单词 */
	max-height: 300px; /* 适当设置最大高度 */
	overflow-y: auto; /* 让它自己滚动 */
}
.example-showcase .el-dropdown-link {
	cursor: pointer;
	color: var(--el-color-primary);
	display: flex;
	align-items: center;
}

.home-image .block {
	padding: 30px 0;
	text-align: center;
	border-right: solid 1px var(--el-border-color);
	display: inline-block;
	width: 20%;
	box-sizing: border-box;
	vertical-align: top;
}
.home-image .block:last-child {
	border-right: none;
}
.home-image .demonstration {
	display: block;
	color: var(--el-text-color-secondary);
	font-size: 14px;
	margin-bottom: 20px;
}
.stream {
	margin-left: 10px;
	display: flex;
	align-items: center; /* 上下居中对齐 */
	gap: 10px; /* 设置图片和文本之间的间距 */
}
.stream img {
	width: 32px;
	height: 32px;
	border-radius: 50%;
	object-fit: cover; /* 确保图片不会变形 */
}
.preCss {
	flex: 0.9; /* 让 pre 占据剩余空间 */
	background-color: white;
	border: 1px solid #ccc;
	padding: 10px;
	min-height: 20px;
	overflow-y: auto;
	border-radius: 5px;
	line-height: 1.5;
	font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
	font-size: 14px;
	font-weight: 400;
	color: #333; /* 文字颜色 */
}
</style>
