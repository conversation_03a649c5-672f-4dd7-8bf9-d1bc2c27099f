export const routes = [
	{
		path: '/',
		component: () => import('@/components/Layout.vue'),
		redirect: '/index',
		meta: { title: '首页', icon: 'House', showChildren: false },
		children: [
			{
				path: '',
				name: '',
				component: () => import('@/views/index/ChatView.vue'),
				// component: () => import('@/views/index/HomeIndex.vue'),
				meta: { title: '', hidden: true },
			},
		],
	},
	{
		path: '/knowledge',
		meta: { title: '知识广场', icon: 'Operation', showChildren: true },
		component: () => import('@/components/Layout.vue'),
		children: [
			{
				path: 'books',
				name: 'Books',
				component: () => import('@/views/knowledge/Books/index.vue'),
				meta: { title: '知识库', icon: 'Notebook', hidden: false },
			},
			{
				path: 'docs',
				name: 'Docs',
				component: () => import('@/views/knowledge/Docs/index.vue'),
				// component: () => import('@/views/knowledge/Docs.vue'),
				meta: { title: '文档集', icon: 'Document', hidden: true },
			},
		],
	},
	{
		path: '/apps',
		meta: { title: '应用市场', icon: 'Menu', showChildren: true },
		component: () => import('@/components/Layout.vue'),
		children: [
			{
				path: 'agent',
				name: 'Agent',
				component: () => import('@/views/agent/AgentCenter.vue'),
				meta: { title: '智能体', icon: 'HelpFilled', hidden: false },
			},
			{
				path: 'mcp-servers',
				name: 'McpServers',
				component: () => import('@/views/agent/mcpServers/index.vue'),
				meta: { title: 'mcp server市场', icon: 'Monitor', hidden: false },
			},
			{
				path: 'update',
				name: 'Update',
				meta: { title: '智能体构建', icon: 'Menu', hidden: true },
				component: () => import('@/views/agent/AgentApp/AgentApp.vue'),
				// component: () => import('@/views/agent/AgentUpdate.vue'),
			},
		],
	},
	{
		path: '/center',
		meta: { title: '设置', icon: 'Menu', showChildren: true, hidden: true },
		component: () => import('@/components/Layout.vue'),
		children: [
			{
				path: 'user',
				name: 'user',
				component: () => import('@/views/user/index.vue'),
				meta: { title: '个人中心', hidden: false },
			},
		],
	},
	{
		path: '/team',
		meta: { title: '团队', icon: 'Menu', showChildren: true, hidden: false },
		component: () => import('@/components/Layout.vue'),
		children: [
			{
				path: 'group',
				name: 'group',
				component: () => import('@/views/group/index.vue'),
				meta: { title: '团队信息', hidden: false },
				props: (route: any) => ({
					id: route.query.id || '1', // 默认值为'1'
				}),
			},
		],
	},
	{
		path: '/value-model',
		meta: { title: '价值模型', icon: 'TrendCharts', showChildren: true, hidden: false },
		component: () => import('@/components/Layout.vue'),
		children: [
			{
				path: 'weekly-report',
				name: 'weekly-report',
				component: () => import('@/views/report/weeklyReport.vue'),
				meta: { title: '报表数据', hidden: false },
			},
		],
	},
];
