import { useState, useEffect, useCallback, useRef } from 'react';

// 无限滚动Hook

const useInfiniteScroll = (
    fetchData, // 获取数据的函数，接收参数 { page, size, ...otherParams }
    options = {} // 配置选项
) => {
    const {
        pageSize = 20,
        threshold = 100, // 触发加载的距离阈值（像素），默认100
        enabled = true, // 是否启用无限滚动，默认true
        initialParams = {}
    } = options;

    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [hasMore, setHasMore] = useState(true);
    const [page, setPage] = useState(1);
    const [total, setTotal] = useState(0);
    const [error, setError] = useState(null);
    const [params, setParams] = useState(initialParams);

    const containerRef = useRef(null);
    const isLoadingRef = useRef(false);

    // 加载数据
    const loadData = useCallback(
        async (currentPage, currentParams, isRefresh = false) => {
            if (isLoadingRef.current) {
                return;
            }

            isLoadingRef.current = true;
            setLoading(true);
            setError(null);

            try {
                const response = await fetchData({
                    page: currentPage,
                    size: pageSize,
                    ...currentParams
                });

                const newItems = response.items || [];
                const totalCount = response.total || 0;

                if (isRefresh) {
                    setData(newItems);
                } else {
                    setData((prev) => [...prev, ...newItems]);
                }

                setTotal(totalCount);
                const hasMoreData = newItems.length === pageSize && currentPage * pageSize < totalCount;
                setHasMore(hasMoreData);
            } catch (err) {
                console.error('加载数据失败:', err);
                setError(err);
            } finally {
                setLoading(false);
                isLoadingRef.current = false;
            }
        },
        [fetchData, pageSize]
    );

    // 加载下一页
    const loadMore = useCallback(() => {
        if (!hasMore || loading || !enabled) {
            return;
        }

        const nextPage = page + 1;
        setPage(nextPage);
        loadData(nextPage, params);
    }, [hasMore, loading, enabled, page, params, loadData]);

    // 刷新数据（重置到第一页）
    const refresh = useCallback(
        (newParams = {}) => {
            const updatedParams = { ...params, ...newParams };
            setParams(updatedParams);
            setPage(1);
            setData([]);
            setHasMore(true);
            setError(null);
            loadData(1, updatedParams, true);
        },
        [params, loadData]
    );

    // 滚动事件处理
    const handleScroll = useCallback(() => {
        if (!containerRef.current || !enabled || loading || !hasMore) {
            return;
        }

        const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
        const distanceToBottom = scrollHeight - scrollTop - clientHeight;

        if (distanceToBottom <= threshold) {
            loadMore();
        }
    }, [enabled, loading, hasMore, threshold, loadMore]);

    // 检查是否需要自动加载更多数据（当内容不足以产生滚动条时）
    const checkAndLoadMore = useCallback(() => {
        if (!containerRef.current || !enabled || loading || !hasMore) {
            return;
        }

        const { scrollHeight, clientHeight } = containerRef.current;
        const hasScrollbar = scrollHeight > clientHeight;

        // 如果没有滚动条且还有更多数据，自动加载
        if (!hasScrollbar && hasMore && data.length > 0) {
            loadMore();
        }
    }, [enabled, loading, hasMore, data.length, loadMore]);

    // 监听滚动事件
    useEffect(() => {
        const container = containerRef.current;
        if (!container || !enabled) {
            return;
        }

        container.addEventListener('scroll', handleScroll, { passive: true });
        return () => {
            container.removeEventListener('scroll', handleScroll);
        };
    }, [handleScroll, enabled]);

    // 当数据更新后检查是否需要自动加载更多
    useEffect(() => {
        const timer = setTimeout(() => {
            checkAndLoadMore();
        }, 100);

        return () => clearTimeout(timer);
    }, [data, checkAndLoadMore]);

    // 初始化加载
    useEffect(() => {
        if (enabled) {
            loadData(1, initialParams, true);
        }
    }, [enabled, loadData, initialParams]);

    return {
        data,
        loading,
        hasMore,
        total,
        error,
        page,

        refresh,
        loadMore,

        containerRef,

        updateParams: (newParams) => {
            refresh(newParams);
        }
    };
};

export default useInfiniteScroll;
