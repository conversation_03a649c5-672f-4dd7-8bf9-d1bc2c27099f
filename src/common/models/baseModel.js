import { get, isEmpty } from 'lodash';
import { createModel } from 'COMMON/middleware';
import { presetReducer } from 'COMMON/middleware';

const [startReducer, , failReducer] = presetReducer;

/**
 * 获取缓存的文档生成的prompt
 * @param list
 * @returns {*|null}
 */
function getCacheDocPrompt(list) {
    if (isEmpty(list)) {
        return null;
    }
    let spaceId = localStorage.getItem('spaceId');
    let cacheDocPrompt;

    try {
        cacheDocPrompt = JSON.parse(localStorage.getItem('cacheDocPrompt') || '{}');
    } catch (e) {
        cacheDocPrompt = {};
    }

    let modelId = cacheDocPrompt[spaceId] || null;

    let currentDocPrompt = modelId === null ? null : list.find((item) => item.id === modelId);
    if (!currentDocPrompt) {
        modelId = list[0].id;
        currentDocPrompt = list[0];
    }

    localStorage.setItem(
        'cacheDocPrompt',
        JSON.stringify({
            ...cacheDocPrompt,
            [spaceId]: modelId
        })
    );

    return currentDocPrompt || null;
}

/**
 * 获取缓存的划线生成/需求文本生成的prompt
 * @param list
 * @returns {*|null}
 */
function getCacheIntelligentGeneratePrompt(list) {
    if (isEmpty(list)) {
        return null;
    }
    // 为了兼容旧的缓存，如果没有新的缓存字段，就用旧的
    let modelId = localStorage.getItem('promptModelId');
    let spaceId = localStorage.getItem('spaceId');
    let cacheIntelligentGeneratePrompt;

    try {
        cacheIntelligentGeneratePrompt = JSON.parse(localStorage.getItem('cacheIntelligentGeneratePrompt') || '{}');
    } catch (e) {
        cacheIntelligentGeneratePrompt = {};
    }

    modelId = cacheIntelligentGeneratePrompt[spaceId] || modelId;

    let currentPrompt = modelId === null ? null : list.find((item) => item.id === modelId);
    if (!currentPrompt) {
        modelId = list[0].id;
        currentPrompt = list[0];
    }

    localStorage.setItem(
        'cacheIntelligentGeneratePrompt',
        JSON.stringify({
            ...cacheIntelligentGeneratePrompt,
            [spaceId]: modelId
        })
    );

    return currentPrompt || null;
}

export default createModel({
    project: 'common',
    namespace: 'base',
    version: {
        initialValue: {
            qamate: '3.9.4',
            fe: '3.9.4'
        },
        syncOptions: [
            {
                setVersion: (version) => ({ version }),
                reducer: (state, action) => {
                    state.version = action.params.version;
                    return { ...state };
                }
            }
        ]
    },
    // 知识库sdk 最新版
    kuSDK: {
        initialValue: null,
        syncOptions: [
            {
                setKuSDK: (kuSDK) => ({ kuSDK }),
                reducer: (state, action) => {
                    state.kuSDK = action.params.kuSDK;
                    return { ...state };
                }
            }
        ]
    },
    currentEnv: {
        initialValue: null,
        syncOptions: [
            {
                setCurrentEnv: (currentEnv) => ({ currentEnv }),
                reducer: (state, action) => {
                    state.currentEnv = action.params.currentEnv;
                    return { ...state };
                }
            }
        ]
    },
    zoom: {
        initialValue: 100,
        syncOptions: [
            {
                setZoom: (zoom) => ({ zoom }),
                reducer: (state, action) => {
                    state.zoom = action.params.zoom;
                    return { ...state };
                }
            }
        ]
    },
    // 侧边 sider
    asider: {
        initialValue: true,
        syncOptions: [
            {
                setAsider: (asider) => ({ asider }),
                reducer: (state, action) => {
                    state.asider = action.params.asider;
                    return { ...state };
                }
            }
        ]
    },
    isRichTextSpace: {
        initialValue: false,
        syncOptions: [
            {
                setIsRichTextSpace: (isRichTextSpace) => ({ isRichTextSpace }),
                reducer: (state, action) => {
                    state.isRichTextSpace = action.params.isRichTextSpace;
                    return { ...state };
                }
            }
        ]
    },
    integrationPath: {
        initialValue: {},
        syncOptions: [
            {
                setIntegrationPath: (integrationPath) => ({ integrationPath }),
                reducer: (state, action) => {
                    state.integrationPath = action.params.integrationPath;
                    return { ...state };
                }
            }
        ]
    },
    regressionCasePath: {
        initialValue: {},
        syncOptions: [
            {
                setRegressionCasePath: (regressionCasePath) => ({ regressionCasePath }),
                reducer: (state, action) => {
                    state.regressionCasePath = action.params.regressionCasePath;
                    return { ...state };
                }
            }
        ]
    },
    wholePath: {
        initialValue: {},
        syncOptions: [
            {
                setWholePath: (wholePath) => ({ wholePath }),
                reducer: (state, action) => {
                    state.wholePath = action.params.wholePath;
                    return { ...state };
                }
            }
        ]
    },
    spaceList: {
        initialValue: [],
        asyncOptions: [
            {
                getSpaceList: (params, success, fail) => ({
                    url: '/common/product/list',
                    method: 'post',
                    params: params,
                    success,
                    fail
                }),
                reducers: [
                    startReducer,
                    (state, action) => {
                        const spaces = get(action, 'data.products', []);
                        if (!isEmpty(spaces)) {
                            let spaceId = localStorage.getItem('spaceId');
                            let currentSpace = null;
                            if (spaceId !== null) {
                                for (let spaceList of spaces) {
                                    if (!isEmpty(spaceList.children)) {
                                        for (let item of spaceList.children) {
                                            if (item.id === +spaceId) {
                                                currentSpace = item;
                                                localStorage.setItem('spaceId', item.id);
                                                break;
                                            }
                                        }
                                    }
                                }
                            }
                            if (!currentSpace?.id && !isEmpty(spaces[0].children)) {
                                localStorage.setItem('spaceId', spaces[0].children[0].id);
                                currentSpace = spaces[0].children[0];
                            }
                            state.currentSpace = currentSpace;
                            state.isRichTextSpace = [237, 236, 247]?.includes(currentSpace?.id);
                        }
                        state.spaceList = spaces;
                        return { ...state };
                    },
                    failReducer
                ]
            }
        ],
        syncOptions: [
            {
                setSpaceList: (spaceList) => ({ spaceList }),
                reducer: (state, action) => {
                    state.spaceList = action.params.spaceList;
                    return { ...state };
                }
            }
        ]
    },
    currentSpace: {
        initialValue: null,
        syncOptions: [
            {
                setCurrentSpace: (space) => ({ space }),
                reducer: (state, action) => {
                    localStorage.setItem('spaceId', action.params.space.id);
                    state.currentSpace = action.params.space;
                    state.isRichTextSpace = [237, 236, 247]?.includes(action.params.space?.id);
                    return { ...state };
                }
            }
        ]
    },
    currentModule: {
        initialValue: null,
        syncOptions: [
            {
                setCurrentModule: (module) => ({ module }),
                reducer: (state, action) => {
                    state.currentModule = action.params.module;
                    return { ...state };
                }
            }
        ]
    },
    // ocr 规则
    ocrCharset: {
        initialValue: null,
        syncOptions: [
            {
                setOcrCharset: (ocrCharset) => ({ ocrCharset }),
                reducer: (state, action) => {
                    state.ocrCharset = action.params.ocrCharset;
                    return { ...state };
                }
            }
        ]
    },
    token: {
        initialValue: undefined,
        asyncOptions: [
            {
                getToken: (options, success, fail) => ({
                    url: '/core/user/uuap/token',
                    method: 'get',
                    options: {
                        redirect: 'manual',
                        credentials: 'include',
                        headers: {
                            originalUrl: window.location.href
                        }
                    },
                    success,
                    fail
                }),
                reducers: [
                    startReducer,
                    (state, action) => {
                        state.token = get(action, 'data.token', '');
                        state.username = get(action, 'data.username', undefined);
                        return { ...state };
                    },
                    failReducer
                ]
            }
        ],
        syncOptions: [
            {
                setToken: (token) => ({ token }),
                reducer: (state, action) => {
                    state.token = action.params.token;
                    return { ...state };
                }
            }
        ]
    },
    // 展示参数开关
    showParams: {
        initialValue: true,
        syncOptions: [
            {
                setShowParams: (showParams) => ({ showParams }),
                reducer: (state, action) => {
                    state.showParams = action.params.showParams;
                    return { ...state };
                }
            }
        ]
    },
    username: {
        initialValue: 'lazyone',
        syncOptions: [
            {
                setUsername: (username) => ({ username }),
                reducer: (state, action) => {
                    state.username = action.params.username;
                    return { ...state };
                }
            }
        ]
    },
    // RAG
    ragUserInfo: {
        initialValue: null,
        asyncOptions: [
            {
                getRagUserInfo: (options, success, fail) => ({
                    url: '/rag/api/user/current',
                    method: 'get',
                    success,
                    fail
                }),
                reducers: [
                    startReducer,
                    (state, action) => {
                        const data = get(action, 'data', {});
                        if (action.data) {
                            const userData = action.data;
                            state.ragUserInfo = {
                                username: get(userData, 'name', ''),
                                imageUrl: get(userData, 'hiImageUrl', ''),
                                departmentName: get(userData, 'departmentName', '')
                            };
                            state.ragUsername = get(userData, 'name', '');
                            state.ragUserImage = get(userData, 'hiImageUrl', '');
                            state.ragUserDepartment = get(userData, 'departmentName', '');
                        } else if (action.data && action.data.code === 40001) {
                            // 处理重定向
                            if (action.data.redirectUrl) {
                                window.location = action.data.redirectUrl;
                            }
                        }
                        return { ...state };
                    },
                    (state, action) => {
                        // 失败时使用默认用户信息
                        state.ragUserInfo = {
                            username: 'guanlin',
                            imageUrl: 'https://erp.baidu.com/avatar/getAvatar?appCode=ERP&uuap=guanlin&token=QNSAMXOMTN',
                            departmentName: '未知部门'
                        };
                        state.ragUsername = 'guanlin';
                        state.ragUserImage = 'https://erp.baidu.com/avatar/getAvatar?appCode=ERP&uuap=guanlin&token=QNSAMXOMTN';
                        state.ragUserDepartment = '未知部门';
                        return { ...state };
                    }
                ]
            }
        ],
        syncOptions: [
            {
                setRagUserInfo: (ragUserInfo) => ({ ragUserInfo }),
                reducer: (state, action) => {
                    state.ragUserInfo = action.params.ragUserInfo;
                    if (action.params.ragUserInfo) {
                        state.ragUsername = action.params.ragUserInfo.username;
                        state.ragUserImage = action.params.ragUserInfo.imageUrl;
                        state.ragUserDepartment = action.params.ragUserInfo.departmentName;
                    }
                    return { ...state };
                }
            },
            {
                clearRagUserInfo: () => ({}),
                reducer: (state, action) => {
                    state.ragUserInfo = null;
                    state.ragUsername = '';
                    state.ragUserImage = '';
                    state.ragUserDepartment = '';
                    return { ...state };
                }
            }
        ]
    },
    ragUsername: {
        initialValue: '',
        syncOptions: [
            {
                setRagUsername: (ragUsername) => ({ ragUsername }),
                reducer: (state, action) => {
                    state.ragUsername = action.params.ragUsername;
                    return { ...state };
                }
            }
        ]
    },
    userAccess: {
        initialValue: undefined,
        asyncOptions: [
            {
                queryFinalAuth: (params, success, fail) => ({
                    url: '/base/auth/queryFinalAuth',
                    method: 'post',
                    params,
                    success,
                    fail
                }),
                reducers: [
                    startReducer,
                    (state, action) => {
                        const userAccess = get(action, 'data', {});
                        state.userAccess = userAccess;
                        return { ...state };
                    },
                    failReducer
                ]
            }
        ],
        syncOptions: [
            {
                setUserAccess: (userAccess) => ({ userAccess }),
                reducer: (state, action) => {
                    state.userAccess = action.params.userAccess;
                    return { ...state };
                }
            }
        ]
    },
    promptList: {
        initialValue: [],
        asyncOptions: [
            {
                getPromptList: (params, success, fail) => ({
                    url: '/core/prompt/model/list',
                    method: 'post',
                    params,
                    success,
                    fail
                }),
                reducers: [
                    startReducer,
                    (state, action) => {
                        const strategy_models = get(action, 'data.promptList', []).map((item) => {
                            const id = Object.keys(item).find((key) => key !== 'type');
                            return {
                                id,
                                mode: 'strategy',
                                name: item[id],
                                type: item.type || 0
                            };
                        });
                        const lab_models = get(action, 'data.labPromptList', []).map((item) => {
                            const id = Object.keys(item)[0];
                            return {
                                id,
                                mode: 'lab',
                                name: item[id]
                            };
                        });
                        const models = [...strategy_models, ...lab_models];
                        state.currentPrompt = getCacheIntelligentGeneratePrompt(models);
                        state.promptList = models;
                        return { ...state };
                    },
                    failReducer
                ]
            }
        ]
    },
    currentPrompt: {
        initialValue: null,
        syncOptions: [
            {
                setCurrentPrompt: (prompt) => ({ prompt }),
                reducer: (state, action) => {
                    let cacheIntelligentGeneratePrompt;
                    try {
                        cacheIntelligentGeneratePrompt = JSON.parse(
                            localStorage.getItem('cacheIntelligentGeneratePrompt') || '{}'
                        );
                    } catch (e) {
                        cacheIntelligentGeneratePrompt = {};
                    }
                    localStorage.setItem(
                        'cacheIntelligentGeneratePrompt',
                        JSON.stringify({
                            ...cacheIntelligentGeneratePrompt,
                            [state.currentSpace?.id]: action.params.prompt?.id
                        })
                    );
                    state.currentPrompt = action.params.prompt;
                    return { ...state };
                }
            }
        ]
    },
    docPromptList: {
        initialValue: [],
        asyncOptions: [
            {
                getDocPromptList: (params, success, fail) => ({
                    url: '/core/prompt/model/list',
                    method: 'post',
                    params,
                    success,
                    fail
                }),
                reducers: [
                    startReducer,
                    (state, action) => {
                        const strategy_models = get(action, 'data.promptList', []).map((item) => {
                            const id = Object.keys(item)[0];
                            return {
                                id,
                                type: 'strategy',
                                name: item[id]
                            };
                        });
                        // const lab_models = get(action, 'data.lab', {}).map(item => {
                        //     const id = Object.keys(item)[0];
                        //     return {
                        //         id,
                        //         type: 'lab',
                        //         name: item[id]
                        //     };
                        // });
                        const lab_models = [];
                        const models = [...strategy_models, ...lab_models];
                        state.currentDocPrompt = getCacheDocPrompt(models);
                        state.docPromptList = models;
                        return { ...state };
                    },
                    failReducer
                ]
            }
        ]
    },
    currentDocPrompt: {
        initialValue: null,
        syncOptions: [
            {
                setCurrentDocPrompt: (prompt) => ({ prompt }),
                reducer: (state, action) => {
                    let cacheDocPrompt;
                    try {
                        cacheDocPrompt = JSON.parse(localStorage.getItem('cacheDocPrompt') || '{}');
                    } catch (e) {
                        cacheDocPrompt = {};
                    }
                    localStorage.setItem(
                        'cacheDocPrompt',
                        JSON.stringify({
                            ...cacheDocPrompt,
                            [state.currentSpace?.id]: action.params.prompt?.id
                        })
                    );
                    state.currentDocPrompt = action.params.prompt;
                    return { ...state };
                }
            }
        ]
    },
    taskList: {
        initialValue: {},
        asyncOptions: [
            {
                getTaskList: (params, success, fail) => ({
                    url: '/base/asyncTask/listTask',
                    method: 'post',
                    params,
                    success,
                    fail
                }),
                reducers: [
                    startReducer,
                    (state, action) => {
                        const tasks = get(action, 'data', {});
                        const lists = tasks?.data?.taskList || tasks.taskList;
                        const pageInfo = tasks?.data?.pageInfo || tasks.pageInfo;
                        state.taskList = {
                            lists,
                            pageInfo
                        };
                        return { ...state, action };
                    },
                    failReducer
                ]
            }
        ],
        syncOptions: [
            {
                setTaskList: (taskList) => ({ taskList }),
                reducer: (state, action) => {
                    state.taskList = action.params.taskList;
                    return { ...state };
                }
            }
        ]
    },
    tagList: {
        initialValue: [],
        asyncOptions: [
            {
                getTagList: (params, success, fail) => ({
                    url: '/base/tag/listTag',
                    method: 'post',
                    params,
                    success,
                    fail
                }),
                reducers: [
                    startReducer,
                    (state, action) => {
                        const tags = get(action, 'data', {});
                        const lists = tags?.data?.tagList || tags.tagList;
                        state.tagList = lists;
                        return { ...state, action };
                    },
                    failReducer
                ]
            }
        ],
        syncOptions: [
            {
                setTagList: (tagList) => ({ tagList }),
                reducer: (state, action) => {
                    state.tagList = action.params.tagList;
                    return { ...state };
                }
            }
        ]
    },
    recording: {
        initialValue: false,
        syncOptions: [
            {
                setRecording: (recording) => ({ recording }),
                reducer: (state, action) => {
                    state.recording = action.params.recording;
                    return { ...state };
                }
            }
        ]
    },
    currentDevice: {
        initialValue: null,
        syncOptions: [
            {
                setCurrentDevice: (device) => ({ device }),
                reducer: (state, action) => {
                    state.currentDevice = action.params.device;
                    return { ...state };
                }
            }
        ]
    },
    deviceList: {
        initialValue: {
            iOS: [],
            android: []
        },
        syncOptions: [
            {
                setDeviceList: (deviceList) => ({ deviceList }),
                reducer: (state, action) => {
                    state.deviceList = action.params.deviceList;
                    if (!state.currentDevice) {
                    }
                    return { ...state };
                }
            }
        ]
    },
    ipPort: {
        initialValue: {},
        syncOptions: [
            {
                setIpPort: (ipPort) => ({ ipPort }),
                reducer: (state, action) => {
                    state.ipPort = action.params.ipPort;
                    return { ...state };
                }
            }
        ]
    },
    lazyMindVersion: {
        initialValue: '',
        asyncOptions: [
            {
                getLazyMindLastRelease: () => ({
                    url: '/lazymind/release/last',
                    method: 'get'
                }),
                reducers: [
                    startReducer,
                    (state, action) => {
                        const version = action?.data?.version;
                        return { ...state, lazyMindVersion: version || '' };
                    },
                    failReducer
                ]
            }
        ]
    },
    showModal: {
        initialValue: false,
        syncOptions: [
            {
                setShowModal: (showModal) => ({ showModal }),
                reducer: (state, action) => {
                    state.showModal = action.params.showModal;
                    return { ...state };
                }
            }
        ]
    },
    // 3.0
    bugConfig: {
        initialValue: [],
        syncOptions: [
            {
                setBugConfig: (bugConfig) => ({ bugConfig }),
                reducer: (state, action) => {
                    state.bugConfig = action.params.bugConfig;
                    return { ...state };
                }
            }
        ],
        asyncOptions: [
            {
                getBugConfig: (params, success, fail) => {
                    return {
                        url: '/core/bug/config/query',
                        method: 'post',
                        params: {
                            ...params
                        },
                        success,
                        fail
                    };
                },
                reducers: [
                    presetReducer[0],
                    (state, action) => {
                        const bugConfig = get(action, 'data.bugConfig', []);
                        state.bugConfig = bugConfig?.filter((item) => item.moduleType !== 2);
                        return { ...state };
                    },
                    presetReducer[2]
                ]
            }
        ]
    },
    filterOsType: {
        initialValue: [],
        syncOptions: [
            {
                setFilterOsType: (filterOsType) => ({ filterOsType }),
                reducer: (state, action) => {
                    state.filterOsType = action.params.filterOsType;
                    return { ...state };
                }
            }
        ]
    },
    creationConfig: {
        initialValue: {},
        syncOptions: [
            {
                setCreationConfig: (creationConfig) => ({ creationConfig }),
                reducer: (state, action) => {
                    state.creationConfig = action.params.creationConfig;
                    return { ...state };
                }
            }
        ],
        asyncOptions: [
            {
                getCreationConfig: (params, success, fail) => {
                    return {
                        url: '/core/config/case/creation/query',
                        method: 'post',
                        params: {
                            ...params
                        },
                        success,
                        fail
                    };
                },
                reducers: [
                    presetReducer[0],
                    (state, action) => {
                        const creationConfig = get(action, 'data', []);
                        state.creationConfig = creationConfig;
                        return { ...state };
                    },
                    presetReducer[2]
                ]
            }
        ]
    },
    defaultConfig: {
        initialValue: {},
        syncOptions: [
            {
                setDefaultConfig: (defaultConfig) => ({ defaultConfig }),
                reducer: (state, action) => {
                    state.defaultConfig = action.params.defaultConfig;
                    return { ...state };
                }
            }
        ],
        asyncOptions: [
            {
                getDefaultConfig: (params, success, fail) => {
                    return {
                        url: '/core/config/case/default/query',
                        method: 'post',
                        params: {
                            ...params
                        },
                        success,
                        fail
                    };
                },
                reducers: [
                    presetReducer[0],
                    (state, action) => {
                        const defaultConfig = get(action, 'data', []);
                        state.defaultConfig = defaultConfig;
                        return { ...state };
                    },
                    presetReducer[2]
                ]
            }
        ]
    },
    // error 拦截信息提示
    throwMsg: {
        initialValue: null,
        syncOptions: [
            {
                setThrowMsg: (throwMsg) => ({ throwMsg }),
                reducer: (state, action) => {
                    state.throwMsg = action.params.throwMsg;
                    return { ...state };
                }
            }
        ]
    },
    // error 异常信息提示
    throwNotification: {
        initialValue: null,
        syncOptions: [
            {
                setThrowNotification: (throwNotification) => ({ throwNotification }),
                reducer: (state, action) => {
                    state.throwNotification = action.params.throwNotification;
                    return { ...state };
                }
            }
        ]
    }
});
