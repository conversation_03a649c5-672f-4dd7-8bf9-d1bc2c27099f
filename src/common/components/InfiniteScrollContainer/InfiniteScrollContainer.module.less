.infiniteScrollContainer {
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;
    
    // 滚动条样式
    &::-webkit-scrollbar {
        width: 6px;
    }
    
    &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
        
        &:hover {
            background: #a8a8a8;
        }
    }
}


.loadingIndicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px;
    color: #666;
    
    .loadingText {
        margin-left: 8px;
        font-size: 14px;
    }
}

.noMoreIndicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px;
    
    .noMoreText {
        font-size: 14px;
        color: #999;
    }
}

.errorContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    height: 100%;
    
    .errorMessage {
        font-size: 16px;
        color: #ff4d4f;
        margin-bottom: 16px;
    }
    
    .retryButton {
        padding: 8px 16px;
        background: #1890ff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        
        &:hover {
            background: #40a9ff;
        }
        
        &:active {
            background: #096dd9;
        }
    }
}

.emptyContainer {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    height: 100%;
    
    .emptyMessage {
        font-size: 16px;
        color: #999;
    }
}
