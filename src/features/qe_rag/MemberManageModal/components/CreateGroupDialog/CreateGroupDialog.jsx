import { useState, useEffect, useMemo } from 'react';
import { Modal, Form, Input, message, Select, Tag } from 'antd';
import { createGroup, editGroup, getBuList } from 'COMMON/api/qe_rag/workgroup';
import MemberSelect from 'COMMON/components/Select/MemberSelect';
import commonModel from 'COMMON/models/commonModel';
import { connectModel } from 'COMMON/middleware';
import ragModel from 'COMMON/models/qe_rag/ragModel';
import baseModel from 'COMMON/models/baseModel';
import SingleMemberSelect from 'COMMON/components/Select/SingleMemberSelect';

const CreateGroupDialog = ({
    visible,
    onCancel,
    onSubmitSuccess,
    formData = {},
    isEdit = false,
    ragUsername
}) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [businessOptions, setBusinessOptions] = useState([]);
    const [businessLoading, setBusinessLoading] = useState(false);

    const dialogTitle = useMemo(() => (isEdit ? '编辑群组' : '新建群组'), [isEdit]);

    // 初始化表单数据
    useEffect(() => {
        if (visible) {
            const initialData = {
                name: formData.name || '',
                business: formData.business || undefined,
                managerArray: formData.managerArray || [],
                memberArray: formData.memberArray || [],
                adminArray: formData.adminArray || [ragUsername], // 默认当前用户为管理员
                createUser: formData.createUser || ragUsername
            };
            form.setFieldsValue(initialData);
        }
    }, [visible, formData, form]);

    // 事业群列表
    useEffect(() => {
        const loadBusinessOptions = async () => {
            try {
                setBusinessLoading(true);
                const res = await getBuList();
                const validOptions = (res || []).filter(
                    (item) => item && typeof item === 'string' && item.trim() !== ''
                );
                setBusinessOptions(validOptions);
            } finally {
                setBusinessLoading(false);
            }
        };

        if (visible) {
            loadBusinessOptions();
        }
    }, [visible]);

    const handleSubmit = async () => {
        try {
            const values = await form.validateFields();
            setLoading(true);

            const basicInfo = {
                name: values.name,
                business: values.business,
                manager: values.managerArray,
                createUser: values.createUser || ragUsername
            };

            const users = {
                member: values.memberArray?.join(';') || '',
                admin: values.adminArray?.join(';') || ''
            };

            let res;
            if (isEdit) {
                const payLoadEdit = {
                    workGroup: {
                        id: formData.id,
                        ...basicInfo
                    },
                    ...users
                };
                res = await editGroup(payLoadEdit);
            } else {
                const payload = {
                    workGroup: {
                        ...basicInfo
                    },
                    ...users
                };
                res = await createGroup(payload);
            }

            message.success(res?.message || (isEdit ? '更新成功' : '创建成功'));
            form.resetFields();
            onCancel();
            onSubmitSuccess?.();
        } finally {
            setLoading(false);
        }
    };

    const handleCancel = () => {
        form.resetFields();
        onCancel();
    };

    return (
        <Modal
            title={dialogTitle}
            open={visible}
            onCancel={handleCancel}
            onOk={handleSubmit}
            confirmLoading={loading}
            okText={isEdit ? '更新' : '创建'}
            cancelText="取消"
            width={600}
        >
            <Form form={form} layout="vertical">
                <Form.Item
                    name="name"
                    label="名称"
                    rules={[{ required: true, message: '请输入名称' }]}
                >
                    <Input placeholder="请群组名称" />
                </Form.Item>

                {isEdit && formData?.createUser && (
                    <Form.Item label="创建者">
                        <Tag color="blue">{formData.createUser}</Tag>
                    </Form.Item>
                )}

                <Form.Item
                    name="business"
                    label="事业群"
                    rules={[{ required: true, message: '请输入事业群名称' }]}
                >
                    <Select
                        placeholder="请输入事业群名称"
                        loading={businessLoading}
                        showSearch
                        filterOption={(input, option) =>
                            option?.children?.toLowerCase().indexOf(input.toLowerCase()) >= 0
                        }
                        allowClear
                    >
                        {businessOptions?.map((item) => (
                            <Select.Option key={item} value={item}>
                                {item}
                            </Select.Option>
                        ))}
                    </Select>
                </Form.Item>

                <Form.Item
                    name="managerArray"
                    label="经理"
                    rules={[{ required: true, message: '请填写经理，输入用户名搜索' }]}
                >
                    <SingleMemberSelect placeholder="请填写经理，输入用户名搜索" />
                </Form.Item>

                <Form.Item name="memberArray" label="成员">
                    <MemberSelect placeholder="请填写成员，输入用户名搜索" variant="outlined" />
                </Form.Item>

                <Form.Item name="adminArray" label="管理员">
                    <MemberSelect placeholder="请填写管理员，输入用户名搜索" variant="outlined" />
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default connectModel([baseModel, commonModel, ragModel], (state) => ({
    workGroupList: state.common.rag.workGroupList,
    ragUsername: state.common.base.ragUsername,
    joinedWorkGroupList: state.common.rag.joinedWorkGroupList,
    allWorkGroupList: state.common.rag.allWorkGroupList,
    embeddingModelList: state.common.rag.embeddingModelList
}))(CreateGroupDialog);
