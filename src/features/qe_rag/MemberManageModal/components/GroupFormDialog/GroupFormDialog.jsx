import { useState, useEffect } from 'react';
import { Modal, Form, Input, Select, message, Tag } from 'antd';
import { createGroup, editGroup, getBuList } from 'COMMON/api/qe_rag/workgroup';
import MemberSelect from 'COMMON/components/Select/MemberSelect/MemberSelect';
import SingleMemberSelect from 'COMMON/components/Select/SingleMemberSelect/SingleMemberSelect';

const { Option } = Select;

const GroupFormDialog = ({ visible, onCancel, formData, isEdit, onSubmitSuccess }) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [businessOptions, setBusinessOptions] = useState([]);
    const [businessLoading, setBusinessLoading] = useState(false);

    // 获取事业群列表
    const fetchBusinessOptions = async () => {
        try {
            setBusinessLoading(true);
            const res = await getBuList();
            setBusinessOptions(res.data || []);
        } catch (error) {
            console.error('获取事业群列表失败', error);
        } finally {
            setBusinessLoading(false);
        }
    };

    useEffect(() => {
        if (visible) {
            fetchBusinessOptions();

            if (formData) {
                form.setFieldsValue({
                    name: formData.name,
                    business: formData.business,
                    managerArray: formData.managerArray || [],
                    adminArray: formData.adminArray || [],
                    memberArray: formData.memberArray || []
                });
            }
        }
    }, [visible, formData, form]);

    const handleSubmit = async () => {
        try {
            const values = await form.validateFields();
            setLoading(true);

            const basicInfo = {
                name: values.name,
                business: values.business,
                manager: values.managerArray,
                createUser: formData?.createUser || '',
            };

            const users = {
                member: values.memberArray?.join(';') || '',
                admin: values.adminArray?.join(';') || '',
            };

            let res;
            if (isEdit) {
                const payLoadEdit = {
                    workGroup: {
                        id: formData.id,
                        ...basicInfo,
                    },
                    ...users,
                };
                res = await editGroup(payLoadEdit);
            } else {
                const payload = {
                    workGroup: {
                        ...basicInfo,
                    },
                    ...users,
                };
                res = await createGroup(payload);
            }

            if (res?.code === 200) {
                message.success(res?.message || (isEdit ? '更新成功' : '创建成功'));
                onSubmitSuccess();
                handleCancel(); // 成功时关闭弹窗
            } else {
                message.error(res?.message || '提交失败');
            }
        } catch (error) {
            if (error.errorFields) {
                // 表单验证错误
                return;
            }
            message.error(error?.message || (isEdit ? '编辑失败' : '创建失败'));
        } finally {
            setLoading(false);
        }
    };

    const handleCancel = () => {
        form.resetFields();
        onCancel();
    };

    return (
        <Modal
            title={isEdit ? '编辑群组' : '新增群组1'}
            open={visible}
            onOk={handleSubmit}
            onCancel={handleCancel}
            confirmLoading={loading}
            width={600}
        >
            <Form form={form} layout="vertical" preserve={false}>
                <Form.Item
                    label="工作组名称"
                    name="name"
                    rules={[{ required: true, message: '请输入工作组名称' }]}
                >
                    <Input placeholder="请输入工作组名称" />
                </Form.Item>

                {isEdit && formData?.createUser && (
                    <Form.Item label="创建者">
                        <Tag color="blue">{formData.createUser}</Tag>
                    </Form.Item>
                )}

                <Form.Item
                    label="事业群"
                    name="business"
                    rules={[{ required: true, message: '请选择事业群' }]}
                >
                    <Select
                        placeholder="请选择事业群"
                        loading={businessLoading}
                        showSearch
                        filterOption={(input, option) =>
                            option?.children?.toLowerCase().indexOf(input.toLowerCase()) >= 0
                        }
                    >
                        {businessOptions.map(item => (
                            <Option key={item} value={item}>{item}</Option>
                        ))}
                    </Select>
                </Form.Item>

                <Form.Item
                    label="经理"
                    name="managerArray"
                    rules={[{ required: true, message: '请选择经理' }]}
                >
                    <SingleMemberSelect
                        placeholder="请填写经理，输入用户名搜索"
                        style={{ width: '100%' }}
                    />
                </Form.Item>

                <Form.Item label="成员2312" name="memberArray">
                    <MemberSelect
                        placeholder="请填写成员，输入用户名搜索"
                        style={{ width: '100%' }}
                    />
                </Form.Item>

                <Form.Item label="管理员" name="adminArray">
                    <MemberSelect
                        placeholder="请填写管理员，输入用户名搜索"
                        style={{ width: '100%' }}
                    />
                </Form.Item>

                {isEdit && (formData?.createAt || formData?.updateAt) && (
                    <>
                        {formData?.createAt && (
                            <Form.Item label="创建时间">
                                <Input value={formData.createAt} disabled />
                            </Form.Item>
                        )}
                        {formData?.updateAt && (
                            <Form.Item label="更新时间">
                                <Input value={formData.updateAt} disabled />
                            </Form.Item>
                        )}
                    </>
                )}
            </Form>
        </Modal>
    );
};

export default GroupFormDialog;
