import { useState, useCallback, useRef } from 'react';
import { useNavigate } from 'umi';
import { Card, Input, Empty } from 'antd';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons';
import { getAgentPaging } from 'COMMON/api/qe_rag/agent';
import EllipsisTooltip from 'COMMON/components/EllipsisTooltip';
import PageHeader from 'COMMON/components/PageHeader';
import InfiniteScrollContainer from 'COMMON/components/InfiniteScrollContainer';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import ragModel from 'COMMON/models/qe_rag/ragModel';
import { getAvatarColor } from 'FEATURES/qe_rag/utils';
import usePageSize from 'HOOKS/usePageSize';
import styles from './AgentPage.module.less';

const AgentPage = ({ allWorkGroupList }) => {
    const navigate = useNavigate();
    const [activeTab, setActiveTab] = useState('my');
    const [searchKeyword, setSearchKeyword] = useState('');
    const pageSize = usePageSize({
        pageName: '智能体页面'
    });

    const infiniteScrollRef = useRef(null);

    // 获取智能体数据
    const fetchAgentData = useCallback(
        async (params) => {
            const queryParams = {
                type: activeTab === 'my' ? 1 : 0,
                keyword: searchKeyword,
                page: params.page,
                size: params.size
            };

            const res = await getAgentPaging(queryParams);

            return {
                items: res?.items || [],
                total: res?.total || 0,
                page: params.page
            };
        },
        [activeTab, searchKeyword]
    );

    const handleSearch = useCallback((value) => {
        setSearchKeyword(value);
        if (infiniteScrollRef.current) {
            infiniteScrollRef.current.refresh();
        }
    }, []);

    const handleTabChange = useCallback((key) => {
        setActiveTab(key);
        if (infiniteScrollRef.current) {
            infiniteScrollRef.current.refresh();
        }
    }, []);

    const handleCreateAgent = () => {
        navigate('/qe_rag/agent/edit');
    };

    const handleEditAgent = (agentId) => {
        navigate(`/qe_rag/agent/edit?id=${agentId}`);
    };

    // 格式化工作组信息
    const formatGroupInfo = (groupId) => {
        if (!groupId) {
            return '默认工作组';
        }

        const numericGroupId = Number(groupId);
        const group = allWorkGroupList.find((g) => g.id === numericGroupId);
        return group ? group.name : `工作组 ${groupId}`;
    };

    const renderAgentCard = useCallback(
        (agent, _index, _actions) => {
            const imageUrl = agent.url;

            return (
                <Card
                    key={agent.id}
                    className={styles.agentCard}
                    hoverable
                    onClick={() => handleEditAgent(agent.id)}
                >
                    <div className={styles.cardContent}>
                        {/* 卡片头部 */}
                        <div className={styles.cardHeader}>
                            <div
                                className={styles.cardCover}
                                style={{
                                    background: imageUrl
                                        ? 'transparent'
                                        : getAvatarColor(agent.name)
                                }}
                            >
                                {imageUrl ? (
                                    <img src={imageUrl} alt={agent.name} />
                                ) : (
                                    <span className={styles.iconText}>
                                        {agent.name?.charAt(0) || 'A'}
                                    </span>
                                )}
                            </div>
                            <div className={styles.cardTitle}>
                                <h3 className={styles.agentName}>{agent.name}</h3>
                                <div className={styles.cardId}>ID: {agent.id}</div>

                                {/* 智能体描述 */}
                                <div className={styles.agentDesc}>
                                    <EllipsisTooltip
                                        text={agent.description || '暂无描述'}
                                        maxWidth={120}
                                        maxLines={2}
                                    />
                                </div>
                            </div>
                        </div>

                        {/* 底部信息 */}
                        <div className={styles.cardFooter}>
                            <div className={styles.tags}>
                                <span className={styles.tag}>
                                    <span className={styles.groupId}>#{agent.groupId}</span>
                                    {formatGroupInfo(agent.groupId)}
                                </span>
                            </div>
                            <div className={styles.createTime}>{agent.createAt}</div>
                        </div>
                    </div>
                </Card>
            );
        },
        [allWorkGroupList, handleEditAgent]
    );

    return (
        <div className={styles.container}>
            {/* 固定头部区域 */}
            <div className={styles.fixedHeader}>
                {/* 页面头部 */}
                <PageHeader
                    title="智能体"
                    tabs={[
                        { key: 'my', label: '我发布的' },
                        { key: 'all', label: '全部' }
                    ]}
                    activeTab={activeTab}
                    onTabChange={handleTabChange}
                    actionButton={{
                        text: '新建智能体',
                        icon: <PlusOutlined />,
                        onClick: handleCreateAgent
                    }}
                />

                {/* 搜索栏 */}
                <div className={styles.searchBar}>
                    <Input
                        placeholder="搜索智能体名称..."
                        prefix={<SearchOutlined />}
                        value={searchKeyword}
                        onChange={(e) => setSearchKeyword(e.target.value)}
                        onPressEnter={() => handleSearch(searchKeyword)}
                        className={styles.searchInput}
                    />
                </div>
            </div>

            {/* 无限滚动内容区域 */}
            <InfiniteScrollContainer
                ref={infiniteScrollRef}
                fetchData={fetchAgentData}
                renderItem={(agent, index, actions) => renderAgentCard(agent, index, actions)}
                scrollOptions={{
                    pageSize,
                    initialParams: {},
                    threshold: 100
                }}
                className={styles.scrollableContent}
                gridClassName={styles.cardGrid}
                renderEmpty={() => (
                    <div className={styles.emptyContainer}>
                        <Empty description="暂无智能体数据" />
                    </div>
                )}
                loadingText="加载中..."
                noMoreText={'没有更多智能体了'}
            />
        </div>
    );
};

export default connectModel([baseModel, commonModel, ragModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    workGroupList: state.common.rag.workGroupList,
    ragUsername: state.common.base.ragUsername,
    joinedWorkGroupList: state.common.rag.joinedWorkGroupList,
    allWorkGroupList: state.common.rag.allWorkGroupList,
    embeddingModelList: state.common.rag.embeddingModelList
}))(AgentPage);
