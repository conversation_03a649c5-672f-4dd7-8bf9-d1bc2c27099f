import { useState, useCallback, useRef } from 'react';
import { Input, Button, Card, message, Dropdown, Select, Modal, Empty } from 'antd';
import {
    SearchOutlined,
    PlusOutlined,
    EditOutlined,
    DeleteOutlined,
    MoreOutlined
} from '@ant-design/icons';
import { useNavigate } from 'umi';
import { getBooks, remove } from 'COMMON/api/qe_rag/book';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import ragModel from 'COMMON/models/qe_rag/ragModel';
import PageHeader from 'COMMON/components/PageHeader';
import EllipsisTooltip from 'COMMON/components/EllipsisTooltip';
import InfiniteScrollContainer from 'COMMON/components/InfiniteScrollContainer';
import KnowledgeFormModal from './components/KnowledgeFormModal';
import { getAvatarColor } from 'FEATURES/qe_rag/utils';
import usePageSize from 'HOOKS/usePageSize';
import commonStyles from 'FEATURES/qe_rag/common.module.less';

import styles from './KnowledgePage.module.less';

const KnowledgePage = ({ allWorkGroupList, joinedWorkGroupList, ragUsername }) => {
    const navigate = useNavigate();
    const pageSize = usePageSize({
        pageName: '知识库页面'
    });
    const { Option } = Select;

    // 状态管理
    const [modalVisible, setModalVisible] = useState(false);
    const [editingBook, setEditingBook] = useState(null);
    const [activeTab, setActiveTab] = useState('my'); // 'my' 或 'all'
    const [searchParams, setSearchParams] = useState({
        name: '',
        owner: ragUsername,
        groupId: ''
    });

    // 无限滚动组件的引用
    const infiniteScrollRef = useRef(null);

    // 获取知识库列表的函数，适配无限滚动
    const fetchBooksData = useCallback(async (params) => {
        const response = await getBooks(params);

        // 根据实际API返回格式调整数据结构
        return {
            items: response?.data?.items || response?.items || [],
            total: response?.data?.total || response?.total || 0,
            page: response?.data?.page || response?.page || params?.page || 1
        };
    }, []);

    // 打开新建/编辑模态框
    const handleOpenModal = (book = null) => {
        setEditingBook(book);
        setModalVisible(true);
    };

    // 关闭模态框
    const handleCloseModal = () => {
        setModalVisible(false);
        setEditingBook(null);
    };

    // 模态框保存成功后的回调
    const handleModalSuccess = useCallback(() => {
        // 刷新无限滚动数据
        if (infiniteScrollRef.current) {
            infiniteScrollRef.current.refresh();
        }
        handleCloseModal();
    }, []);

    // 搜索处理
    const handleSearch = useCallback(
        (values) => {
            const newParams = {
                ...searchParams,
                ...values
            };
            setSearchParams(newParams);
            // 更新无限滚动组件的参数并刷新数据
            if (infiniteScrollRef.current) {
                infiniteScrollRef.current.updateParams(newParams);
            }
        },
        [searchParams]
    );

    // 标签切换处理
    const handleTabChange = useCallback(
        (tab) => {
            setActiveTab(tab);
            const newParams = {
                ...searchParams,
                // 根据标签类型调整查询参数
                owner: tab === 'my' ? ragUsername : '',
                groupId: '' // 切换tab时重置工作组筛选
            };
            setSearchParams(newParams);
            // 更新无限滚动组件的参数并刷新数据
            if (infiniteScrollRef.current) {
                infiniteScrollRef.current.updateParams(newParams);
            }
        },
        [searchParams, ragUsername]
    );

    // 删除知识库
    const handleDelete = useCallback(
        async (id, refreshData) => {
            await remove({
                id,
                owner: ragUsername
            });

            message.success('删除成功');
            // 刷新数据
            if (refreshData) {
                refreshData();
            }
        },
        [ragUsername]
    );

    // 查看文档
    const handleViewDocs = (id) => {
        navigate(`/qe_rag/knowledge/docs?knowledgeId=${id}`);
    };

    // 检查是否为管理员
    const isAdmin = (admins) => {
        return ragUsername && admins?.includes(ragUsername);
    };

    // 渲染单个知识库卡片
    const renderKnowledgeCard = useCallback(
        (book, _index, { refresh: refreshData }) => {
            // 根据 groupId 从 allWorkGroupList 中找到对应的工作组名称
            const workGroup = allWorkGroupList?.find((group) => group.id === book.groupId);
            const groupName = workGroup?.name || '默认工作组';

            // 创建下拉菜单项
            const menuItems = [
                {
                    key: 'edit',
                    label: '编辑',
                    icon: <EditOutlined />,
                    disabled: !isAdmin(book.owner),
                    onClick: () => handleOpenModal(book)
                },
                {
                    key: 'delete',
                    label: '删除',
                    icon: <DeleteOutlined />,
                    disabled: !isAdmin(book.owner),
                    danger: true,
                    onClick: () => {
                        Modal.confirm({
                            title: '确认删除',
                            content: '确认要删除该知识库吗？删除后无法恢复。',
                            okText: '确认',
                            cancelText: '取消',
                            okType: 'danger',
                            onOk: () => handleDelete(book.id, refreshData)
                        });
                    }
                }
            ];

            return (
                <Card
                    key={book.id}
                    className={styles.knowledgeCard}
                    hoverable
                    onClick={() => handleViewDocs(book.id)}
                >
                    <div className={styles.cardContent}>
                        {/* 卡片头部 */}
                        <div className={styles.cardHeader}>
                            <div
                                className={styles.cardIcon}
                                style={{ background: getAvatarColor(book.name) }}
                            >
                                <span className={styles.iconText}>
                                    {book.name?.charAt(0) || 'K'}
                                </span>
                            </div>
                            <div className={styles.cardTitle}>
                                <h3 className={styles.knowledgeName}>{book.name}</h3>
                                <div className={styles.cardId}>ID: {book.id}</div>
                            </div>
                            <div onClick={(e) => e.stopPropagation()}>
                                <Dropdown
                                    menu={{ items: menuItems }}
                                    trigger={['hover']}
                                    placement="bottomRight"
                                >
                                    <Button
                                        type="text"
                                        icon={<MoreOutlined />}
                                        className={styles.moreButton}
                                    />
                                </Dropdown>
                            </div>
                        </div>

                        {/* 知识库描述 */}
                        <EllipsisTooltip
                            text={book.description}
                            style={{
                                margin: '10px 0',
                                fontSize: '12px',
                                color: '#666'
                            }}
                        />

                        {/* 底部信息 */}
                        <div className={styles.cardFooter}>
                            <div className={styles.tags}>
                                <span className={styles.tag}>
                                    <span className={styles.groupId}># {book.groupId}</span>
                                    {groupName}
                                </span>
                            </div>
                            <div className={styles.updateTime}>
                                {book.updateAt || book.createAt}
                            </div>
                        </div>
                    </div>
                </Card>
            );
        },
        [allWorkGroupList, ragUsername, handleDelete, handleOpenModal, handleViewDocs]
    );

    return (
        <div className={styles.container}>
            {/* 固定头部区域 */}
            <div className={styles.fixedHeader}>
                {/* 页面头部 */}
                <PageHeader
                    title="知识库"
                    tabs={[
                        { key: 'my', label: '我的知识库' },
                        { key: 'all', label: '全部知识库' }
                    ]}
                    activeTab={activeTab}
                    onTabChange={handleTabChange}
                    actionButton={{
                        text: '新建知识库',
                        icon: <PlusOutlined />,
                        onClick: () => handleOpenModal()
                    }}
                />

                {/* 搜索栏 */}
                <div className={styles.searchBar}>
                    <Input
                        placeholder="搜索知识库名称..."
                        prefix={<SearchOutlined />}
                        value={searchParams.name}
                        onChange={(e) => setSearchParams({ ...searchParams, name: e.target.value })}
                        onPressEnter={() => handleSearch(searchParams)}
                        className={styles.searchInput}
                    />
                    <Select
                        placeholder={'请选择工作组'}
                        value={searchParams.groupId || undefined}
                        onChange={(value) => {
                            const newParams = {
                                ...searchParams,
                                groupId: value || ''
                            };
                            setSearchParams(newParams);
                            handleSearch(newParams);
                        }}
                        allowClear
                        className={styles.workGroupSelect}
                        style={{ width: 240 }}
                    >
                        {(activeTab === 'my' ? joinedWorkGroupList : allWorkGroupList)?.map(
                            (group) => (
                                <Option key={group.id} value={group.id}>
                                    <span className={commonStyles.groupId}>#{group.id}</span>
                                    {group.name}
                                </Option>
                            )
                        )}
                    </Select>
                </div>
            </div>

            {/* 无限滚动内容区域 */}
            <InfiniteScrollContainer
                ref={infiniteScrollRef}
                fetchData={fetchBooksData}
                renderItem={(book, index, actions) => renderKnowledgeCard(book, index, actions)}
                scrollOptions={{
                    pageSize,
                    initialParams: searchParams,
                    threshold: 100
                }}
                className={styles.scrollableContent}
                gridClassName={styles.cardGrid}
                renderEmpty={() => (
                    <div className={styles.emptyContainer}>
                        <Empty description="暂无知识库数据" />
                    </div>
                )}
                loadingText="加载中..."
                noMoreText="没有更多知识库了"
            />

            {/* 新建/编辑知识库弹窗 */}
            <KnowledgeFormModal
                visible={modalVisible}
                onCancel={handleCloseModal}
                editingBook={editingBook}
                onSuccess={handleModalSuccess}
                joinedWorkGroupList={joinedWorkGroupList || []}
            />
        </div>
    );
};

export default connectModel([baseModel, commonModel, ragModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    workGroupList: state.common.rag.workGroupList,
    ragUsername: state.common.base.ragUsername,
    joinedWorkGroupList: state.common.rag.joinedWorkGroupList,
    allWorkGroupList: state.common.rag.allWorkGroupList,
    embeddingModelList: state.common.rag.embeddingModelList
}))(KnowledgePage);
