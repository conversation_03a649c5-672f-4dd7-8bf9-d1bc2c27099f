import React, { useState, useEffect, useCallback, useRef } from 'react';
import { cloneDeep, merge, isEmpty } from 'lodash';
import { useNavigate } from 'umi';
import { Row, Col, Tag, message } from 'antd';
import { stringifyUrl } from 'query-string';
import { useDebounceFn } from 'HOOKS/useDebounceFn';
import { connectModel } from 'COMMON/middleware';
import { getQueryParams } from 'COMMON/utils/utils';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import ragModel from 'COMMON/models/qe_rag/ragModel';

import {
    getDocuments,
    deleteDocument,
    updateDocumentStatus,
    updateDocument,
    getDocumentDetail
} from 'COMMON/api/qe_rag/document';
import { getBook } from 'COMMON/api/qe_rag/book';
import KnowledgeBaseHeader from './components/KnowledgeBaseHeader/KnowledgeBaseHeader';
import DocumentList from './components/DocumentList/DocumentList';
import DocumentDetailPanel from './components/DocumentDetailPanel/DocumentDetailPanel';
import AddDocumentModal from './components/AddDocumentModal/AddDocumentModal';
import {
    DOCUMENT_STATUSES,
    DEFAULT_PAGE_SIZE,
    DEFAULT_CURRENT_PAGE,
    DEFAULT_FILTERS,
    DEFAULT_FILTER_QUERY
} from './const';
import styles from './DocPage.module.less';
const DocumentManagePage = ({
    statuses = DOCUMENT_STATUSES,
    onRouteChange,
    allWorkGroupList,
    ragUsername
}) => {
    const [filters, setFilters] = useState(DEFAULT_FILTERS);
    const [selectedDoc, setSelectedDoc] = useState(null);
    const [searchText, setSearchText] = useState('');
    const [addModalVisible, setAddModalVisible] = useState(false);
    const [shouldSelectFirst, setShouldSelectFirst] = useState(false);

    // 数据状态管理
    const [items, setItems] = useState([]);
    const [total, setTotal] = useState(0);
    const [loading, setLoading] = useState(false);
    const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
    const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
    const [searchQuery, setSearchQuery] = useState('');
    const [filterQuery, setFilterQuery] = useState(DEFAULT_FILTER_QUERY);

    // 动态分页相关状态和ref
    const listContainerRef = useRef(null);
    const [documentDetail, setDocumentDetail] = useState(null);
    const [detailLoading, setDetailLoading] = useState(false);
    const [editLoading, setEditLoading] = useState(false);

    // 知识库默认值状态
    const [knowledgeBaseDefaults, setKnowledgeBaseDefaults] = useState(null);
    const [defaultsLoading, setDefaultsLoading] = useState(false);

    // 切片分页状态
    const [chunkPagination, setChunkPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0
    });

    const query = getQueryParams();
    const navigate = useNavigate();

    // 计算当前页显示的切片数据
    const getCurrentChunks = () => {
        if (!documentDetail?.contentList) {
            return [];
        }
        const { current, pageSize } = chunkPagination;
        const startIndex = (current - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        return documentDetail.contentList.slice(startIndex, endIndex);
    };

    // 处理切片分页变化
    const handleChunkPageChange = (page, size) => {
        setChunkPagination((prev) => ({
            ...prev,
            current: page,
            pageSize: size || prev.pageSize
        }));
    };

    useEffect(() => {
        if (documentDetail?.contentList) {
            setChunkPagination((prev) => ({
                ...prev,
                total: documentDetail.contentList.length,
                current: 1
            }));
        }
    }, [documentDetail?.contentList]);

    const fetchDocuments = useCallback(
        async (overrideParams = {}) => {
            setLoading(true);
            try {
                const queryParams = {
                    title: filterQuery.title || searchQuery,
                    owner: filterQuery.owner,
                    status: filterQuery.status,
                    id: filterQuery.id,
                    knowledgeBaseId: query?.knowledgeId || 64,
                    page: currentPage,
                    size: pageSize,
                    ...overrideParams
                };
                const result = await getDocuments(queryParams);

                setItems(result.items || []);
                setTotal(result.total || 0);
            } finally {
                setLoading(false);
            }
        },
        [searchQuery, filterQuery, currentPage, pageSize, knowledgeBaseDefaults?.id]
    );

    // 获取知识库默认值
    const fetchKnowledgeBaseDefaults = useCallback(async () => {
        if (!query?.knowledgeId) {
            return;
        }
        setDefaultsLoading(true);
        try {
            const res = await getBook(query?.knowledgeId);
            const embeddingRule = JSON.parse(res.embeddingRule || '{}');
            const defaults = {
                ...res,
                parseId: res.parseId || 1,
                delimiter: embeddingRule.delimiter || ['。', '！'],
                chunkTokenNum: embeddingRule.chunkTokenNum || 600,
                embeddingModelId: res.embeddingModelId,
                cronOpen: false,
                cronExpression: ''
            };

            setKnowledgeBaseDefaults(defaults);
        } finally {
            setDefaultsLoading(false);
        }
    }, [query?.knowledgeId]);

    useEffect(() => {
        fetchDocuments();
        fetchKnowledgeBaseDefaults();
    }, [fetchDocuments, fetchKnowledgeBaseDefaults]);

    useEffect(() => {
        fetchDocuments();
    }, [currentPage, pageSize, searchQuery, filterQuery]);

    // 根据路由中的docId和items变化来处理文档选中
    useEffect(() => {
        if (!items || items.length === 0) {
            setSelectedDoc(null);
            return;
        }

        if (query?.docId) {
            const targetDoc = items.find((item) => item.id === +query?.docId);
            if (targetDoc) {
                if (selectedDoc?.id !== targetDoc.id) {
                    setSelectedDoc(targetDoc);
                    fetchDocumentDetail(targetDoc.id);
                }
                return;
            }
        }

        if (shouldSelectFirst) {
            handleDocumentSelect(items[0]);
            setShouldSelectFirst(false);
            return;
        }

        if (!query?.docId) {
            setSelectedDoc(null);
        }
    }, [items, query?.docId, shouldSelectFirst]);

    // 防抖搜索
    const { run: debouncedSearch } = useDebounceFn(
        (searchValue) => {
            setSearchQuery(searchValue);
            setCurrentPage(1);
        },
        { wait: 500 }
    );

    useEffect(() => {
        debouncedSearch(searchText);
    }, [searchText, debouncedSearch]);

    // const updateFilter = useCallback((key, value) => {
    //     setFilters((prev) => merge(cloneDeep(prev), { [key]: value }));
    // }, []);
    const updateFilter = (key, value) => {
        setFilters((prev) => ({ ...prev, [key]: value }));
    };

    // 重置筛选条件
    const resetFilters = useCallback(() => {
        setFilters(cloneDeep(DEFAULT_FILTERS));
        setFilterQuery(cloneDeep(DEFAULT_FILTER_QUERY));
        setCurrentPage(1);
    }, []);

    // 检查是否有筛选条件被选中
    const hasActiveFilters = () => {
        const activeFilters = {
            ...filterQuery,
            ...filters
        };
        return !isEmpty(
            Object.values(activeFilters).filter(
                (value) => value !== '' && value !== null && value !== undefined
            )
        );
    };

    // 标签
    const getStatusTag = (status, _isSelected = false) => {
        const statusInfo = statuses.find((item) => item.key === status);
        const statusText = statusInfo ? statusInfo.value : '';

        switch (status) {
            case 1:
            case 5:
                return <Tag color="orange">{statusText}</Tag>;
            case 2:
                return <Tag color="green">{statusText}</Tag>;
            case 3:
                return <Tag color="red">{statusText}</Tag>;
            case 4:
                return <Tag color="default">{statusText}</Tag>;
            default:
                return <Tag>{statusText}</Tag>;
        }
    };

    // 处理文档详情保存
    const handleDocumentSave = async (updateData) => {
        try {
            setEditLoading(true);
            await updateDocument(updateData);

            message.success('文档更新成功');
            // 重新获取文档详情
            await fetchDocumentDetail(documentDetail.id);
            await fetchDocuments();
        } finally {
            setEditLoading(false);
        }
    };

    // 获取文档详情
    const fetchDocumentDetail = useCallback(async (docId) => {
        setDetailLoading(true);
        try {
            const response = await getDocumentDetail({ id: docId });
            setDocumentDetail(response);
        } finally {
            setDetailLoading(false);
        }
    }, []);

    // 处理文档选择
    const handleDocumentSelect = (doc) => {
        if (selectedDoc?.id === doc.id) {
            return;
        }
        setSelectedDoc(doc);
        fetchDocumentDetail(doc.id);
        navigate(
            stringifyUrl({
                url: '/qe_rag/knowledge/docs',
                query: {
                    ...query,
                    docId: doc.id
                }
            })
        );
        onRouteChange?.(doc.id);
    };

    // 处理删除文档
    const handleDeleteDocument = async (docId, docTitle) => {
        const body = {
            id: docId,
            owner: ragUsername
        };
        await deleteDocument(body);

        message.success(`文档 "${docTitle}" 删除成功`);

        if (selectedDoc?.id === docId) {
            setSelectedDoc(null);
            setDocumentDetail(null);

            const { docId, ...otherParams } = query;
            navigate(
                stringifyUrl({
                    url: '/qe_rag/knowledge/docs',
                    query: otherParams
                })
            );
        }

        await fetchDocuments();
    };

    // 处理文档状态更新（禁用/启用）
    const handleUpdateDocumentStatus = async (docId, docTitle, newStatus, actionName) => {
        await updateDocumentStatus({
            id: docId,
            owner: ragUsername,
            status: newStatus
        });
        message.success(`文档 "${docTitle}" ${actionName}成功`);
        await fetchDocuments();
    };

    const handleAddDocumentSuccess = async () => {
        setAddModalVisible(false);
        await fetchDocuments();
        await new Promise((resolve) => setTimeout(resolve, 100));
        setShouldSelectFirst(true);
    };

    const handlePageChange = (page, size) => {
        setCurrentPage(page);
        if (size && size !== pageSize) {
            setPageSize(size);
        }
    };

    // 添加文档
    const handleAddDocument = () => {
        setAddModalVisible(true);
    };

    // 筛选
    const handleApplyFilters = () => {
        setFilterQuery({
            title: filters.documentName || '',
            owner: filters.creator || '',
            status: filters.status || '',
            id: filters.documentId || ''
        });
        setCurrentPage(1);
    };

    return (
        <>
            <div className={styles.mainContainer}>
                <KnowledgeBaseHeader
                    knowledgeBaseDefaults={knowledgeBaseDefaults}
                    allWorkGroupList={allWorkGroupList}
                    onAddDocument={handleAddDocument}
                />

                {/* 主要内容区域 */}
                <Row gutter={8} className={styles.mainContentRow}>
                    {/* 左侧文档列表 */}
                    <Col span={8}>
                        <DocumentList
                            ref={listContainerRef}
                            searchText={searchText}
                            onSearchChange={setSearchText}
                            filters={filters}
                            onFilterChange={updateFilter}
                            onResetFilters={resetFilters}
                            onApplyFilters={handleApplyFilters}
                            hasActiveFilters={hasActiveFilters}
                            statuses={statuses}
                            items={items}
                            loading={loading}
                            selectedDoc={selectedDoc}
                            onDocumentSelect={handleDocumentSelect}
                            currentPage={currentPage}
                            pageSize={pageSize}
                            total={total}
                            onPageChange={handlePageChange}
                            onUpdateDocumentStatus={handleUpdateDocumentStatus}
                            onDeleteDocument={handleDeleteDocument}
                            getStatusTag={getStatusTag}
                        />
                    </Col>

                    {/* 右侧详情面板 */}
                    <Col span={16}>
                        <DocumentDetailPanel
                            selectedDoc={selectedDoc}
                            documentDetail={documentDetail}
                            detailLoading={detailLoading}
                            editLoading={editLoading}
                            chunkPagination={chunkPagination}
                            onChunkPageChange={handleChunkPageChange}
                            getCurrentChunks={getCurrentChunks}
                            onDocumentSave={handleDocumentSave}
                        />
                    </Col>
                </Row>
            </div>

            {/* 添加文档弹窗 */}
            <AddDocumentModal
                visible={addModalVisible}
                onCancel={() => setAddModalVisible(false)}
                onSuccess={handleAddDocumentSuccess}
                loading={loading}
                knowledgeBaseId={knowledgeBaseDefaults?.id}
                defaultValues={knowledgeBaseDefaults}
                defaultsLoading={defaultsLoading}
            />
        </>
    );
};

export default connectModel([baseModel, commonModel, ragModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    workGroupList: state.common.rag.workGroupList,
    ragUsername: state.common.base.ragUsername,
    joinedWorkGroupList: state.common.rag.joinedWorkGroupList,
    allWorkGroupList: state.common.rag.allWorkGroupList,
    embeddingModelList: state.common.rag.embeddingModelList
}))(DocumentManagePage);
