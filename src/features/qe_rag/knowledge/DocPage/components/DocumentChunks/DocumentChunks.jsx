import React from 'react';
import { Space, Pagination, Empty, Typography } from 'antd';
import styles from './DocumentChunks.module.less';

const { Text } = Typography;

const DocumentChunks = ({
    chunks = [],
    pagination,
    onPageChange,
    loading = false
}) => {
    if (!chunks || chunks.length === 0) {
        return <Empty description="暂无切片数据" />;
    }

    return (
        <>
            <div className={styles.chunksListContainer}>
                <Space
                    direction="vertical"
                    size="small"
                    className={styles.chunksSpace}
                >
                    {chunks.map((chunk, index) => {
                        const actualIndex =
                            (pagination.current - 1) * pagination.pageSize + index;
                        return (
                            <div
                                key={actualIndex}
                                className={styles.chunkCard}
                            >
                                <div className={styles.chunkHeader}>
                                    切片 #{actualIndex + 1}
                                </div>
                                <div className={styles.chunkContent}>
                                    {chunk.content}
                                </div>
                            </div>
                        );
                    })}
                </Space>
            </div>

            {/*  只有多页时才显示 */}
            {pagination.total > pagination.pageSize && (
                <div className={styles.chunksPaginationContainer}>
                    <Pagination
                        current={pagination.current}
                        pageSize={pagination.pageSize}
                        total={pagination.total}
                        onChange={onPageChange}
                        simple
                        size="small"
                        showTotal={(total, range) =>
                            `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                        }
                    />
                </div>
            )}
        </>
    );
};

export default DocumentChunks;
