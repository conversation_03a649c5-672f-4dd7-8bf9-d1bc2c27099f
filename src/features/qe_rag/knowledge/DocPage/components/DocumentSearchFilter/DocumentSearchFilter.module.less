.searchFilterContainer {
  display: flex;
  gap: 8px;
  align-items: center;
  width: 100%;
  padding: 4px 4px 4px 0; // 为红点留出空间
  position: relative;
}

.searchContainer {
  flex: 1;
}

.searchInput {
  width: 100%;
}

.filterPopoverContent {
  min-width: 300px;
}

.filterLabel {
  font-size: 14px;
}

.filterRowMargin {
  margin-top: 8px;
}

.filterSelectWidth {
  width: 100%;
}

.filterActions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 16px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.filterIcon {
  cursor: pointer;
  font-size: 16px;
  color: #777777;
}

// 确保 Badge 小红点不被遮挡
:global(.ant-badge-dot) {
  z-index: 9999 !important;
  position: absolute !important;
}


