.documentDetailContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.basicInfoSection {
  flex-shrink: 0;
}

.sectionTitle {
  margin-bottom: 16px;
}

.sectionTitleWithMargin {
  margin: 16px 0;
}

.sectionTitleChunks {
  margin: 16px 0;
  flex-shrink: 0;
}

.descriptionsContainer {
  background-color: #fafafa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
}

.descriptionItem {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.descriptionItemContent {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

// Processing message section
.processingMessageSection {
  margin-bottom: 24px;
  flex-shrink: 0;
}

.processingMessageContent {
  padding: 12px;
  background-color: #fff2e8;
  border: 1px solid #ffd591;
  border-radius: 6px;
  font-size: 14px;
  color: #d46b08;
  white-space: pre-wrap;
  word-break: break-word;
}

.metadataSection {
  margin-bottom: 24px;
  flex-shrink: 0;
}

.metadataContent {
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 6px;
  font-size: 14px;
  color: #666;
  white-space: pre-wrap;
  word-break: break-word;
}

.chunksSection {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
}

.chunksTitleContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.chunksCount {
  font-size: 12px;
  margin-left: 8px;
}
