import { useEffect } from 'react';
import { Outlet, useLocation } from 'umi';
import moment from 'moment';
import 'moment/locale/zh-cn';
import { Layout, message, notification } from 'antd';
import classnames from 'classnames';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import { ErrorNotice } from './components';
import styles from './BasicLayout.module.less';

moment.locale('zh-cn');

function BasicLayout(props) {
    const { getRagUserInfo, throwNotification, throwMsg, className } = props;
    const location = useLocation();
    const [notificationApi, notificationContextHolder] = notification.useNotification();
    const [messageApi, contextHolder] = message.useMessage();

    const isRagRoute = location.pathname.includes('/qe_rag');
    // error 异常信息提示
    useEffect(() => {
        if (throwNotification === null) {
            return;
        }
        let params = {
            duration: 5,
            showProgress: true
        };
        params.description = <ErrorNotice throwNotification={throwNotification} />;
        notificationApi.open(params);
    }, [throwNotification]);

    // error 拦截信息提示
    useEffect(() => {
        if (throwMsg === null) {
            return;
        }
        messageApi.open({
            ...throwMsg,
            type: throwMsg?.type ?? 'error',
            content: throwMsg?.message ? throwMsg?.message + '' : undefined
        });
    }, [throwMsg]);
    const func = async () => {
        if (isRagRoute) {
            try {
                await getRagUserInfo();
            } catch (err) {
                console.error('RAG用户认证失败:', err);
            }
        }
    };

    // web 加载时直接获取
    useEffect(() => {
        func();
    }, []);

    // 监听路由变化，处理RAG认证
    useEffect(() => {
        const handleRagAuth = async () => {
            if (isRagRoute) {
                try {
                    await getRagUserInfo();
                } catch (err) {
                    console.error('路由变化 - RAG用户认证失败:', err);
                }
            }
        };
        handleRagAuth();
    }, [location.pathname]);

    return (
        <div>
            {contextHolder}
            {notificationContextHolder}
            <Layout className={classnames(className, styles.layoutContainer)}>
                <Outlet />
            </Layout>
        </div>
    );
}

export default connectModel([baseModel], (state) => ({
    throwMsg: state.common.base.throwMsg,

    throwNotification: state.common.base.throwNotification,
    ragUserInfo: state.common.base.ragUserInfo
}))(BasicLayout);
